<template>
  <Dialog
    v-if="visible"
    :visible="visible"
    :title="isEdit ? title : '查看评分标准'"
    width="1100px"
    @closed="hide"
  >
    <el-form
      label-suffix=":"
      ref="queryForm"
      label-width="80px"
      style="margin-bottom: 15px"
      class="query-form"
      :model="queryForm"
      :rules="rules"
      inline
    >
      <el-row>
        <el-col :span="24">
          <div class="box-wrap">
            <span class="title">指标分类</span>
            <span class="content">{{ queryForm.classifyName || '--' }}</span>
          </div>
          <!-- <el-form-item label="指标分类">
            {{ queryForm.classifyName || '--' }} -->
          <!-- <el-tooltip
              class="item"
              effect="dark"
              :content="queryForm.classifyName || '--'"
              placement="top-start"
            >
              <div class="ellipsis-wrap">
            {{ queryForm.classifyName || '--' }}
            </div>
            </el-tooltip> -->
          <!-- </el-form-item> -->
        </el-col>
        <el-col :span="24">
          <div class="box-wrap">
            <span class="title">评价指标</span>
            <span class="content">{{ queryForm.evaluateTarget || '--' }}</span>
          </div>
          <!-- <el-form-item label="评价指标">
            {{ queryForm.evaluateTarget || '--' }} -->
          <!-- <el-tooltip
              class="item"
              effect="dark"
              :content="queryForm.evaluateTarget || '--'"
              placement="top-start"
            >
              <div class="ellipsis-wrap">
            {{ queryForm.evaluateTarget || '--' }}
            </div>
            </el-tooltip> -->
          <!-- </el-form-item> -->
        </el-col>
        <!-- <el-col :span="8"
          ><el-form-item label="指标解释">
            {{ queryForm.targetExplain || '--' }}
            <el-tooltip
              class="item"
              effect="dark"
              :content="queryForm.targetExplain || '--'"
              placement="top-start"
            >
              <div class="ellipsis-wrap">
            {{ queryForm.targetExplain || '--' }}
            </div>
            </el-tooltip>
          </el-form-item></el-col
        > -->
      </el-row>
    </el-form>
    <el-form
      ref="form"
      :rules="rules"
      :model="formInfo"
      label-width="200px"
      label-position="right"
      label-suffix="："
    >
      <el-table
        :data="formInfo.memberList"
        border
        stripe
        v-loading="isLoading"
        :header-cell-style="{ backgroundColor: '#fafafa' }"
        style="width: 100%"
      >
        <el-table-column label="序号" type="index" align="center" width="50">
        </el-table-column>
        <el-table-column prop="scoreMethod" header-align="center">
          <template slot="header">
            <span v-if="isEdit" style="color: #f56c6c">*</span> 评分标准
          </template>
          <template slot-scope="scope">
            <el-form-item
              label-width="0"
              v-if="isEdit"
              :prop="'memberList.' + scope.$index + '.scoreMethod'"
              :rules="rules.scoreMethod"
            >
              <el-input
                autosize
                :maxlength="500"
                type="textarea"
                v-model="scope.row.scoreMethod"
                placeholder="请输入评分标准"
              ></el-input>
            </el-form-item>
            <!-- <template v-else>
              <div v-html="scope.row.scoreMethod || '--'"></div>
            </template> -->
            <div style="white-space: pre" v-else>
              <pre
                style="
                  margin: 0;
                  font-family: none;
                  white-space: pre-wrap||pre-line;
                "
                >{{ scope.row.scoreMethod || '--' }}</pre
              >
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" v-if="isEdit" align="center" width="100">
          <template slot-scope="scope">
            <el-button
              type="text"
              @click="handleDelete(scope.$index, scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <el-button v-if="isEdit" class="dynamic-add-btn" @click="handleAdd"
        >新 增</el-button
      >
    </el-form>
    <div slot="footer">
      <el-button type="primary" plain @click="hide">取消</el-button>
      <el-button type="primary" v-if="isEdit" @click="onSubmit"
        >保存
      </el-button>
    </div>
  </Dialog>
</template>
<script>
  import Dialog from '@/components/basic-dialog';
  import { deepClone } from '@/util/util';
  import { hasReferenced } from '@/api/assess-manage';

  const form = {
    memberList: []
  };
  const queryForm = {
    classifyName: undefined,
    evaluateTarget: '',
    targetExplain: ''
  };
  export default {
    components: { Dialog },
    props: {
      indicatorList: { type: Array, require: () => [] },
      isEdit: { type: Boolean, default: false },
      id: { type: String, default: '' }
    },
    data() {
      return {
        visible: false,
        loading: false,
        tableData: [],
        total: 0,
        title: '添加评分标准',
        formInfo: { ...form },
        rules: {
          scoreMethod: [{ required: true, message: '请输入评分标准' }]
        },
        // ------------
        currentIndex: undefined, // 当前编辑行的行号
        selectedList: [],
        isLoading: false,
        selectedRow: {},
        queryForm: { ...queryForm },
        pages: {
          pageNum: 1,
          pageSize: 10
        }
      };
    },
    methods: {
      // 是否被引用
      async hasReferenced(row) {
        try {
          // true 可以删除
          // false 不能删除
          this.isLoading = true;
          let {
            data: { data }
          } = await hasReferenced({
            methodId: row.id
          });
          this.isLoading = false;
          return data || false;
        } catch (error) {
          this.isLoading = false;
          console.log(error);
          return false;
        }
      },
      async handleDelete(index, row) {
        if (row.id) {
          let bool = await this.hasReferenced(row);
          if (!bool)
            return this.$message.warning('该评分标准已被引用，无法删除！');
        }
        this.formInfo.memberList.splice(index, 1);
      },
      handleAdd() {
        let row = {
          scoreMethod: ''
        };
        let arr = this.formInfo.memberList;
        arr.push(row);
      },
      validForm(flag) {
        let bool = false;
        this.$refs[flag].validate((valid) => {
          bool = valid;
        });
        return bool;
      },
      // 提交
      onSubmit() {
        let bool = this.validForm('form');
        if (!bool) return;
        let length = this.formInfo.memberList.length;
        if (!length) return this.$message.warning('请添加评分标准');
        this.$message.success('保存成功');

        let arr = [...this.formInfo.memberList];
        // arr.forEach((item) => {
        //   item.classification = this.queryForm.name;
        // });
        this.$emit('save-success', arr, this.currentIndex);
        this.hide();
      },
      async show(row, index) {
        this.visible = true;
        this.currentIndex = index;
        let obj = deepClone(row);
        this.selectedRow = obj;
        this.setForm(obj);
      },
      setForm(row) {
        for (const key in this.queryForm) {
          this.queryForm[key] = row[key];
        }
        console.log('row', row);
        let historyList = row.list || [];
        if (!historyList.length) {
          return this.handleAdd();
        }
        this.formInfo.memberList = row.list || [];
      },
      resetPage() {
        this.queryForm = { ...queryForm };
        this.formInfo.memberList = [];
        form.memberList = [];
      },
      reset() {
        this.resetPage();
        this.queryText();
      },
      hide() {
        this.resetPage();
        this.visible = false;
      }
    }
  };
</script>
<style lang="scss" scoped>
  .query-form {
    .box-wrap {
      display: grid;
      grid-template-columns: 72px 1fr;

      span.title {
        margin-bottom: 0;
      }

      span.title::after {
        content: '：';
      }

      // span.content {
      // }
    }

    :deep(.ellipsis-wrap) {
      width: 240px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    /deep/.el-form-item--mini.el-form-item {
      display: flex;
    }

    /deep/.el-form-item--mini .el-form-item__content {
      flex: 1;
    }
  }
</style>
