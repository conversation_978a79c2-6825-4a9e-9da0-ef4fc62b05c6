export default {
  name: 'YkTable',
  props: {
    config: {
      type: Object,
      default: () => ({})
    }
  },
  render(h) {
    const scopedSlots = {};
    Object.keys(this.$parent.$scopedSlots).forEach((key) => {
      if (key !== 'default') {
        scopedSlots[key] = this.$parent.$scopedSlots[key];
      }
    });
    return h(
      'el-table',
      {
        ref: 'YkTable',
        key: this.config.key || '',
        class: this.config.className || '',
        attrs: { ...this.config.attrs },
        on: { ...this.config.listeners },
        scopedSlots
      },
      this.config.children
    );
  },
  methods: {
    clearSelection() {
      this.$refs['YkTable'] && this.$refs['YkTable'].clearSelection();
    },
    toggleRowSelection(row, selected) {
      this.$refs['YkTable'] &&
        this.$refs['YkTable'].toggleRowSelection(row, selected);
    },
    toggleAllSelection() {
      this.$refs['YkTable'] && this.$refs['YkTable'].toggleAllSelection();
    },
    toggleRowExpansion(row, expanded) {
      this.$refs['YkTable'] &&
        this.$refs['YkTable'].toggleRowExpansion(row, expanded);
    },
    setCurrentRow(row) {
      this.$refs['YkTable'] && this.$refs['YkTable'].setCurrentRow(row);
    },
    clearSort() {
      this.$refs['YkTable'] && this.$refs['YkTable'].clearSort();
    },
    clearFilter(columnKey) {
      this.$refs['YkTable'] && this.$refs['YkTable'].clearFilter(columnKey);
    },
    doLayout() {
      this.$refs['YkTable'] && this.$refs['YkTable'].doLayout();
    },
    sort(prop, order) {
      this.$refs['YkTable'] && this.$refs['YkTable'].sort(prop, order);
    }
  }
};
