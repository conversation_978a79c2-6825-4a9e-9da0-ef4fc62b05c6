<template>
  <el-dialog
    append-to-body
    fullscreen
    :modal="false"
    :visible="open"
    @close="close"
  >
    <div slot="title" style="height: 35px">
      <strong style="font-size: 18px">{{ title }}</strong>
      <!-- <span class="font-btn">
        <el-button
          size="mini"
          icon="el-icon-plus"
          circle
          style="margin-right: 5px"
          :disabled="addBtn"
          @click="fontAdd"
        ></el-button>
        <strong class="strong-font"> 字号：{{ font || 0 }} </strong>
        <el-button
          size="mini"
          icon="el-icon-minus"
          circle
          :disabled="reduceBtn"
          @click="fontReduce"
        ></el-button>
      </span> -->
    </div>
    <slot></slot>
  </el-dialog>
</template>

<script>
  export default {
    name: 'full-screen-table',
    props: {
      title: {
        type: String,
        default: ''
      },
      open: {
        type: Boolean,
        default: false
      }
    },
    model: {
      prop: 'open',
      event: 'change'
    },
    data() {
      return {};
    },
    methods: {
      close() {
        this.$emit('change', false);
      }
    }
  };
</script>

<style lang="scss" scoped>
  ::v-deep .el-dialog__header {
    padding-top: 10px !important;
  }

  ::v-deep .el-dialog__body {
    padding: 0 20px;
  }
</style>
