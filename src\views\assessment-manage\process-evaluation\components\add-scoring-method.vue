<template>
  <Dialog
    v-if="visible"
    :visible="visible"
    :title="title"
    width="1100px"
    @closed="hide"
  >
    <el-form
      label-suffix=":"
      ref="queryForm"
      label-width="80px"
      class="query-form"
      :model="queryForm"
      :rules="rules"
      inline
    >
      <el-row>
        <el-col :span="8"
          ><el-form-item label="指标分类">
            {{ queryForm.classifyName || '--' }}
            <!-- <el-tooltip
              class="item"
              effect="dark"
              :content="queryForm.classifyName || '--'"
              placement="top-start"
            >
              <div class="ellipsis-wrap">
                {{ queryForm.classifyName || '--' }}
              </div>
            </el-tooltip> -->
          </el-form-item></el-col
        >
        <el-col :span="16"
          ><el-form-item label="评价指标">
            {{ queryForm.evaluateTarget || '--' }}
            <!-- <el-tooltip
              class="item"
              effect="dark"
              :content="queryForm.evaluateTarget || '--'"
              placement="top-start"
            >
              <div class="ellipsis-wrap">
                {{ queryForm.evaluateTarget || '--' }}
              </div>
            </el-tooltip> -->
          </el-form-item></el-col
        >
        <!-- <el-col :span="8"
          ><el-form-item label="指标解释">
            {{ queryForm.targetExplain || '--' }}
            <el-tooltip
              class="item"
              effect="dark"
              :content="queryForm.targetExplain || '--'"
              placement="top-start"
            >
              <div class="ellipsis-wrap">
                {{ queryForm.targetExplain || '--' }}
              </div>
            </el-tooltip>
          </el-form-item></el-col
        > -->
      </el-row>
    </el-form>
    <el-form
      ref="form"
      :rules="rules"
      :model="formInfo"
      label-width="200px"
      label-position="right"
      label-suffix="："
    >
      <el-table
        :data="formInfo.memberList"
        border
        stripe
        :header-cell-style="{ backgroundColor: '#fafafa' }"
        style="width: 100%"
      >
        <el-table-column label="序号" type="index" align="center" width="50">
        </el-table-column>
        <el-table-column prop="scoreMethod" header-align="center">
          <template slot="header">
            <span style="color: #f56c6c">*</span> 评分标准
          </template>
          <template slot-scope="scope">
            <el-form-item
              label-width="0"
              :prop="'memberList.' + scope.$index + '.scoreMethod'"
              :rules="rules.scoreMethod"
            >
              <el-input
                autosize
                :maxlength="500"
                v-if="isEdit"
                type="textarea"
                v-model="scope.row.scoreMethod"
                placeholder="请输入评分标准"
              ></el-input>
              <template v-else>{{ scope.row.scoreMethod || '--' }}</template>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column label="操作" v-if="isEdit" align="center" width="100">
          <template slot-scope="scope">
            <el-button
              :disabled="formInfo.memberList.length == 1"
              type="text"
              @click="handleDelete(scope.$index, scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <el-button v-if="isEdit" class="dynamic-add-btn" @click="handleAdd"
        >新 增</el-button
      >
    </el-form>
    <div slot="footer">
      <el-button type="primary" plain @click="hide">取消</el-button>
      <el-button type="primary" v-if="isEdit" @click="onSubmit"
        >保存
      </el-button>
    </div>
  </Dialog>
</template>
<script>
  import Dialog from '@/components/basic-dialog';
  import { deepClone } from '@/util/util';

  const form = {
    memberList: []
  };
  const queryForm = {
    classifyName: undefined,
    evaluateTarget: '',
    targetExplain: ''
  };
  export default {
    components: { Dialog },
    props: {
      indicatorList: { type: Array, require: () => [] },
      isEdit: { type: Boolean, default: false },
      id: { type: String, default: '' }
    },
    data() {
      return {
        visible: false,
        loading: false,
        tableData: [],
        total: 0,
        title: '添加评分标准',
        formInfo: { ...form },
        rules: {
          scoreMethod: [{ required: true, message: '请输入评分标准' }]
        },
        // ------------
        currentIndex: undefined, // 当前编辑行的行号
        selectedList: [],
        queryForm: { ...queryForm },
        pages: {
          pageNum: 1,
          pageSize: 10
        }
      };
    },
    methods: {
      handleDelete(index) {
        this.formInfo.memberList.splice(index, 1);
      },
      handleAdd() {
        let row = {
          scoreMethod: ''
        };
        let arr = this.formInfo.memberList;
        arr.push(row);
      },
      validForm(flag) {
        let bool = false;
        this.$refs[flag].validate((valid) => {
          bool = valid;
        });
        return bool;
      },
      // 提交
      onSubmit() {
        let bool = this.validForm('form');
        if (!bool) return;
        let length = this.formInfo.memberList.length;
        if (!length) return this.$message.warning('请添加评分标准');
        this.$message.success('保存成功');

        let arr = [...this.formInfo.memberList];
        // arr.forEach((item) => {
        //   item.classification = this.queryForm.name;
        // });
        this.$emit('save-success', arr, this.currentIndex);
        this.hide();
      },
      async show(row, index) {
        this.visible = true;
        this.currentIndex = index;
        let obj = deepClone(row);
        this.setForm(obj);
      },
      setForm(row) {
        for (const key in this.queryForm) {
          this.queryForm[key] = row[key];
        }
        console.log('row', row);
        let historyList = row.list || [];
        if (!historyList.length) {
          return this.handleAdd();
        }
        this.formInfo.memberList = row.list || [];
      },
      resetPage() {
        this.queryForm = { ...queryForm };
        this.formInfo.memberList = [];
        form.memberList = [];
      },
      reset() {
        this.resetPage();
        this.queryText();
      },
      hide() {
        this.resetPage();
        this.visible = false;
      }
    }
  };
</script>
<style lang="scss" scoped>
  .query-form {
    :deep(.ellipsis-wrap) {
      width: 240px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    /deep/.el-form-item--mini.el-form-item {
      display: flex;
    }

    /deep/.el-form-item--mini .el-form-item__content {
      flex: 1;
    }
  }
</style>
