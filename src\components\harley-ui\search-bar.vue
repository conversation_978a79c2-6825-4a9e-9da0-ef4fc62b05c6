<template>
  <div class="h-search-bar">
    <el-form
      @submit.native="$emit('search')"
      :model="model"
      :style="formStyle"
      inline
    >
      <slot></slot>
      <div class="el-form-item button-area">
        <slot name="button-prefix"></slot>
        <el-button @click="$emit('search')" type="primary"> 查询 </el-button>
        <el-button @click="$emit('reset')" type="primary"> 重置 </el-button>
        <slot name="button"></slot>
      </div>
    </el-form>
  </div>
</template>

<script>
  export default {
    name: 'HSearchBar',
    props: {
      model: {
        type: Object,
        required: false
      },
      formStyle: String
    }
  };
</script>

<style lang="scss" scoped>
  .h-search-bar {
    overflow: hidden;

    .el-form {
      .button-area {
        float: right;
        margin-right: 0;
        vertical-align: bottom;
      }
    }
  }
</style>

<style lang="scss">
  .h-search-bar {
    .el-form {
      .el-form-item {
        margin-right: 30px;

        .el-form-item__label {
          width: fit-content !important;
        }

        .el-date-editor--daterange {
          width: 300px;
        }
      }
    }
  }
</style>
