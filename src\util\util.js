import { validatenull } from './validate';
import { Base64 } from 'js-base64';
import { preUrl } from '@/config/env';
// office online 在线预览
export const onlinePreview = (url) => {
  let previewUrl = Base64.encode(url);
  window.open(preUrl + previewUrl);
};
//表单序列化
export const serialize = (data) => {
  let list = [];
  Object.keys(data).forEach((ele) => {
    list.push(`${ele}=${data[ele]}`);
  });
  return list.join('&');
};
export const getObjType = (obj) => {
  var toString = Object.prototype.toString;
  var map = {
    '[object Boolean]': 'boolean',
    '[object Number]': 'number',
    '[object String]': 'string',
    '[object Function]': 'function',
    '[object Array]': 'array',
    '[object Date]': 'date',
    '[object RegExp]': 'regExp',
    '[object Undefined]': 'undefined',
    '[object Null]': 'null',
    '[object Object]': 'object'
  };
  if (obj instanceof Element) {
    return 'element';
  }
  return map[toString.call(obj)];
};
export const getViewDom = () => {
  return window.document
    .getElementById('avue-view')
    .getElementsByClassName('el-scrollbar__wrap')[0];
};
/**
 * 对象深拷贝
 */
export const deepClone = (data) => {
  var type = getObjType(data);
  var obj;
  if (type === 'array') {
    obj = [];
  } else if (type === 'object') {
    obj = {};
  } else {
    //不再具有下一层次
    return data;
  }
  if (type === 'array') {
    for (var i = 0, len = data.length; i < len; i++) {
      obj.push(deepClone(data[i]));
    }
  } else if (type === 'object') {
    for (var key in data) {
      obj[key] = deepClone(data[key]);
    }
  }
  return obj;
};
// 获取文档是否可预览
export const docViewJudge = (type) => {
  let arr = [
    'doc',
    'dot',
    'wps',
    'wpt',
    'docx',
    'dotx',
    'docm',
    'dotm',
    'rtf',
    'xls',
    'xlt',
    'et',
    'xlsx',
    'xltx',
    'csv',
    'xlsm',
    'xltm',
    'ppt',
    'pptx',
    'pptm',
    'ppsx',
    'ppsm',
    'pps',
    'potx',
    'potm',
    'dpt',
    'dps',
    'pdf'
    // 'txt',
  ];
  if (arr.indexOf(type) > -1) {
    return true;
  }
  return false;
};
export const encodeOSSUrl = (url) => {
  let urlArr = url.split('/');
  let lastIndex = urlArr.length - 1;
  urlArr[lastIndex] = encodeURIComponent(urlArr[lastIndex]);
  return urlArr.join('/');
};
// 获取文档icon
export const getDocIcon = (type) => {
  let arrObj = {
    f: '/document/file-PDF-icon.png',
    w: '/document/file-Word-icon.png',
    p: '/document/file-PPT-icon.png',
    s: '/document/file-Excel-icon.png',
    ai: '/document/file-icon-ai.png',

    xls: '/document/file-icon-excel.png',
    xlsx: '/document/file-icon-excel.png',

    gif: '/document/file-icon-gif.png',
    html: '/document/file-icon-html.png',
    jpg: '/document/file-icon-jpg.png',
    mp3: '/document/file-icon-mp3.png',
    pdf: '/document/file-icon-pdf.png',
    png: '/document/file-icon-png.png',

    ppt: '/document/file-icon-ppt.png',
    pptx: '/document/file-icon-ppt.png',

    psd: '/document/file-icon-psd.png',
    // rar: '/document/file-icon-rar.png',
    txt: '/document/file-icon-txt.png',

    word: '/document/file-icon-word.png',
    doc: '/document/file-icon-word.png',
    docx: '/document/file-icon-word.png',

    video: '/document/file-icon-video.png',
    mp4: '/document/file-icon-video.png',
    m2v: '/document/file-icon-video.png',
    mkv: '/document/file-icon-video.png',
    rmvb: '/document/file-icon-video.png',
    wmv: '/document/file-icon-video.png',
    avi: '/document/file-icon-video.png',
    flv: '/document/file-icon-video.png',
    mov: '/document/file-icon-video.png',

    zip: '/document/file-icon-zip.png',
    rar: '/document/file-icon-zip.png'
  };
  if (arrObj[type]) {
    return arrObj[type];
  } else {
    return `/document/file-icon-other.png`;
  }
};
/**
 * 设置灰度模式
 */
export const toggleGrayMode = (status) => {
  if (status) {
    document.body.className = document.body.className + ' grayMode';
  } else {
    document.body.className = document.body.className.replace(' grayMode', '');
  }
};
/**
 * 设置主题
 */
export const setTheme = (name) => {
  document.body.className = name;
};

/**
 * 加密处理
 */
export const encryption = (params) => {
  let { data, type, param, key } = params;
  let result = JSON.parse(JSON.stringify(data));
  if (type == 'Base64') {
    param.forEach((ele) => {
      result[ele] = btoa(result[ele]);
    });
  } else if (type == 'Aes') {
    param.forEach((ele) => {
      result[ele] = window.CryptoJS.AES.encrypt(result[ele], key).toString();
    });
  }
  return result;
};

/**
 * 浏览器判断是否全屏
 */
export const fullscreenToggel = () => {
  if (fullscreenEnable()) {
    exitFullScreen();
  } else {
    reqFullScreen();
  }
};
/**
 * esc监听全屏
 */
export const listenfullscreen = (callback) => {
  function listen() {
    callback();
  }

  document.addEventListener('fullscreenchange', function () {
    listen();
  });
  document.addEventListener('mozfullscreenchange', function () {
    listen();
  });
  document.addEventListener('webkitfullscreenchange', function () {
    listen();
  });
  document.addEventListener('msfullscreenchange', function () {
    listen();
  });
};
/**
 * 浏览器判断是否全屏
 */
export const fullscreenEnable = () => {
  var isFullscreen =
    document.isFullScreen ||
    document.mozIsFullScreen ||
    document.webkitIsFullScreen;
  return isFullscreen;
};

/**
 * 浏览器全屏
 */
export const reqFullScreen = () => {
  if (document.documentElement.requestFullScreen) {
    document.documentElement.requestFullScreen();
  } else if (document.documentElement.webkitRequestFullScreen) {
    document.documentElement.webkitRequestFullScreen();
  } else if (document.documentElement.mozRequestFullScreen) {
    document.documentElement.mozRequestFullScreen();
  }
};
/**
 * 浏览器退出全屏
 */
export const exitFullScreen = () => {
  if (document.documentElement.requestFullScreen) {
    document.exitFullScreen();
  } else if (document.documentElement.webkitRequestFullScreen) {
    document.webkitCancelFullScreen();
  } else if (document.documentElement.mozRequestFullScreen) {
    document.mozCancelFullScreen();
  }
};
/**
 * 递归寻找子类的父类
 */

export const findParent = (menu, id) => {
  for (let i = 0; i < menu.length; i++) {
    if (menu[i].children.length != 0) {
      for (let j = 0; j < menu[i].children.length; j++) {
        if (menu[i].children[j].id == id) {
          return menu[i];
        } else {
          if (menu[i].children[j].children.length != 0) {
            return findParent(menu[i].children[j].children, id);
          }
        }
      }
    }
  }
};
/**
 * 判断2个对象属性和值是否相等
 */

/**
 * 动态插入css
 */

export const loadStyle = (url) => {
  const link = document.createElement('link');
  link.type = 'text/css';
  link.rel = 'stylesheet';
  link.href = url;
  const head = document.getElementsByTagName('head')[0];
  head.appendChild(link);
};
/**
 * 判断路由是否相等
 */
export const diff = (obj1, obj2) => {
  delete obj1.close;
  var o1 = obj1 instanceof Object;
  var o2 = obj2 instanceof Object;
  if (!o1 || !o2) {
    /*  判断不是对象  */
    return obj1 === obj2;
  }

  if (Object.keys(obj1).length !== Object.keys(obj2).length) {
    return false;
    //Object.keys() 返回一个由对象的自身可枚举属性(key值)组成的数组,例如：数组返回下表：let arr = ['a', 'b', 'c'];console.log(Object.keys(arr))->0,1,2;
  }

  for (var attr in obj1) {
    var t1 = obj1[attr] instanceof Object;
    var t2 = obj2[attr] instanceof Object;
    if (t1 && t2) {
      return diff(obj1[attr], obj2[attr]);
    } else if (obj1[attr] !== obj2[attr]) {
      return false;
    }
  }
  return true;
};
/**
 * 根据字典的value显示label
 */
export const findByvalue = (dic, value) => {
  let result = '';
  if (validatenull(dic)) return value;
  if (
    typeof value == 'string' ||
    typeof value == 'number' ||
    typeof value == 'boolean'
  ) {
    let index = 0;
    index = findArray(dic, value);
    if (index != -1) {
      result = dic[index].label;
    } else {
      result = value;
    }
  } else if (value instanceof Array) {
    result = [];
    let index = 0;
    value.forEach((ele) => {
      index = findArray(dic, ele);
      if (index != -1) {
        result.push(dic[index].label);
      } else {
        result.push(value);
      }
    });
    result = result.toString();
  }
  return result;
};
/**
 * 根据字典的value查找对应的index
 */
export const findArray = (dic, value) => {
  for (let i = 0; i < dic.length; i++) {
    if (dic[i].value == value) {
      return i;
    }
  }
  return -1;
};
/**
 * 生成随机len位数字
 */
export const randomLenNum = (len, date) => {
  let random = '';
  random = Math.ceil(Math.random() * 100000000000000)
    .toString()
    .substr(0, len ? len : 4);
  if (date) random = random + Date.now();
  return random;
};
/**
 * 打开小窗口
 */
export const openWindow = (url, title, w, h) => {
  // Fixes dual-screen position                            Most browsers       Firefox
  const dualScreenLeft =
    window.screenLeft !== undefined ? window.screenLeft : screen.left;
  const dualScreenTop =
    window.screenTop !== undefined ? window.screenTop : screen.top;

  const width = window.innerWidth
    ? window.innerWidth
    : document.documentElement.clientWidth
    ? document.documentElement.clientWidth
    : screen.width;
  const height = window.innerHeight
    ? window.innerHeight
    : document.documentElement.clientHeight
    ? document.documentElement.clientHeight
    : screen.height;

  const left = width / 2 - w / 2 + dualScreenLeft;
  const top = height / 2 - h / 2 + dualScreenTop;
  const newWindow = window.open(
    url,
    title,
    'toolbar=no, location=no, directories=no, status=no, menubar=no, scrollbars=no, resizable=yes, copyhistory=no, width=' +
      w +
      ', height=' +
      h +
      ', top=' +
      top +
      ', left=' +
      left
  );

  // Puts focus on the newWindow
  if (window.focus) {
    newWindow.focus();
  }
};

/**
 * 获取顶部地址栏地址
 */
export const getTopUrl = () => {
  return window.location.href.split('/#/')[0];
};

/**
 * 获取url参数
 * @param name 参数名
 */
export const getQueryString = (name) => {
  let reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i');
  let r = window.location.search.substr(1).match(reg);
  if (r != null) return unescape(decodeURI(r[2]));
  return null;
};

/**
 * 下载文件
 * @param {String} path - 文件地址
 * @param {String} name - 文件名,eg: test.png
 */
export const downloadFileBlob = (path, name) => {
  const xhr = new XMLHttpRequest();
  xhr.open('get', path);
  xhr.responseType = 'blob';
  xhr.send();
  xhr.onload = function () {
    if (this.status === 200 || this.status === 304) {
      // 如果是IE10及以上，不支持download属性，采用msSaveOrOpenBlob方法，但是IE10以下也不支持msSaveOrOpenBlob
      if ('msSaveOrOpenBlob' in navigator) {
        navigator.msSaveOrOpenBlob(this.response, name);
        return;
      }
      const url = URL.createObjectURL(this.response);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = name;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }
  };
};

/**
 * 下载文件
 * @param {String} path - 文件地址
 * @param {String} name - 文件名,eg: test.png
 */
export const downloadFileBase64 = (path, name) => {
  const xhr = new XMLHttpRequest();
  xhr.open('get', path);
  xhr.responseType = 'blob';
  xhr.send();
  xhr.onload = function () {
    if (this.status === 200 || this.status === 304) {
      const fileReader = new FileReader();
      fileReader.readAsDataURL(this.response);
      fileReader.onload = function () {
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = this.result;
        a.download = name;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
      };
    }
  };
};
// 数据合并
export function mergeRecursive(source, target) {
  for (var p in target) {
    try {
      if (target[p].constructor == Object) {
        source[p] = mergeRecursive(source[p], target[p]);
      } else {
        source[p] = target[p];
      }
    } catch (e) {
      source[p] = target[p];
    }
  }
  return source;
}

/**
 * 下载excel
 * @param {blob} fileArrayBuffer 文件流
 * @param {String} filename 文件名称
 */
export const downloadXls = (fileArrayBuffer, filename) => {
  let data = new Blob([fileArrayBuffer], {
    type: 'application/vnd.ms-excel,charset=utf-8'
  });
  if (typeof window.chrome !== 'undefined') {
    // Chrome
    var link = document.createElement('a');
    link.href = window.URL.createObjectURL(data);
    link.download = filename;
    link.click();
  } else if (typeof window.navigator.msSaveBlob !== 'undefined') {
    // IE
    var blob = new Blob([data], { type: 'application/force-download' });
    window.navigator.msSaveBlob(blob, filename);
  } else {
    // Firefox
    var file = new File([data], filename, {
      type: 'application/force-download'
    });
    window.open(URL.createObjectURL(file));
  }
};
/**
 * 下载zip
 * @param {blob} fileArrayBuffer 文件流
 * @param {String} filename 文件名称
 */
export const downloadZip = (fileArrayBuffer, filename) => {
  let data = new Blob([fileArrayBuffer], {
    type: 'application/zip,charset=utf-8'
  });
  if (typeof window.chrome !== 'undefined') {
    // Chrome
    var link = document.createElement('a');
    link.href = window.URL.createObjectURL(data);
    link.download = filename;
    link.click();
  } else if (typeof window.navigator.msSaveBlob !== 'undefined') {
    // IE
    var blob = new Blob([data], { type: 'application/force-download' });
    window.navigator.msSaveBlob(blob, filename);
  } else {
    // Firefox
    var file = new File([data], filename, {
      type: 'application/force-download'
    });
    window.open(URL.createObjectURL(file));
  }
};
// 树转列表
export const treeToList = (tree, childrenKey = 'children') => {
  let list = [];
  tree.forEach((item) => {
    list.push(item);
    if (item[childrenKey] && item[childrenKey].length) {
      list = list.concat(treeToList(item[childrenKey], childrenKey));
    }
  });
  return list;
};
// 金额小写转大写
export const numToCny = (n) => {
  let unit = '仟佰拾亿仟佰拾万仟佰拾圆角分',
    str = '';
  n += '00';
  let p = n.indexOf('.');
  if (p >= 0) {
    n = n.substring(0, p) + n.substr(p + 1, 2);
  }
  unit = unit.substr(unit.length - n.length);
  for (let i = 0; i < n.length; i++) {
    str += '零壹贰叁肆伍陆柒捌玖'.charAt(n.charAt(i)) + unit.charAt(i);
  }
  return str
    .replace(/零(仟|佰|拾|角)/g, '零')
    .replace(/(零)+/g, '零')
    .replace(/零(万|亿|圆)/g, '$1')
    .replace(/(亿)万|壹(拾)/g, '$1$2')
    .replace(/^圆零?|零分/g, '')
    .replace(/圆$/g, '圆整');
};

/**
 * 去掉字符串空格
 * @param {*} str 要处理的字符串
 * @param {*} trimAll 是否去除中间空格
 */
export function trim(str, trimAll = false) {
  if (!str) return '';
  const reg = trimAll ? /\s+/g : /(^\s*)|(\s*$)/g;
  return str.replace(reg, '');
}

// 去掉所有空格 val 字符串
export const trimAll = (val) => {
  if (typeof val === 'number') {
    return val;
  }
  if (!val) {
    return undefined;
  }
  if (Array.isArray(val)) {
    return [...val];
  }
  return val.trim() || undefined;
};

// 转换下拉菜单数据
export const formatTreeData = (data, props = {}) => {
  if (!Array.isArray(data) || !data.length) return false;
  const { label = 'label', value = 'value' } = props;
  return data.map((item) => {
    return {
      label: item[label],
      value: item[value],
      children: item.hasChildren ? formatTreeData(item.children, props) : null
    };
  });
};

// 转换下拉菜单数据
export const formatTreeData2 = (data, props = {}) => {
  if (!Array.isArray(data) || !data.length) return false;
  const { label = 'label', value = 'value' } = props;
  return data.map((item) => {
    return {
      label: item[label],
      value: item[value],
      children: item.children ? formatTreeData(item.children, props) : null
    };
  });
};

// 平铺下拉菜单数据
export const flatTreeData = (data, props = {}) => {
  if (!Array.isArray(data) || !data.length) return [];
  const ret = [];
  const { label = 'label', value = 'value' } = props;
  data.forEach((item) => {
    ret.push({
      label: item[label],
      value: item[value]
    });
    if (item.children && item.children.length) {
      ret.push(...flatTreeData(item.children, props));
    }
  });
  return ret;
};

// 根据ID查询树形结构中的对应的数据
export const findItemById = (list, id) => {
  // 每次进来使用find遍历一次
  let res = list.find((item) => item.value == id);
  if (res) {
    return res;
  } else {
    for (let i = 0; i < list.length; i++) {
      if (list[i].children instanceof Array && list[i].children.length > 0) {
        res = findItemById(list[i].children, id);
        if (res) return res;
      }
    }
    return null;
  }
};
/**
 * 将数组转化为 key: value 的格式
 * @param {*} options
 * @example getKeyValue([{ value: 1, label: '通过' }, { value: 2, label: '不通过' }]) => { 1: '通过', 2: '不通过' }
 */
export function getKeyValue(options) {
  return options.reduce((prev, curr) => {
    prev[curr.value] = curr.label;
    return prev;
  }, {});
}
const calculation = {
  add() {
    let figures = [].shift.call(arguments);
    let args = [].slice.call(arguments);
    let multiple = Math.pow(10, figures);
    let result = 0;
    args.forEach((num) => {
      result += Math.round(num * multiple);
    });
    return result / multiple;
  },
  sub() {
    let figures = [].shift.call(arguments);
    let args = [].slice.call(arguments);
    let multiple = Math.pow(10, figures);
    let result = Math.round(args.shift() * multiple);
    args.forEach((num) => {
      result -= Math.round(num * multiple);
    });
    return result / multiple;
  },
  mult() {
    let figures = [].shift.call(arguments);
    let args = [].slice.call(arguments);
    let multiple = Math.pow(10, figures);
    let result = 1;
    args.forEach((num) => {
      result *= Math.round(num * multiple);
    });
    return result / Math.pow(multiple, args.length);
  },
  divi() {
    let figures = [].shift.call(arguments);
    let args = [].slice.call(arguments);
    let multiple = Math.pow(10, figures);
    let result = Math.round(args.shift() * multiple);
    args.forEach((num) => {
      result /= Math.round(num * multiple);
    });
    return result;
  }
};

/**
 * 浮点数进行计算
 * @example calculate('add', 1, 2) => 3
 *          calculate('sub', 2, 1) => 1
 *          calculate('mult', 2, 1) => 2
 *          calculate('divi', 4, 2) => 2
 */
export function calculate() {
  let type = [].shift.call(arguments);
  let args = [].slice.call(arguments);
  // 表示小数点后几位
  let figures = 0;
  args.forEach((num) => {
    let decimal = (num + '').split('.')[1];
    if (!decimal) {
      return;
    }
    figures = decimal.length > figures ? decimal.length : figures;
  });
  args.unshift(figures);
  return calculation[type].apply(null, args);
}

// 最大余额法计算饼状图百分比
export function getPercentValue(valueList, idx, precision) {
  // 判断是否为空
  if (!valueList[idx]) {
    return 0;
  }
  // 求和
  var sum = valueList.reduce(function (acc, val) {
    return acc + (isNaN(val) ? 0 : val);
  }, 0);
  if (sum === 0) {
    return 0;
  }
  // 10的2次幂是100，用于计算精度。
  var digits = Math.pow(10, precision);
  // 扩大比例100，
  var votesPerQuota = valueList.map(function (val) {
    return ((isNaN(val) ? 0 : val) / sum) * digits * 100;
  });
  // 总数，扩大比例意味的总数要扩大
  var targetSeats = digits * 100;
  // 再向下取值，组成数组
  var seats = votesPerQuota.map(function (votes) {
    return Math.floor(votes);
  });
  // 再新计算合计，用于判断与总数量是否相同，相同则占比会100%
  var currentSum = seats.reduce(function (acc, val) {
    return acc + val;
  }, 0);
  // 余数部分的数组：原先数组减去向下取值的数组，得到余数部分的数组
  var remainder = votesPerQuota.map(function (votes, idx) {
    return votes - seats[idx];
  });
  // 给最大最大的余额加1，凑个占比100%；
  while (currentSum < targetSeats) {
    //  找到下一个最大的余额，给其加1
    var max = Number.NEGATIVE_INFINITY;
    var maxId = null;
    for (var i = 0, len = remainder.length; i < len; ++i) {
      if (remainder[i] > max) {
        max = remainder[i];
        maxId = i;
      }
    }
    // 对最大项余额加1
    ++seats[maxId];
    // 已经增加最大余数加1，则下次判断就可以不需要再判断这个余额数。
    remainder[maxId] = 0;
    // 总的也要加1，为了判断是否总数是否相同，跳出循环。
    ++currentSum;
  }
  // 这时候的seats就会总数占比会100%
  return seats[idx] / digits;
}
// 格式化时间(去除上午/下午字符)
export function formatTime(timeStr) {
  const reg =
    /[\u4e00-\u9fa5|\u3002|\uff1f|\uff01|\uff0c|\u3001|\uff1b|\uff1a|\u201c|\u201d|\u2018|\u2019|\uff08|\uff09|\u300a|\u300b|\u3008|\u3009|\u3010|\u3011|\u300e|\u300f|\u300c|\u300d|\ufe43|\ufe44|\u3014|\u3015|\u2026|\u2014|\uff5e|\ufe4f|\uffe5]/gi;

  if (!reg.test(timeStr)) return timeStr;

  let timeArr = timeStr.replace(reg, '').split(' ');
  let date = timeArr[0].split('-');
  let hour = timeArr[1].split(':')[0].padStart(2, '0');
  let minute = timeArr[1].split(':')[1].padStart(2, '0');
  let second = timeArr[1].split(':')[2].padStart(2, '0');

  if (timeStr.includes('下午')) {
    hour = Number(hour) + 12;
  }

  return `${date[0]}-${date[1]}-${date[2]} ${hour}:${minute}:${second}`;
}
// 生成一个不重复的ID  GenNonDuplicateID()
export function GenNonDuplicateID(randomLength) {
  return Number(
    Math.random().toString().substr(3, randomLength) + Date.now()
  ).toString(36);
}

// 处理url传入的query
export function getJointUrl(url, query) {
  if (!url) {
    throw new Error('url cannot be empty');
  }
  if (!Object.keys(query).length) {
    return url;
  }
  let res = `${url}${url.includes('?') ? '&' : '?'}`;
  for (const key in query) {
    let val = query[key];
    if (val === '' || val === undefined) continue;
    if (Array.isArray(val)) {
      val = val.toString();
    }
    res += `${key}=${encodeURIComponent(encodeURIComponent(val))}&`;
  }
  return res.slice(0, -1);
}
