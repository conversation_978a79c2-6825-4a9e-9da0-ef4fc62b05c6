<template>
  <div class="search-content">
    <el-form
      label-suffix="："
      :rules="rules"
      inline
      v-loading="searchLoading"
      ref="search"
      :model="form"
    >
      <el-form-item label="发起组织" prop="initiateOrgId">
        <el-select
          v-model="form.initiateOrgId"
          filterable
          placeholder="请选择"
          @change="handleOrganization"
        >
          <el-option
            v-for="dict in deliveryList"
            :key="dict.id"
            :label="dict.deptName"
            :value="dict.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        v-show="isCurrentOrg"
        label="被考核单位"
        prop="assessedOrgId"
      >
        <el-select
          v-model="form.assessedOrgId"
          filterable
          clearable
          @change="submit()"
          placeholder="请选择"
        >
          <el-option
            v-for="dict in assessedOrgList"
            :key="dict.id"
            :label="dict.deptName"
            :value="dict.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="体系名称" prop="schemeId">
        <el-select
          v-model="form.schemeId"
          filterable
          clearable
          @change="submit()"
          :disabled="!form.initiateOrgId"
          placeholder="请选择"
        >
          <el-option
            v-for="dict in systemNameList"
            :key="dict.id"
            :label="dict.schemeName"
            :value="dict.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="考核周期" prop="rwPeriodDetailId">
        <el-select
          v-model="form.rwPeriodDetailId"
          filterable
          clearable
          @change="submit()"
          :disabled="!form.initiateOrgId"
          placeholder="请选择"
          style="width: 130px"
        >
          <el-option
            v-for="dict in checkList"
            :key="dict.id"
            :label="dict.periodName"
            :value="dict.id"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button
          icon="el-icon-search"
          :disabled="!form.initiateOrgId"
          type="primary"
          @click="submit()"
          >查询</el-button
        >
        <el-button
          icon="el-icon-delete"
          :disabled="!form.initiateOrgId"
          @click="reset"
          >清空</el-button
        >
        <el-button
          @click="exportResult"
          type="warning"
          v-if="permission['evaluation-result-export']"
          :disabled="!form.initiateOrgId"
          :loading="exportLoading"
          icon="el-icon-download"
          plain
          >{{ exportLoading ? '导出中' : '导出' }}</el-button
        >
        <!-- <el-button icon="el-icon-search" type="primary" @click="preview()"
          >预览</el-button
        > -->
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
  import { getCurrentUp } from '@/api/assess-manage/examine-result';
  import { getBelowDepartment } from '@/api/assess-manage';
  import { getStore } from '@/util/store';
  import { mapGetters } from 'vuex';

  export default {
    props: {
      form: { type: Object, default: () => {} },
      systemNameList: { type: Array, default: () => [] },
      checkList: { type: Array, default: () => [] },
      searchLoading: { type: Boolean, default: false },
      exportLoading: { type: Boolean, default: false }
    },
    data() {
      return {
        deliveryList: [],
        assessedOrgList: [],
        rules: {
          initiateOrgId: [{ required: true, message: ' ' }]
          // schemeId: [{ required: true, message: ' ' }],
          // rwPeriodDetailId: [{ required: true, message: ' ' }]
        }
      };
    },
    computed: {
      ...mapGetters(['userInfo', 'permission']),
      unitId() {
        let org = getStore({ name: 'current-organization' });
        return org ? org['id'] : '';
      },
      isCurrentOrg() {
        return this.unitId === this.form.initiateOrgId;
      }
    },
    async mounted() {
      await this.getDeliveryList();
      await this.getAssessedList();
      // await this.getSystemName();
      // await this.getCheckList();
    },
    methods: {
      preview() {
        let routeUrl = this.$router.resolve({
          name: 'file-preview',
          query: {
            url: `url`,
            docName: `docName`,
            fileType: `fileType`,
            fileUrl: `fileUrl`
          }
        });
        window.open(routeUrl.href, '_blank');
      },
      // 发起组织
      async getDeliveryList() {
        try {
          const Func = getCurrentUp;
          const {
            data: { data }
          } = await Func({ deptId: this.unitId });
          this.deliveryList = data || [];
          // this.form.initiateOrgId =
          //   this.deliveryList[0] && this.deliveryList[0].id;
        } catch (e) {
          console.log(e);
        }
      },
      // 被考核单位
      async getAssessedList() {
        try {
          const {
            data: { data }
          } = await getBelowDepartment({ deptId: this.unitId });
          this.assessedOrgList = data || [];
        } catch (e) {
          console.log(e);
        }
      },
      handleOrganization() {
        this.form.rwPeriodDetailId = undefined;
        this.form.schemeId = undefined;
        this.submit();
      },
      exportResult() {
        this.$emit('exportResult');
      },
      validForm() {
        let bool = false;
        this.$refs.search.validate((valid) => {
          bool = valid;
        });
        return bool;
      },
      reset() {
        this.form.rwPeriodDetailId = undefined;
        this.form.schemeId = undefined;
        this.form.assessedOrgId = undefined;
        this.submit(1);
      },
      submit(reset) {
        let bool = this.validForm();
        if (!bool) {
          return this.$emit('search', []);
        }
        this.$emit('search', { ...this.form, reset });
      }
    }
  };
</script>
<style lang="scss" scoped>
  .el-form-item {
    margin-bottom: 10px;
  }
</style>
