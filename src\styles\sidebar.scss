

.el-menu--popup{
    .el-menu-item{
        background-color: #20222a;

        i,span{
            color: hsla(0deg,0%,100%,70%);
        }

        &:hover{
            i,span{
                color: #fff;
            }
        }

        &.is-active {
            background-color: rgba(0,0,0,80%);

            &::before {
                position: absolute;
                top: 0;
                bottom: 0;
                left: 0;
                width: 4px;
                background: #409eff;
                content: '';
            }

            i,span{
                color: #fff;
            }
        }
    }

}

.avue-sidebar {
    position: relative;
    box-sizing: border-box;
    height: 100%;
    padding-top: 60px;
    background-color: #20222a;
    box-shadow: 2px 0 6px rgba(0,21,41,35%);
    transition: width .2s;
    user-select: none;

    .el-scrollbar__wrap {
        overflow-x: hidden;
    }

    &--tip{
        position: absolute;
        top: 5px;
        left: 5%;
        z-index: 2;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 90%;
        height: 140px;
        color: #ccc;
        font-size: 14px;
        text-align: center;
        background-color: rgba(0,0,0,40%);
        border-radius: 5px;
    }

    .el-menu-item,.el-submenu__title{
        i{
            margin-top: -3px;
            margin-right: -10px;
        }

        i,span{
            color: hsla(0deg,0%,100%,70%);
        }

        &:hover{
            background: transparent;

            i,span{
               color: #fff;
            }
        }

        &.is-active {
            &::before {
                position: absolute;
                top: 0;
                bottom: 0;
                left: 0;
                width: 4px;
                background: #409eff;
                content: '';
            }
            background-color: rgba(0,0,0,80%);

            i,span{
                color: #fff;
            }
        }
    }

}
