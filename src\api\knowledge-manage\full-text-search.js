import request from '@/router/axios';
// ------ 全文检索 -------
// 列表
export const getClassifyList = (data) => {
  return request({
    url: '/api/es/queryHighLightWordDoc',
    method: 'post',
    data
  });
};
// 新增、编辑
export const knowledgeSave = (data) => {
  return request({
    url: '/api/zbusiness-knowledge/base/save',
    method: 'post',
    data
  });
};
// 删除
export const knowledgeDel = (data) => {
  return request({
    url: '/api/zbusiness-knowledge/base/deleteByIds',
    method: 'delete',
    data
  });
};
// 详情
export const knowledgeDetail = (id) => {
  return request({
    url: `/api/zbusiness-knowledge/base/fetchById/${id}`,
    method: 'get'
  });
};
// 查询所有部门
export const getAllOrgTreeList = () => {
  return request({
    url: `/api/szyk-system/dept/lazy-list`,
    method: 'get'
  });
};
