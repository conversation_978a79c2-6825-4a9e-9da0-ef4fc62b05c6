<template>
  <div
    class="h-upload"
    :id="'h-upload' + _uid"
    :class="{ 'is-deleted': isDeleted }"
  >
    <el-upload
      v-if="(uploadable && !closeUpload) || againUpload"
      :action="action"
      :headers="hHeaders"
      :multiple="multiple"
      :show-file-list="false"
      :accept="accept"
      :list-type="listType"
      :before-upload="handleBeforeUpload"
      :on-success="handleOnSuccess"
      :on-error="handleOnError"
      :on-exceed="handleExceed"
      :disabled="hDisabled"
      :drag="drag"
      :auto-upload="autoUpload"
      :on-change="onChange"
      :data="uploadData"
      class="h-upload-content"
      :class="{ 'content-empty': contentEmpty }"
    >
      <slot></slot>
      <slot name="trigger"></slot>
      <div v-if="showTips" slot="tip" class="h-upload-tips">{{ tip }}</div>
    </el-upload>
    <template v-else-if="!fileList.length && noTip">无</template>
    <div v-if="showFileList" v-loading="fileLoading">
      <div
        v-for="(
          { url, icon, name, type, createTime, id, attachId, domain, urlname },
          index
        ) in fileList"
        :key="url + index"
        class="file-item"
        :class="{ 'is-disabled': hDisabled }"
      >
        <el-image
          :src="currentUrl"
          :preview-src-list="[currentUrl]"
          :class="`js_imgPreviewModel${key}${index} img-preview-model`"
        >
        </el-image>
        <img v-oss :src="icon" class="file-item-icon" />
        <span
          class="file-name"
          :style="{
            maxWidth: zoomWidth ? `${FixedWidth - rZoomWidth}px` : 'auto'
          }"
          :title="name + ' ' + createTime"
        >
          <el-button
            :style="{
              maxWidth: zoomWidth ? `${FixedWidth - rZoomWidth}px` : 'auto'
            }"
            class="button-file-name"
            style="color: #606266"
            type="text"
            @click="
              seeFile(type, index, url, name, id, attachId, domain, urlname)
            "
            >{{ name }}
            <template v-if="showCreateTime">{{ createTime }}</template>
          </el-button>
        </span>
        <template v-if="!disabled">
          <i class="el-icon-circle-check" />
          <i
            @click="handleOnRemove(index)"
            class="el-icon-circle-close file-list-close-btn"
          />
        </template>
        <template v-if="!isDeleted && disabled">
          <span
            @click="handleDownload(encodeOSSUrl(url), name, id, attachId, type)"
            class="el-button-down el-button--text"
          >
            下载
          </span>
        </template>
      </div>
    </div>
  </div>
</template>

<script>
  import website from '@/config/website';
  // import { Base64 } from 'js-base64';
  import {
    deepClone,
    getDocIcon,
    docViewJudge,
    encodeOSSUrl,
    downloadFileBlob,
    downloadXls,
    downloadZip
  } from '@/util/util';
  import { resetRouter } from '@/router/router';
  // import { getWpsViewUrl } from '@/api/resource/wps';
  import { mapGetters } from 'vuex';
  import { dateFormat } from '@/util/date';
  // import { onlinePreview } from '@/util/util';
  import { downloadFile } from '@/api/common';

  export default {
    name: 'HUpload',
    inject: {
      elForm: {
        default: ''
      }
    },
    props: {
      value: [String, Array],
      drag: {
        type: Boolean,
        default: false
      },
      orgId: { type: String, default: '' },
      // 固定一个宽度
      FixedWidth: { type: Number, default: 106 },
      contentEmpty: {
        type: Boolean,
        default: false
      },
      // default: '/api/attila-resource/oss/endpoint/put-file-by-original-name'
      action: {
        type: String,
        default: '/api/szyk-resource/oss/endpoint/put-file-attach'
      },
      headers: {
        type: Object,
        default() {
          return {};
        }
      },
      multiple: Boolean,
      limit: {
        type: Number,
        default: 30
      },
      zoomWidth: {
        type: Number,
        default: 0
      },
      showFileList: {
        type: Boolean,
        default: false
      },
      // '.rar, .zip, .doc, .docx, .pdf, .jpg'
      accept: {
        type: String,
        default:
          '.jpg, .jpeg, .png, .gif, .bmp, .doc, .docx, .xls, .xlsx, .pdf, .ppt, .pptx, .zip, .rar, .7z'
      },
      beforeUpload: Function,
      onSuccess: Function,
      onError: Function,
      onRemove: Function,
      listType: {
        type: String,
        validator(value) {
          return ['text', 'picture', 'picture-card'].indexOf(value) !== -1;
        }
      },
      disabled: {
        type: Boolean,
        default: false
      },
      showLoading: {
        type: Boolean,
        default: false
      },
      showCreateTime: {
        type: Boolean,
        default: false
      },
      againUpload: {
        type: Boolean,
        default: false
      },
      // 上传文件最大限制
      maxFileSize: {
        type: Number,
        default: 100
      },
      tip: {
        type: String,
        default:
          '支持扩展名：.jpg, .jpeg, .png, .gif, .bmp, .doc, .docx, .xls, .xlsx, .pdf, .ppt, .pptx, .zip, .rar, .7z，且不超过100MB'
      },
      // 判断文件列表没数据时是否显示无
      noTip: {
        type: Boolean,
        default: true
      },
      showTip: {
        type: Boolean,
        default: true
      },
      autoUpload: {
        type: Boolean,
        default: true
      },
      onChange: Function,
      resetDownLoadFunction: {
        type: Function,
        default: async (url, fileName, id, attachId, type) => {
          if (id || attachId) {
            downloadFile(attachId || id)
              .then((res) => {
                if (['rar', 'zip'].includes(type)) {
                  downloadZip(res.data, fileName);
                } else {
                  downloadXls(res.data, fileName);
                }
              })
              .catch(() => {});
          } else {
            downloadFileBlob(url, fileName);
          }
        }
      },
      isDeleted: {
        type: Boolean,
        default: false
      },
      closeUpload: {
        type: Boolean,
        default: false
      },
      // 如果useAttachId为true，则使用attachId
      useAttachId: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        loading: null,
        key: '',
        encodeOSSUrl,
        uploadData: {
          menuId: '',
          orgId: ''
        },
        visualWidth: 0,
        currentUrl: '',
        fileLoading: false
      };
    },
    computed: {
      hHeaders() {
        let headers = {
          Authorization: `Basic ${website.correspondentSec}`,
          'Tenant-Id': this.$store.getters.tenantId,
          'User-Type': 'web',
          ...this.headers
        };
        headers[website.tokenHeader] = 'bearer ' + this.$store.getters.token;
        return headers;
      },
      hDisabled() {
        return this.disabled || (this.elForm || {}).disabled;
      },
      uploadable() {
        return (
          !this.hDisabled &&
          ((!this.multiple && !this.value) ||
            (this.multiple && this.value.length < this.limit))
        );
      },
      showTips() {
        return this.tip && this.uploadable && this.showTip;
      },
      fileList() {
        return this.getFile(this.value);
        // let list = [];
        // if (this.multiple) {
        //   list = [...this.value];
        // } else if (this.value) {
        //   list = [this.value];
        // }
        // const u = list.filter((l) => l);
        // return Array.from(u, (url) => {
        //   return this.getFile(url);
        // });
      },
      srcList() {
        return this.fileList
          .filter(({ type }) => this.imageType(type))
          .map(({ url }) => url);
      },
      rZoomWidth() {
        return this.disabled ? this.zoomWidth : this.zoomWidth - 22;
      },
      ...mapGetters(['userInfo', 'menu'])
    },
    watch: {
      value(val) {
        if (this.$parent.$options.componentName === 'ElFormItem') {
          this.$parent.$emit('el.form.change', val);
        }
      }
    },
    created() {
      this.key = new Date().getTime();
    },
    mounted() {
      this.computeFileNameWith();
    },
    methods: {
      computeFileNameWith() {
        const element = document.getElementById('h-upload' + this._uid);
        const visualWidth =
          element.parentNode && element.parentNode.offsetWidth;
        this.visualWidth = visualWidth || 0;
      },
      // 菜单树的遍历（广度优先搜索算法）
      bfsTreeEach(tree, func) {
        let node,
          nodes = tree.slice();
        while ((node = nodes.shift())) {
          func(node);
          if (node.children && node.children.length) {
            nodes.push(...node.children);
          }
          if (this.uploadData.menuId !== '') {
            break;
          }
        }
      },
      // 添加文件预览按钮是否显示判定
      docViewJudgeFun(type) {
        return docViewJudge(type);
      },
      imageType(type) {
        return [
          'jpg',
          'jpeg',
          'png',
          'gif',
          'bmp',
          'JPG',
          'JPEG',
          'PNG',
          'GIF',
          'BMP'
        ].includes(type);
      },
      // 下载文档
      handleDownload(...params) {
        this.$emit('dispatch', 'download');
        this.resetDownLoadFunction(...params);
      },
      // 查看文档
      seeFile(type, index, url, name, id, attachId, domain, urlname) {
        this.$emit('dispatch', 'see');
        if (this.imageType(type)) {
          this.fileLoading = true;
          downloadFile(attachId || id)
            .then((res) => {
              let file = res.data;
              var blob = new Blob([file], { type: file.type });
              var url = URL.createObjectURL(blob);
              this.currentUrl = url;
              // document.querySelector('#js_imgPreviewModel').src = url;
              // console.log(blob, url);
              let imgBox = document.getElementsByClassName(
                `js_imgPreviewModel${this.key}${index}`
              )[0];
              setTimeout(() => {
                imgBox.getElementsByTagName('img')[0].click();
                this.fileLoading = false;
              }, 800);
            })
            .catch(() => {
              this.fileLoading = false;
            });
        } else if (['rar', 'zip'].includes(type)) {
          // downloadFileBlob(url, name);
          downloadFile(attachId || id)
            .then((res) => {
              downloadZip(res.data, name);
            })
            .catch(() => {});
        } else {
          let routeUrl = this.$router.resolve({
            name: 'file-preview',
            query: {
              url: `${domain}/${urlname}`,
              type,
              name,
              id
            }
          });
          window.open(routeUrl.href, '_blank');
          // onlinePreview(url);
        }
      },
      handleBeforeUpload(file) {
        this.uploadData.menuId = '';
        this.bfsTreeEach(this.menu, (node) => {
          if (node.code === this.$route.meta.remark) {
            this.uploadData.menuId = node.id;
          }
        });
        this.uploadData.orgId = this.orgId;

        let { name, size } = file;
        let typeList = this.accept
          .split(',')
          .map((item) => item.trim().toLowerCase().substr(1));
        // 文件类型校验
        let dotIndex = name.lastIndexOf('.');
        if (dotIndex === -1) {
          this.$message.error(
            `请上传正确格式的文件。支持格式：${typeList.join('/')}`
          );
          return false;
        } else {
          let suffix = name.substring(dotIndex + 1);
          if (typeList.indexOf(suffix.toLowerCase()) === -1) {
            this.$message.error(
              `请上传正确格式的文件。支持格式：${typeList.join('/')}`
            );
            return false;
          }
        }
        let result = true;
        if (this.multiple && this.value.length >= this.maxFileSize) {
          this.$message.error('文件数量超过上限');
          return false;
        }
        if (size > 1048576 * this.maxFileSize) {
          this.$message.error(`文件大小不能超过${this.maxFileSize}M！`);
          return false;
        }

        if (this.beforeUpload) {
          result = this.beforeUpload(file);
          if (result === false) {
            return false;
          }
        }
        if (this.showLoading) {
          this.loading = this.$loading({
            lock: true,
            text: '正在上传',
            spinner: 'el-icon-loading'
          });
        }
        if (result !== true) {
          return Promise.resolve(result);
        }
      },
      handleOnSuccess(response, file) {
        // console.log('response', response);
        // console.log('file', file);
        if (this.showLoading && this.loading) {
          this.loading.close();
          this.loading = null;
        }
        let value = response.data;
        if (this.multiple && this.value) {
          value = [...this.value, value];
        }
        // 原先
        // this.$emit('input', value);
        this.$emit('input', this.getFile(value));
        if (this.onSuccess) {
          this.$nextTick(() => {
            this.onSuccess(response, file, this.fileList);
          });
        }
      },
      handleOnError(err, file) {
        if (this.showLoading && this.loading) {
          this.loading.close();
          this.loading = null;
        }
        if (err.status === 401) {
          this.$store.commit('SET_REFRESH_LOCK', true);
          // 如果是401则跳转到登录页面
          if (!this.$store.getters.reloginFlag) {
            this.$store.commit('SET_RELOGIN_FLAG', true);
            this.$confirm(
              '您尚未登录或者登录信息已失效，请重新登录',
              '请登录',
              {
                confirmButtonText: '前往登录'
              }
            )
              .then(() => {
                this.$store.dispatch('FedLogOut').then(() => {
                  resetRouter();
                  this.$router.push({ name: 'login' });
                });
              })
              .catch(() => {
                this.$store.commit('SET_RELOGIN_FLAG', false);
              });
          }
          return;
        }
        let msg = JSON.parse(err.message).msg;
        this.$message.error(msg);
        if (this.onError) {
          this.onError(err, file, this.fileList);
        }
      },
      handleExceed() {
        this.$message.warning(`当前限制最多上传 ${this.limit} 个文件`);
      },
      handleOnRemove(index) {
        let file = deepClone(this.fileList[index]);
        let value;
        if (this.multiple) {
          value = [...this.value];
          value.splice(index, 1);
        } else {
          value = '';
        }
        this.$emit('input', value);
        if (this.onRemove) {
          this.$nextTick(() => {
            this.onRemove(file, this.fileList);
          });
        }
      },
      getFile(list) {
        if (!list) return [];
        list = Array.isArray(list) ? list : [list];
        let createUser = this.userInfo['user_id'];
        let createUserName = this.userInfo['nick_name'];
        let fileList = deepClone(list);
        fileList.forEach((item) => {
          item.id = item.id ? item.id : item.attachId;
          if (this.useAttachId) item.id = item.attachId;
          const urlname = item.urlname || item.name;
          item.urlname = urlname;
          item.name = item.originalName || item.name || '';
          let type = item.name.split('.');
          item.type = type[type.length - 1];
          item.url = item.link;
          item.createUser = createUser;
          item.createUserName = item.createUserName || createUserName;
          const d = item.createTime ? new Date(item.createTime) : new Date();
          let createTime = dateFormat(d, 'yyyy-MM-dd');
          item.createTime = createTime;
          item.icon = getDocIcon(item.type);
        });
        return fileList;
        // let name = '';
        // let type = '';
        // let icon = '';
        // let index = url.lastIndexOf('/');
        // if (index > -1) {
        //   name = url.substring(index + 1, url.length);
        // }
        // if (name.split('.').length > 2) {
        //   index = name.indexOf('.');
        //   if (index === 32) {
        //     name = name.substring(index + 1, name.length);
        //   }
        // }
        // index = name.lastIndexOf('.');
        // if (index > -1) {
        //   type = name.substring(index + 1, name.length);
        // }
        // icon = getDocIcon(type);
        // return { url, name, type, icon };
      },
      getPreviewList(url) {
        let list = [...this.srcList];
        let index = list.indexOf(url);
        if (index > -1) {
          let preList = list.splice(0, index) || [];
          list = list.concat(preList);
        }
        return list;
      }
    }
  };
</script>

<style lang="scss">
  @import '@/styles/element-ui';

  .h-upload {
    .button-file-name {
      padding: 0;
      padding-bottom: 2px;
      overflow: hidden; // 溢出隐藏
      white-space: nowrap; // 禁止换行
      text-align: left;
      text-overflow: ellipsis; // ...
      user-select: text;

      &:hover {
        text-decoration: underline;
      }
    }

    .file-name {
      // display: flex;
      // flex: 1 !important;

      // 修改省略号
      overflow: hidden; // 溢出隐藏
      white-space: nowrap; // 禁止换行
      text-align: left;
      text-overflow: ellipsis; // ...
    }

    &.is-deleted {
      .file-name {
        text-decoration: line-through;

        // overflow: hidden;
        // text-overflow: ellipsis;
        // width: 150px;
        // height: 32px;
        // font-size: 14px;
        // line-height: 16px;
        // overflow: hidden;
        // text-overflow: ellipsis;
        // display: -webkit-box;
        // -webkit-line-clamp: 1;
        // -webkit-box-orient: vertical;
        // border: 1px solid black;
      }
    }

    .img-preview-model {
      width: 0;
      height: 0;
    }

    .h-upload-content {
      width: 100%;
      height: 100%;

      & + .file-item {
        margin-top: 4px;
      }

      .h-upload-tips {
        margin-left: 10px;
        color: #909399;
        font-size: 12px;
      }
    }

    .content-empty {
      height: 0 !important;
    }

    .file-item {
      display: flex;
      align-items: center;

      // align-items: center;
      // padding: 12px 18px;
      line-height: 22px;

      .el-button--text {
        cursor: pointer;
      }

      .file-item-icon {
        height: 14px;
        margin-right: 8px;
      }

      .el-icon-circle-check {
        padding-top: 2px;
        color: #70cd44;
        font-size: 16px;
      }

      .file-list-close-btn {
        display: none;
        padding-top: 2px;
        color: #a3a3a3;
        font-size: 16px;
        cursor: pointer;

        &:hover {
          color: #757575;
        }
      }

      &:hover {
        .el-icon-circle-check {
          display: none;
        }

        .file-list-close-btn {
          display: inline-block;
        }
      }

      .el-button-down {
        display: inline-block;
        min-width: 28px;
        margin-left: 5px;
        padding: 0;
        font-size: 14px;
      }

      &.is-disabled {
        span {
          flex: unset;
          text-align: left;
        }
      }

      & + .file-item {
        margin-top: 4px;
      }
    }
  }
</style>
