<template>
  <div>
    <el-form ref="form" :model="queryParams" :inline="true" label-suffix="：">
      <el-form-item label="流程名称" prop="flowName">
        <el-input
          v-model="queryParams.flowName"
          placeholder="请输入流程名称"
          clearable
          :maxlength="20"
        />
      </el-form-item>
      <el-form-item label="发起人" prop="applyUserName">
        <el-input
          v-model="queryParams.applyUserName"
          placeholder="请输入发起人"
          clearable
          :maxlength="20"
        />
      </el-form-item>
      <el-form-item label="发起时间" prop="applyTime">
        <el-date-picker
          v-model="queryParams.applyTime"
          type="daterange"
          style="width: 240px"
          align="right"
          range-separator="至"
          value-format="yyyy-MM-dd HH:mm:ss"
          :default-time="['00:00:00', '23:59:59']"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-delete" @click="resetQuery">清空</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
  const query = {
    flowName: '',
    applyUserName: '',
    applyTime: []
  };
  export default {
    name: 'ProjectSubmitSearch',
    data() {
      return {
        queryParams: { ...query },
        unfold: false,
        loading: false,
        labelList: []
      };
    },
    mounted() {
      // this.handleQuery();
    },
    methods: {
      // 重置
      resetQuery() {
        this.$refs['form'].resetFields();
        this.handleQuery(1);
      },
      // 查询
      handleQuery(isResetQuery) {
        let { applyTime } = this.queryParams;
        let applyStartTime = undefined;
        let applyEndTime = undefined;
        if (Array.isArray(applyTime) && applyTime.length) {
          applyStartTime = applyTime[0];
          applyEndTime = applyTime[1];
        }
        let query = {
          ...this.queryParams,
          applyStartTime,
          applyEndTime,
          applyTime: undefined,
          isResetQuery
        };
        this.$emit('search', query);
      }
    }
  };
</script>
<style lang="scss"></style>
