<template>
  <div class="bc-form-build-relation-apply">
    <el-input
      @click.native="onClick"
      :value="value.name || ''"
      :placeholder="data.placeholder"
      readonly
      :title="value.name || ''"
    />
    <el-dialog
      @open="onOpen"
      title="选择关联审批单"
      :visible.sync="visible"
      width="800px"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <h-table
        v-loading="loading"
        @size-change="sizeChange"
        @pagination-current-change="currentChange"
        :data="tableData"
        :columns="columns"
        row-key="id"
        max-height="500px"
        show-pagination
        :page-size="size"
        :current-page="current"
        :total="total"
      >
        <el-radio
          slot="radio"
          slot-scope="{ row }"
          @change="currentRow = row"
          :value="currentRow.processInstanceId"
          :label="row.processInstanceId"
          class="radio-label-none"
        >
        </el-radio>
        <template slot="name" slot-scope="{ row }">
          <span @click="goApply(row)" class="formName">
            {{ row.processName }}
          </span>
        </template>
      </h-table>
      <template slot="footer">
        <el-button @click="visible = false">取消</el-button>
        <el-button @click="onChange" type="primary"> 确定 </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
  import { getRelationApplyList } from '@/api/flow/flow';

  export default {
    name: 'RelationApplyComp',
    props: {
      value: {
        type: Object,
        default() {
          return {};
        }
      },
      data: {
        type: Object,
        default() {
          return {};
        }
      },
      disabled: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        visible: false,
        currentRow: {},
        loading: false,
        customerName: '',
        tableData: [],
        columns: [
          { label: '', slotName: 'radio', width: '65px' },
          { label: '审批名称', slotName: 'name', showOverflowTooltip: true },
          {
            label: '审批分类',
            value: 'groupName',
            width: '120px',
            showOverflowTooltip: true
          },
          {
            label: '发起人',
            value: 'createUserName',
            width: '120px',
            showOverflowTooltip: true
          },
          { label: '发起时间', value: 'createDate', width: '180px' }
        ],
        size: 10,
        current: 1,
        total: 0
      };
    },
    methods: {
      onClick() {
        if (!this.disabled) {
          this.visible = true;
        }
      },
      onOpen() {
        if (this.value.id) {
          this.currentRow = {
            processInstanceId: this.value.id,
            processName: this.value.name
          };
        } else {
          this.currentRow = {};
        }
        this.size = 10;
        this.current = 1;
        this.search();
      },
      search() {
        this.loading = true;
        getRelationApplyList({
          processDefinitionId: this.data.relevanceApply,
          current: this.current,
          size: this.size
        })
          .then((res) => {
            this.loading = false;
            let { records = [], total } = res.data.data;
            this.tableData = records;
            this.total = total;
            if (this.currentRow.processInstanceId) {
              this.currentRow = records.find(
                (item) =>
                  item.processInstanceId === this.currentRow.processInstanceId
              );
            }
          })
          .catch(() => {
            this.loading = false;
          });
      },
      currentChange(currentPage) {
        this.current = currentPage;
        this.search();
      },
      sizeChange(size) {
        this.size = size;
        this.current = 1;
        this.search();
      },
      onChange() {
        let { processInstanceId = '', processName = '' } = this.currentRow;
        if (processInstanceId) {
          this.$emit(
            'input',
            processInstanceId
              ? { id: processInstanceId, name: processName }
              : {}
          );
        }
        this.visible = false;
      }
    }
  };
</script>

<style lang="scss">
  @import '@/styles/element-ui';

  .bc-form-build-relation-apply {
    .el-input__inner {
      cursor: pointer;
    }
  }

  .radio-label-none .el-radio__label {
    display: none;
  }
</style>
