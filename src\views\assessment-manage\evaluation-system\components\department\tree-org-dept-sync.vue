<template>
  <div style="width: 100%; height: 100%">
    <div class="department">
      <h3 class="dep_txt">组织结构</h3>
    </div>
    <div class="tree-wrapper" v-loading="loading">
      <el-tree
        v-if="status"
        ref="tree"
        node-key="id"
        lazy
        check-on-click-node
        check-strictly
        show-checkbox
        empty-text="暂无数据"
        default-expand-all
        :props="props"
        :load="loadNode"
        :render-content="renderContent"
        @check="checkFn"
        @check-change="checkChangeEmit"
      />
    </div>
  </div>
</template>

<script>
  import { getCurrentChild } from '@/api/assess-manage';
  import { mapGetters } from 'vuex';
  import { getStore } from '@/util/store';
  export default {
    name: 'TreeOrgSync',
    data() {
      return {
        props: {
          label: 'deptName',
          children: 'children',
          isLeaf: (data) => !data.hasChildren
        },
        nodeInfo: {},
        allExpanded: []
      };
    },
    props: {
      single: {
        type: Boolean,
        default: false
      },
      nodeList: {
        type: Array,
        default() {
          return [];
        }
      },
      tree: {
        type: Object,
        default() {
          return {};
        }
      },
      expandedArr: {
        type: Array,
        default() {
          return [];
        }
      },
      expandedQueryArr: {
        type: Array,
        default() {
          return [];
        }
      },
      loading: {
        type: Boolean,
        default: false
      },
      status: {
        type: Boolean,
        default: false
      },
      query: {
        type: String,
        default: ''
      }
    },
    watch: {
      expandedArr: {
        handler() {
          this.unRepeat();
        },
        deep: true,
        immediate: true
      },
      expandedQueryArr: {
        handler() {
          this.unRepeat();
        },
        deep: true,
        immediate: true
      }
    },
    computed: {
      ...mapGetters(['userInfo']),
      unitId() {
        let org = getStore({ name: 'current-organization' });
        return org ? org['id'] : '';
      }
    },
    methods: {
      // 去重
      unRepeat() {
        const _temp = [...this.expandedArr, ...this.expandedQueryArr];
        const _arr = new Set(_temp);
        this.allExpanded = [..._arr];
      },
      // 请求数据
      async request(id) {
        try {
          // this.loading = true;
          const res = await getCurrentChild({
            tenantId: '000000',
            parentId: id,
            title: this.query
          });
          let arr = res.data.data || [];
          arr.forEach((item) => (item.deptName = item.title));
          return arr;
        } catch (e) {
          console.error(e);
        } finally {
          if (typeof id !== undefined) {
            // this.loading = false;
          }
        }
      },
      // 加载数据
      async loadNode(node, resolve) {
        if (node.level === 0) {
          let params = {
            tenantId: '000000',
            parentId: this.unitId,
            title: this.query
          };
          const res = await getCurrentChild(params);
          let arr = res.data.data || [];
          arr.forEach((item) => (item.deptName = item.title));
          // 渲染
          this.$nextTick(function () {
            this.$refs.tree.setCheckedNodes(this.nodeList);
          });
          return resolve(arr);
        }
        if (node.level >= 1) {
          const list = await this.request(node.data.id);
          // 渲染
          this.$nextTick(function () {
            this.$refs.tree.setCheckedNodes(this.nodeList);
          });
          return resolve(list);
        }
      },
      // 单节点check 选择
      checkChangeEmit(node, status) {
        this.nodeInfo = {
          node,
          status
        };
      },
      checkFn(node, checkArr) {
        const { checkedNodes } = checkArr;
        if (this.single) {
          this.$refs.tree.setCheckedKeys([]);
          this.$nextTick(function () {
            this.$refs.tree.setCheckedNodes([node]);
          });
          this.$emit('setNodeSingle', [node]);
        } else {
          if (this.nodeInfo.status) {
            this.$emit('setNodePush', checkedNodes);
          } else {
            this.$emit('setNodeSplice', node);
          }
        }
      },
      // 渲染内容
      renderContent(h, { data }) {
        // deptCategory (1:公司,2:部门,3:小组)
        const { deptCategory, deptName } = data;
        if (deptCategory === 2) {
          return (
            <span>
              <i className="el-icon-folder" style="margin-right: 5px" />
              {deptName}【部门】
            </span>
          );
        } else if (deptCategory === 1) {
          return (
            <span>
              <i class="el-icon-office-building" style="margin-right: 5px" />
              {deptName}
            </span>
          );
        } else {
          return <span>{deptName}</span>;
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .department {
    box-sizing: border-box;
    height: 32px;
    margin: 0;
    background: #f8f8f9;

    h3 {
      margin: 0;
      font-weight: 600;
    }

    .dep_txt {
      padding-left: 15px;
      overflow: hidden;
      color: #515a6e;
      font-size: 13px;
      line-height: 30px;
      white-space: normal;
      text-overflow: ellipsis;
      word-break: break-all;
    }
  }

  .tree-wrapper {
    height: calc(100% - 40px);
    overflow: auto;
  }
</style>
