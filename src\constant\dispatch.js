import { getKeyValue } from '@/util/util';
// 周期类型
export const periodOptions = [
  { label: '月度', value: '1' },
  { label: '季度', value: '2' },
  { label: '半年度', value: '3' },
  { label: '年度', value: '4' }
];
// 周期状态
export const periodStatusOption = [
  { label: '全部开启', value: '1' },
  { label: '部分关闭', value: '2' },
  { label: '全部关闭', value: '3' }
];
export const periodKeyValue = getKeyValue(periodOptions);
export const periodStatusKeyValue = getKeyValue(periodStatusOption);
// 状态
export const statusOptions = [
  { label: '待下发', value: '1' },
  { label: '已下发', value: '2' },
  { label: '部分提报', value: '3' },
  { label: '全部提报', value: '4' }
];
