<template>
  <div>
    <Dialog width="1100px" :visible="open" :title="dialogTitle" @closed="reset">
      <el-table :data="list" stripe border style="width: 100%" row-key="id">
        <el-table-column type="index" width="60" align="center" label="序列" />
        <el-table-column
          align="center"
          :label="labelName"
          show-overflow-tooltip
        >
          <template slot-scope="{ row }">
            {{ row.name }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="结果" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <span style="color: #67c23a" v-if="row.result">成功</span>
            <span style="color: #f56c6c" v-else>失败</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="失败原因" show-overflow-tooltip>
          <template slot-scope="{ row }">
            {{ row.message || '---' }}
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button @click="reset">关 闭</el-button>
      </div>
    </Dialog>
  </div>
</template>
<script>
  import Dialog from '@/components/basic-dialog';
  // import {
  //   labelList,
  //   getLabelClassification,
  //   addLabel,
  //   addProjectLabel
  // } from '@/api/project-library';
  // import { formatTreeData } from '@/util/util';
  import { mapGetters } from 'vuex';
  export default {
    name: 'ProjectLibraryDetailEdit',
    components: { Dialog },
    props: {
      dialogTitle: {
        type: String,
        default: ''
      },
      formVisible: {
        type: Boolean,
        default: false
      },
      list: { type: Array, default: () => [] },
      labelName: { type: String, default: '单据编号' }
    },
    data() {
      return {};
    },
    methods: {
      reset() {
        this.open = false;
        this.$emit('refresh');
      }
    },
    computed: {
      ...mapGetters(['userInfo']),
      open: {
        get() {
          return this.formVisible;
        },
        set() {
          this.$emit('close');
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .user_tag {
    margin-top: 10px;
    margin-right: 10px;
  }

  .dialog-footer {
    .el-button + .el-button {
      margin-left: 10px !important;
    }
  }
</style>
