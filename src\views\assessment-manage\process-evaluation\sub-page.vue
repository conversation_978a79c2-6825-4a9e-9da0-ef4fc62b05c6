<template>
  <basic-container>
    <el-card class="box-card">
      <div slot="header" class="library-title">基本信息</div>
      <DetailInfo
        v-if="!isEdit"
        v-loading="loading"
        :detail="detail"
      ></DetailInfo>
      <EditInfo
        ref="info"
        v-if="isEdit"
        :formInfo="formInfo"
        :isEdit="isEdit"
        :formMember="formMember"
        :lowerDepartment="lowerDepartment"
        :systemNameList="systemNameList"
        :assessedList="assessedList"
        :sourceList="sourceList"
        :detail="detail"
        :id="id"
        v-loading="loading"
        @setsolution="handleSetsolution"
      ></EditInfo>
    </el-card>
    <el-card class="box-card">
      <div slot="header" class="library-title">
        考核指标<span style="font-size: 14px"
          >（扣/加分分值不高于单项评价指标分值）</span
        >
      </div>
      <EditMember
        ref="member"
        :isEdit="isEdit"
        :form="formMember"
        :formInfo="formInfo"
        :id="id"
        v-loading="loading"
        @setStageList="setStageList"
      ></EditMember>
    </el-card>

    <!-- <el-card class="box-card">
      <div slot="header" class="library-title">审批流程</div>
      sdfsdf
    </el-card> -->
    <div style="width: 100%; height: 80px"></div>
    <div class="btn-group">
      <el-button
        icon="el-icon-circle-close"
        :loading="btnLoading"
        type="text"
        @click="resetQuery"
        >取消</el-button
      >
      <el-button
        v-if="isEdit || !id"
        :loading="btnLoading"
        type="text"
        icon="el-icon-circle-check"
        @click="handleSubmit"
      >
        提交</el-button
      >
    </div>
    <InitiateSupervise
      ref="initiate"
      @save-success="saveSuccess"
      @hide="resetQuery"
    ></InitiateSupervise>
  </basic-container>
</template>
<script>
  import { DetailInfo, EditInfo, EditMember, DetailMember } from './components';
  import { InitiateSupervise } from '@/views/assessment-manage/initiate-supervise';

  import {
    getDetail,
    submitFetch
  } from '@/api/assess-manage/process-examination';
  import {
    getLowerDepartment,
    getSystemName,
    getCyclePage,
    getSourceNumber,
    getBelowDepartment
  } from '@/api/assess-manage';
  import { getDeptTree } from '@/api/system/dept';
  import { mapGetters } from 'vuex';
  import { calculate, formatTreeData } from '@/util/util';
  const form = {
    id: undefined,
    rwNo: undefined,
    initiateOrgId: '',
    initiateOrgName: '',
    oldinitiateOrgId: '',
    oldinitiateOrgName: '',
    assessedOrgId: '',
    assessedOrgName: '',
    oldassessedOrgId: '',
    oldassessedOrgName: '',
    schemeId: '',
    schemeName: '',
    oldschemeId: '',
    oldschemeName: '',
    rwPeriodDetailId: '',
    periodName: '',
    sourceType: '',
    associateId: '',
    associateNo: '',
    remark: '',
    createUserName: '',
    createTime: '',
    rwTotalScore: '',
    isInit: 1,
    rwDate: '',
    processFileIds: []
  };
  const formMember = {
    stageList: []
  };
  export default {
    name: 'indicator-sub-page',
    components: {
      DetailInfo,
      EditInfo,
      EditMember,
      DetailMember,
      InitiateSupervise
    },
    data() {
      return {
        detail: {},
        formInfo: { ...form },
        formMember: { ...formMember },
        btnLoading: false,
        loading: false,
        lowerDepartment: [],
        systemNameList: [],
        sourceList: [],
        assessedList: []
      };
    },
    computed: {
      isEdit() {
        let route = {
          'process-evaluation-edit': true,
          'process-evaluation-add': true
        };
        return route[this.$route.name] || false;
      },
      id() {
        return this.$route.params.id;
      },
      status() {
        return this.$route.query.status;
      },
      ...mapGetters(['permission', 'userInfo'])
    },
    async created() {
      // await this.getDept();
      // 新增
      if (!this.id) {
        // 申报年份
        // let year = new Date().getFullYear();
        // this.formFunds.year = year + '';
      } else {
        this.loading = true;
        await this.getDetail();
        this.loading = false;
      }
    },
    watch: {
      'formInfo.initiateOrgId': {
        handler: async function (val) {
          if (val && this.isEdit) {
            this.loading = true;
            // this.isEdit && (await this.getLowerDepartment(val));
            this.isEdit && (await this.getCyclePage(val));
            this.isEdit && (await this.getSystemName(val));
            // this.isEdit && (await this.requestAssessedOrganization(val));
            this.loading = false;
          }
        }
      },
      'formInfo.assessedOrgId': {
        handler: async function (val) {
          if (val && this.isEdit) {
            // this.loading = true;
            // console.log('isEdit', this.isEdit);
            // this.isEdit && (await this.getSystemName(val));
            // this.loading = false;
          }
        }
      },
      'formInfo.sourceType': {
        handler: async function (val) {
          if (val && this.isEdit) {
            this.loading = true;
            if (val === '1') {
              this.isEdit && (await this.getSourceNumber());
            } else {
              this.sourceList = [];
            }
            this.loading = false;
          }
        }
      }
    },
    methods: {
      // 被考核组织organization
      async requestAssessedOrganization() {
        const params = {
          deptId: this.formInfo.initiateOrgId
        };
        try {
          const {
            data: { data }
          } = await getBelowDepartment(params);
          let arr = data || [];
          this.assessedList = arr;
        } catch (e) {
          console.error(e);
        }
      },
      async getDept() {
        try {
          const {
            data: { data }
          } = await getDeptTree();
          this.lowerDepartment = formatTreeData(data, {
            label: 'title',
            value: 'id'
          });
        } catch (e) {
          console.error(e);
        }
      },
      async getSourceNumber() {
        try {
          let { assessedOrgId: constructionUnitId } = this.formInfo;
          let {
            data: { data }
          } = await getSourceNumber({ constructionUnitId });
          this.sourceList = data || [];
        } catch (error) {
          console.log(error);
        }
      },
      // 获取体系名称
      async getSystemName() {
        try {
          let { initiateOrgId } = this.formInfo;

          if (!initiateOrgId) return;
          let {
            data: { data }
          } = await getSystemName({
            initiateOrgId,
            schemeStatus: '0'
          });
          this.systemNameList = data || [];
          // if (!schemeName) {
          //   let schemeId = this.systemNameList.length
          //     ? this.systemNameList[0].id
          //     : '';
          //   let schemeName = this.systemNameList.length
          //     ? this.systemNameList[0].schemeName
          //     : '';
          //   this.formInfo.schemeId = schemeId;
          //   this.formInfo.schemeName = schemeName;
          //   this.formInfo.oldschemeId = schemeId;
          //   this.formInfo.oldschemeName = schemeName;
          // }
        } catch (error) {
          console.log(error);
        }
      },
      // 获取考核周期
      async getCyclePage(deptId) {
        try {
          let {
            data: { data }
          } = await getCyclePage({
            deptId,
            periodDetailStatus: '0',
            current: 1,
            size: 1
          });
          let arr = data ? data.records : [];
          if (!this.id) {
            this.formInfo.rwPeriodDetailId = arr.length ? arr[0].id : '';
            this.formInfo.periodName = arr.length ? arr[0].periodName : '';
          }
        } catch (error) {
          console.log(error);
        }
      },
      // 获取被考核机构
      async getLowerDepartment(deptId) {
        try {
          let {
            data: { data }
          } = await getLowerDepartment({ deptId });
          this.lowerDepartment = data || [];
        } catch (error) {
          console.log(error);
        }
      },
      handleSetsolution(flag = false) {
        !flag && (this.systemNameList = []);
        this.setStageList();
        // this.$refs.member.getIndicatorList();
      },
      setStageList(arr) {
        this.formMember.stageList = arr || [];
        this.calculateScore();
      },
      // 计算总分
      calculateScore() {
        let list = this.formMember.stageList;
        let total = 0;
        list.forEach((item) => {
          if (item.addScore == '0') {
            total = calculate('add', total, item.score);
          }
        });
        console.log(total);
        this.$set(this.formInfo, 'totalScore', total);
      },
      validTable() {
        // let { stageList: list } = this.formMember;
        // 评分是否正确
        // for (let index = 0; index < list.length; index++) {
        //   let bool = reg.regTwoNumber.test(list[index].rwScore);
        //   if (!bool && list[index].rwScore) {
        //     this.$message.warning(
        //       `第${index + 1} 行，考核扣/加分请输入10正数或2位小数`
        //     );
        //     return false;
        //   }
        // }
        // 是否在正确范围内打分
        // const res = new Map();
        // for (const item of list) {
        //   res.set(item['evaluateTargetId'], item);
        // }
        // let allEvaluateArr = [...res.values()];
        // let evaluateBool = allEvaluateArr.every((item) => {
        //   let score = Number(item.score);
        //   let getScore = Number(item.getScore);
        //   return score >= getScore && getScore >= 0;
        // });
        // !evaluateBool && this.$message.warning('请填写得分不大于分值不小于0');
        // return evaluateBool;
      },
      // 保存
      async handleSubmit() {
        let info = this.$refs['info'].validForm();
        if (!info) return this.$message.warning('请填写必要字段');
        let length = this.formMember.stageList.length;
        if (!length) return this.$message.warning('请添加考核指标');
        let member = this.$refs['member'].validForm();
        if (!member) return;

        // if (!this.id) {
        // let obj = this.validTable();
        // if (obj)
        //   return this.$message.warning(`请添加第${obj.index + 1}行评分标准`);
        // }
        try {
          let { rwTotalScore } = this.formInfo;
          this.$confirm(
            `考核扣/加分合计为${rwTotalScore || 0}分，确认提交？`,
            '提示',
            {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }
          )
            .then(async () => {
              let list = this.formMember.stageList;
              list.forEach((item) => {
                item.evaluateTargetId = item.evaluateTargetId
                  ? item.evaluateTargetId
                  : item.schemeEvaluateId;
                item.scoreMethodId = item.scoreMethodId
                  ? item.scoreMethodId
                  : item.id;
              });
              let processFileIds = this.formInfo.processFileIds.map(
                (item) => item.attachId || item.id
              );
              const params = {
                ...this.formInfo,
                processFileIds,
                rwType: '2',
                list
              };
              try {
                this.btnLoading = true;
                this.loading = true;
                const {
                  data: { data }
                } = await submitFetch(params);
                console.log(data);
                this.btnLoading = false;
                this.loading = false;
                // this.$message.success('保存成功');
                // this.resetQuery();
                await this.confirm('是否发起督办？', '发起督办')
                  .then(() => {
                    // console.log(2154545);
                    this.$refs.initiate.show({ id: data.id });
                  })
                  .catch(() => {
                    this.resetQuery();
                  });
              } catch (error) {
                this.btnLoading = false;
                this.loading = false;
              }
            })
            .catch(() => {});
        } catch (e) {
          console.log(e);
        } finally {
          this.btnLoading = false;
          this.loading = false;
        }
      },
      saveSuccess(list) {
        this.$router.$avueRouter.closeTag();
        let businessId = list.map((item) => item.id).join(',');
        let { assessedOrgId } = this.formInfo;
        this.$router.push({
          path: '/supervise-business/initiate-supervise/add',
          query: {
            businessType: 3, // 过程考核
            businessId,
            assessedOrgId
          }
        });
        // this.$refs.initiate.hide();
      },
      // 重置
      resetQuery() {
        this.$router.$avueRouter.closeTag();
        let isSubmit = this.$route.query.isSubmit;
        if (isSubmit)
          return this.$router.push(
            `/project-manage/project-submit/submit-view/${isSubmit}`
          );
        let home = this.$route.query.home;
        if (home) return this.$router.push(`/wel/index`);
        let assessed = this.$route.query.assessed;
        if (assessed)
          return this.$router.push(`/assessment-manage/record-assessed`);
        this.$router.push({
          path: '/assessment-manage/process-evaluation',
          query: {
            status: this.status
          }
        });
      },
      async getDetail() {
        try {
          const Func = getDetail;
          const {
            data: { data }
          } = await Func({
            id: this.id,
            flag: this.status == '1' ? 1 : 2
          });
          this.detail = { ...data };
          this.setForm();
        } catch (e) {
          console.log(e);
        }
      },
      // 初始化
      setForm() {
        this.detail.isInit = 1;
        for (const key in this.formInfo) {
          this.formInfo[key] = this.detail[key];
        }
        // 初始化编辑-保持数据显示
        this.formInfo.oldinitiateOrgId = this.formInfo.initiateOrgId;
        this.formInfo.oldinitiateOrgName = this.formInfo.initiateOrgName;
        this.formInfo.oldassessedOrgId = this.formInfo.assessedOrgId;
        this.formInfo.oldassessedOrgName = this.formInfo.assessedOrgName;
        this.formInfo.oldschemeId = this.formInfo.schemeId;
        this.formInfo.oldschemeName = this.formInfo.schemeName;
        // 获取指标分类
        // this.$refs.member.getIndicatorList();
        this.formInfo.processFileIds = this.detail['processFileList'] || [];

        let list = this.detail.list || [];
        // ----------------
        let obj = this.arrGroup(
          list,
          'evaluateTargetId',
          (item) => item.evaluateTargetId
        );
        // ----------------
        list.forEach((item) => {
          // item.id = undefined;
          let evaluateTargetId = JSON.stringify(item.evaluateTargetId);
          // 加分项为是，限制最高上限
          item.currentAssessScore =
            obj[evaluateTargetId] > Number(item.score)
              ? Number(item.score)
              : obj[evaluateTargetId];

          item.columnIndicator = item.number;
          item.oldRwScore = item.rwScore;
        });
        this.formMember.stageList = list;
      },
      // 归类 key 合并数
      arrGroup(arr, key, fn) {
        const obj = {};
        arr.forEach((item) => {
          const key = JSON.stringify(fn(item));
          obj[key] = obj[key] || [];
          obj[key].push(item);
        });
        for (const key in obj) {
          let rwScore = obj[key].reduce(
            (accumulator, currentValue) =>
              accumulator.concat(currentValue.rwScore || 0),
            [obj[key][0].leftScore]
          );
          obj[key] = calculate('add', ...rwScore);
        }

        return obj;
      }
    }
  };
</script>

<style lang="scss" scoped>
  .library-title {
    margin: 5px 0;
    padding-left: 8px;
    border-left: 4px solid #409eff;
  }

  .box-card:not(:first-child) {
    margin-top: 20px;
  }

  .el-pagination {
    margin-top: 20px;
  }

  .btn-group {
    position: fixed;
    bottom: 8px;
    left: 208px;
    z-index: 5;
    box-sizing: border-box;
    width: calc(100% - 240px);
    padding: 0 20px;
    text-align: right;
    background-color: rgba(66, 78, 89, 79%);
    border-radius: 4px;

    :deep(.el-button--text) {
      color: #cae4fb;
    }
  }

  /deep/ .el-table .el-table__header .cell {
    color: #909399 !important;
  }

  /deep/ .el-button--small span {
    font-size: 13px !important;
  }

  /deep/ .el-table .cell {
    font-size: 14px;
  }
</style>
