<template>
  <el-form
    ref="form"
    :model="hValue"
    :rules="formRules"
    :label-width="labelWidth"
    :validate-on-rule-change="false"
    class="apply-components"
  >
    <el-row :gutter="50">
      <template v-for="item of list">
        <component
          :is="`${item.type}Comp${fieldStatus[item.id] === '0' ? 'Edit' : ''}`"
          v-if="['inputMoney', 'daterange', 'region'].includes(item.type)"
          :key="item.id"
          v-model="hValue[item.id]"
          :prop="item.id"
          :data="item.valueJson"
        />
        <el-col
          v-else-if="item.type !== 'desc'"
          :key="item.id"
          :span="rowTypes.includes(item.type) ? 24 : 12"
        >
          <el-form-item
            :prop="item.id"
            :label="getComponentLabel(item)"
            :class="{ 'image-item': item.type === 'image' }"
          >
            <input-comp v-if="item.id === 'rest1'" :value="hValue.rest6" />
            <input-comp
              v-else-if="item.id === 'competeStartTime'"
              :value="`${hValue.competeStartTime || ''}${
                hValue.competeStartTime && hValue.competeEndTime
                  ? ` - ${hValue.competeEndTime}`
                  : ''
              }`"
            />
            <component
              :is="`${item.type}Comp${
                fieldStatus[item.id] === '0' ? 'Edit' : ''
              }`"
              v-else
              ref="formComp"
              v-model="hValue[item.id]"
              :data="item.valueJson || {}"
              :form="hValue"
              :component-list="item.children"
              :accept="accept"
              :total-value="totalValue || hValue"
            >
              <span v-if="item.type === 'file'" slot="button"> 选择附件 </span>
            </component>
          </el-form-item>
        </el-col>
      </template>
    </el-row>
  </el-form>
</template>
<script>
  import {
    ROW_TYPES,
    NO_RULE_TYPES,
    INPUT_RULE_TYPES
  } from '@/components/form-build-new/const';
  import {
    ID_CARD_NO_IMPORT,
    PHONE,
    TELEPHONE,
    MOBILE_PHONE,
    NO_ALL_SPACE,
    DATE_AND_HALF_DAY
  } from '@/const/validator';

  import inputComp from './items/common-comp';
  import textareaComp from './items/textarea-comp';
  import tipsComp from '@/components/form-build-new/items/tip-comp';
  import inputNumberComp from './items/common-comp';
  import inputMoneyComp from './items/input-money-comp';
  import computedComp from '@/components/form-build-new/items/computed-comp';
  import radioComp from './items/common-comp';
  import checkboxComp from './items/checkbox-comp';
  import dateComp from './items/common-comp';
  import daterangeComp from './items/daterange-comp';
  import imageComp from './items/image-comp';
  import fileComp from './items/file-comp';
  import idcardComp from './items/common-comp';
  import phoneComp from './items/common-comp';
  import peopleComp from './items/people-comp';
  import deptComp from './items/dept-comp';
  import areaComp from './items/area-comp';
  import regionComp from './items/region-comp';
  import relationApplyComp from './items/relation-apply-comp';
  import formComp from './items/form-comp';
  import textComp from './items/common-comp';
  import inputCompEdit from '@/components/form-build-new/items/input-comp';
  import textareaCompEdit from '@/components/form-build-new/items/textarea-comp';
  import tipsCompEdit from '@/components/form-build-new/items/tip-comp';
  import inputNumberCompEdit from '@/components/form-build-new/items/input-number-comp';
  import inputMoneyCompEdit from '@/components/form-build-new/items/input-money-comp';
  import computedCompEdit from '@/components/form-build-new/items/computed-comp';
  import radioCompEdit from '@/components/form-build-new/items/radio-comp';
  import checkboxCompEdit from '@/components/form-build-new/items/checkbox-comp';
  import dateCompEdit from '@/components/form-build-new/items/date-comp';
  import daterangeCompEdit from '@/components/form-build-new/items/daterange-comp';
  import imageCompEdit from '@/components/form-build-new/items/image-comp';
  import fileCompEdit from '@/components/form-build-new/items/file-comp';
  import idcardCompEdit from '@/components/form-build-new/items/idcard-comp';
  import phoneCompEdit from '@/components/form-build-new/items/phone-comp';
  import peopleCompEdit from '@/components/form-build-new/items/people-comp';
  import deptCompEdit from '@/components/form-build-new/items/dept-comp';
  import areaCompEdit from '@/components/form-build-new/items/area-comp';
  import regionCompEdit from '@/components/form-build-new/items/region-comp';
  import relationApplyCompEdit from '@/components/form-build-new/items/relation-apply-comp';
  import formCompEdit from '@/components/form-build-new/items/form-comp';
  import textCompEdit from '@/components/form-build-new/items/text-comp';

  export default {
    name: 'ApplyComponent',
    components: {
      inputComp,
      textareaComp,
      tipsComp,
      inputNumberComp,
      inputMoneyComp,
      computedComp,
      radioComp,
      checkboxComp,
      dateComp,
      daterangeComp,
      imageComp,
      fileComp,
      idcardComp,
      phoneComp,
      peopleComp,
      deptComp,
      areaComp,
      regionComp,
      relationApplyComp,
      formComp,
      textComp,
      inputCompEdit,
      textareaCompEdit,
      tipsCompEdit,
      inputNumberCompEdit,
      inputMoneyCompEdit,
      computedCompEdit,
      radioCompEdit,
      checkboxCompEdit,
      dateCompEdit,
      daterangeCompEdit,
      imageCompEdit,
      fileCompEdit,
      idcardCompEdit,
      phoneCompEdit,
      peopleCompEdit,
      deptCompEdit,
      areaCompEdit,
      regionCompEdit,
      relationApplyCompEdit,
      formCompEdit,
      textCompEdit
    },
    props: {
      value: {
        type: Object,
        default() {
          return {};
        }
      },
      componentList: {
        type: Array,
        default: () => []
      },
      labelWidth: {
        type: String,
        default: '160px'
      },
      accept: String,
      fieldStatus: {
        type: Object,
        default: () => {
          return {};
        }
      },
      totalValue: Object
    },
    data() {
      return {
        rowTypes: ROW_TYPES,
        noRuleTypes: NO_RULE_TYPES,
        inputRuleTypes: INPUT_RULE_TYPES,
        formRules: {}
      };
    },
    computed: {
      hValue: {
        get() {
          return this.value;
        },
        set(val) {
          this.$emit('input', val);
        }
      },
      list() {
        const list = [];
        this.componentList.forEach((item) => {
          if (this.fieldStatus[item.id] === '2') {
            return;
          }
          const isType =
            [
              'rest',
              'replacecard',
              'work',
              'out',
              'trip',
              'turnFormal',
              'leave'
            ].includes(item.type) ||
            item.type.startsWith('yd-') ||
            item.type.startsWith('htTo');
          isType ? list.push(...item.children) : list.push(item);
        });
        return list;
      }
    },
    methods: {
      init() {
        this.formRules = this.getRules(this.componentList);
      },
      getRules(list) {
        let rules = {};
        list.forEach((item) => {
          if (this.noRuleTypes.includes(item.type)) {
            return;
          }
          let rule = [];
          if (item.valueJson.required) {
            if (this.inputRuleTypes.includes(item.type)) {
              rule = [
                { required: true, message: '请输入', trigget: 'blur' },
                NO_ALL_SPACE
              ];
            } else if (['image', 'file'].includes(item.type)) {
              rule.push({ type: 'array', required: true, message: '请上传' });
            } else if ('computed' === item.type) {
              rule.push({ required: true, validator: this.validateComputed });
            } else {
              rule.push({ required: true, message: '请选择' });
            }
          }
          if ('date' === item.type && item.valueJson.dateType === 2) {
            rule.push(DATE_AND_HALF_DAY);
          } else if ('idcard' === item.type) {
            rule.push(ID_CARD_NO_IMPORT);
          } else if ('phone' === item.type) {
            switch (item.valueJson.phoneType) {
              case 1:
                rule.push(PHONE);
                break;
              case 2:
                rule.push(TELEPHONE);
                break;
              case 3:
                rule.push(MOBILE_PHONE);
                break;
            }
          }
          if (item.type === 'region') {
            rules[`${item.id}.provinceCode`] = rule;
            rules[`${item.id}.street`] = [
              { required: true, message: '请输入', trigget: 'blur' },
              NO_ALL_SPACE
            ];
          } else if (item.type === 'inputMoney') {
            rules[`${item.id}.value`] = rule;
          } else {
            rules[item.id] = rule;
          }
        });
        return rules;
      },
      getComponentLabel(data) {
        if (['tips', 'desc'].includes(data.type)) {
          return ' ';
        }
        let { name, unit } = data.valueJson;
        if (['inputNumber', 'text'].includes(data.type) && unit) {
          return `${name}(${unit})：`;
        }
        return `${name}：`;
      },
      validateComputed(rule, value, callback) {
        callback(
          isNaN(parseInt(value, 10))
            ? new Error('编辑的计算公式为空或不符合计算法则，无法计算')
            : undefined
        );
      },
      validate() {
        return new Promise((resolve, reject) => {
          let list = [];
          if (this.$refs.formComp) {
            list = list.concat(this.$refs.formComp);
          }
          if (this.$refs.group) {
            list = list.concat(this.$refs.group);
          }
          Promise.all([
            this.validateForm(),
            ...Array.from(list, (item) => item.validate())
          ])
            .then(() => {
              resolve();
            })
            .catch(() => {
              reject();
            });
        });
      },
      validateForm() {
        return new Promise((resolve, reject) => {
          this.$refs.form.validate((valid) => {
            valid ? resolve() : reject();
          });
        });
      },
      validateField(props, callback) {
        this.$refs.form.validateField(props, callback);
      },
      clearValidate(props) {
        this.$refs.form.clearValidate(props);
      }
    }
  };
</script>

<style lang="scss">
  @import '@/styles/element-ui';

  .apply-components {
    &.el-form {
      .el-form-item {
        &.image-item {
          .el-form-item__content {
            line-height: 1;
          }
        }
      }
    }

    .apply-components {
      .el-row {
        position: unset;
      }
    }
  }
</style>
