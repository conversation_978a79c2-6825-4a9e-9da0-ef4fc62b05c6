.center-monitoring-container {
  display: grid;
  grid-template-rows: 78px 25% auto;
  height: 100%;
  padding: 0 20px;

  .summarizing {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;

    // gap: 20px;
    &-item {
      display: grid;
      grid-template-columns: 40% 60%;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 78px;
      background-image: url('./imgs/summarizing.png');
      background-repeat: no-repeat;
      background-position: center center;

      .summarizing-icon {
        width: 100%;
        height: 100%;
        background: url('./imgs/summarizing-icon.png');
        background-repeat: no-repeat;
        background-position: right center;
      }

      .summarizing-wrap {
        display: grid;
        grid-template-rows: 40% 60%;
        align-items: center;
        justify-content: center;
        width: 60%;

        span:first-child {
          color: #fff;
          font-weight: 400;
          font-size: 15px;
          font-family: "Microsoft YaHei";
        }

        span:last-child {
          color: #fff;
          font-weight: bold;
          font-size: 25px;
          font-family: "Source Han Sans CN";
          text-align: center;
          background: linear-gradient(180deg, #fbcf34 0%, #ff9b58 100%);
          background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }
    }
  }

  .abnormal-units{
    display: grid;
    grid-template-rows: 30px auto;
    margin-top: 20px;

    &-title{
      width: 100%;
      height: 34px;
      padding-left: 10px;
      color: #04edf9;

      // font-size: 16px;
      line-height: 34px;
      background-image: url('./imgs/center-title.png');
      background-repeat: no-repeat;
    }

    .abnormal-units-content{
      display: grid;
      grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr;

      .abnormal-units-item{
        position: relative;
        width: 100%;
        height: 100%;

        .units-title{
          position: absolute;
          top: 20px;
          left: 0;
          width: 100%;
          font-size: 16px;
          text-align: center;
        }

        .units-number{
          position: absolute;
          bottom: 20px;
          left: 0;
          z-index: 5;
          width: 100%;
          text-align: center;

          .number-item:first-child{
            z-index: 99;
            color: #ff0;
            font-weight: bold;
            font-size: 32px;
          }

          .number-item:last-child{
            color: #02a7f0;
          }
        }
      }
    }
  }

  .service-indicators{
    display: grid;
    grid-template-rows: 30px auto;
    margin-bottom: 10px;

    &-title{
      width: 100%;
      height: 34px;
      padding-left: 10px;
      color: #04edf9;

      // font-size: 16px;
      line-height: 34px;
      background-image: url('./imgs/center-title.png');
      background-repeat: no-repeat;
    }

    .service-indicators-content{
      display: grid;
      grid-template-rows: 1fr 1fr;
      grid-template-columns: 1fr 1fr;
      gap: 20px;
      margin-top: 20px;

      .service-indicators-item{
        display: grid;
        grid-template-rows: 30px auto;
        text-align: center;
        background-image: url('./imgs/border.png');

        // background-size: cover;
        background-size: 100% 100%;

        .content-title{
          width: 100%;
          height: 30px;

          // padding: 5px 20px;
          color: #fff;
          font-size: 16px;
          line-height: 30px;
          background: linear-gradient(90deg, transparent 0%, #0057b0 51%, transparent 100%);
        }

        .chart-wrap{
          height: 100%;

          // background-color: aquamarine;
        }
      }
    }
  }

  .left-content {
    display: grid;
    grid-template-rows: 1fr 1fr 1fr 1fr;
    gap: 10px;
    height: 94%;

    &-item {
      display: grid;
      grid-template-rows: 30px auto;
      text-align: center;
      background-image: url('./imgs/border.png');

      // background-size: cover;
      background-size: 100% 100%;

      .content-title {
        width: 100%;
        height: 30px;

        // padding: 5px 20px;
        color: #fff;
        font-size: 16px;
        line-height: 30px;
        background: linear-gradient(
          90deg,
          transparent 0%,
          #0057b0 51%,
          transparent 100%
        );
      }

      .chart-wrap {
        height: 100%;

        // background-color: aquamarine;
      }
    }
  }
}
