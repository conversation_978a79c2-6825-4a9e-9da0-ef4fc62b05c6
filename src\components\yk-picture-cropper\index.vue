<template>
  <div>
    <div
      class="user-info-head"
      :class="{ 'no-img': !imgBase, 'wrapper-circle': type === 'circle' }"
      @click="editCropper()"
    >
      <img v-bind:src="imgBase" class="img-lg" />
    </div>
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="800px"
      append-to-body
      @opened="modalOpened"
      @close="closeDialog"
    >
      <el-row>
        <el-col :xs="24" :md="12" :style="{ height: '350px' }">
          <vue-cropper
            ref="cropper"
            :img="defaultBase"
            :info="true"
            :autoCrop="options.autoCrop"
            :autoCropWidth="options.autoCropWidth"
            :autoCropHeight="options.autoCropHeight"
            :fixedBox="options.fixedBox"
            :outputType="options.outputType"
            @realTime="realTime"
            v-if="visible"
          />
        </el-col>
        <el-col :xs="24" :md="12" :style="{ height: '350px' }">
          <div
            class="avatar-upload-preview"
            :class="{ 'wrapper-circle': type === 'circle' }"
          >
            <img :src="previews.url" :style="previews.img" />
          </div>
        </el-col>
      </el-row>
      <br />
      <el-row>
        <el-col :lg="2" :sm="3" :xs="3">
          <el-upload
            action="#"
            :http-request="requestUpload"
            :show-file-list="false"
            :before-upload="beforeUpload"
          >
            <el-button>
              选择
              <i class="el-icon-upload el-icon--right"></i>
            </el-button>
          </el-upload>
        </el-col>
        <el-col :lg="{ span: 1, offset: 2 }" :sm="2" :xs="2">
          <el-button icon="el-icon-plus" @click="changeScale(1)"></el-button>
        </el-col>
        <el-col :lg="{ span: 1, offset: 1 }" :sm="2" :xs="2">
          <el-button icon="el-icon-minus" @click="changeScale(-1)"></el-button>
        </el-col>
        <el-col :lg="{ span: 1, offset: 1 }" :sm="2" :xs="2">
          <el-button
            icon="el-icon-refresh-left"
            @click="rotateLeft()"
          ></el-button>
        </el-col>
        <el-col :lg="{ span: 1, offset: 1 }" :sm="2" :xs="2">
          <el-button
            icon="el-icon-refresh-right"
            @click="rotateRight()"
          ></el-button>
        </el-col>
        <el-col :lg="{ span: 2, offset: 6 }" :sm="2" :xs="2">
          <el-button type="primary" @click="uploadImg">提 交</el-button>
        </el-col>
      </el-row>
    </el-dialog>
  </div>
</template>

<script>
  // import { VueCropper } from 'vue-cropper';
  import { imgUpload } from '@/api/desk/im';
  import { debounce } from 'lodash';

  export default {
    // components: { VueCropper },
    props: {
      type: {
        type: String,
        default: 'square'
      },
      imgBase: {
        type: String
      }
    },
    model: {
      prop: 'imgBase',
      event: 'change'
    },
    data() {
      return {
        // 是否显示弹出层
        open: false,
        // 是否显示cropper
        visible: false,
        // 弹出层标题
        title: '图片裁剪',
        defaultBase: undefined, //初始/选择图片的base64
        options: {
          autoCrop: true, // 是否默认生成截图框
          autoCropWidth: 200, // 默认生成截图框宽度
          autoCropHeight: 200, // 默认生成截图框高度
          fixedBox: true, // 固定截图框大小 不允许改变
          outputType: 'png' // 默认生成截图为PNG格式
        },
        previews: {},
        resizeHandler: null
      };
    },
    methods: {
      // 编辑头像
      editCropper() {
        this.open = true;
        this.defaultBase = this.imgBase;
      },
      // 打开弹出层结束时的回调
      modalOpened() {
        this.visible = true;
        if (!this.resizeHandler) {
          this.resizeHandler = debounce(() => {
            this.refresh();
          }, 100);
        }
        window.addEventListener('resize', this.resizeHandler);
      },
      // 刷新组件
      refresh() {
        this.$refs.cropper.refresh();
      },
      // 覆盖默认的上传行为
      requestUpload() {},
      // 向左旋转
      rotateLeft() {
        this.$refs.cropper.rotateLeft();
      },
      // 向右旋转
      rotateRight() {
        this.$refs.cropper.rotateRight();
      },
      // 图片缩放
      changeScale(num) {
        num = num || 1;
        this.$refs.cropper.changeScale(num);
      },
      // 将blob转为uri
      blobToDataURI(blob, callback) {
        const reader = new FileReader();
        reader.readAsDataURL(blob);
        reader.onload = () => {
          callback(reader.result);
        };
      },
      // 上传预处理
      beforeUpload(file) {
        if (file.type.indexOf('image/') == -1) {
          this.$message.error(
            '文件格式错误，请上传图片类型,如：JPG，PNG后缀的文件。'
          );
        } else {
          this.blobToDataURI(file, (res) => {
            this.defaultBase = res;
          });
        }
      },
      // 上传图片
      uploadImg() {
        this.$refs.cropper.getCropBlob((data) => {
          let formData = new FormData();
          formData.append('file', data);
          imgUpload(formData).then((res) => {
            this.open = false;
            const data = res.data.data;
            console.log(data);
            this.$emit('change', data.link);
            this.visible = false;
          });
        });
      },
      // 实时预览
      realTime(data) {
        this.previews = data;
      },
      // 关闭窗口
      closeDialog() {
        this.visible = false;
        window.removeEventListener('resize', this.resizeHandler);
      }
    }
  };
</script>
<style scoped lang="scss">
  img:not([src]) {
    opacity: 0;
  }

  .user-info-head {
    position: relative;
    display: inline-block;
    height: 120px;
    text-align: center;

    &.no-img,
    &:hover {
      &::after {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        color: #eee;
        font-size: 24px;
        font-style: normal;
        line-height: 110px;
        background: black;
        cursor: pointer;
        opacity: 0.5;
        content: '+';
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
      }
    }

    &.wrapper-circle {
      &.no-img,
      &:hover {
        &::after {
          border-radius: 50%;
        }
      }

      .img-lg {
        border-radius: 50%;
      }
    }

    .img-lg {
      width: 120px;
      height: 120px;
    }
  }

  .avatar-upload-preview {
    position: absolute;
    top: 50%;
    width: 200px;
    height: 200px;
    overflow: hidden;
    box-shadow: 0 0 4px #ccc;
    transform: translate(50%, -50%);

    &.wrapper-circle {
      border-radius: 50%;
    }
  }
</style>
