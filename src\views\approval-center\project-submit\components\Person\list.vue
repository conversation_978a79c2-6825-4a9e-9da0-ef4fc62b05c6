<template>
  <el-table
    v-loading="loading"
    :data="arr"
    height="420"
    style="width: 100%"
    header-cell-class-name="headerTh"
    row-class-name="rowClassName"
  >
    <el-table-column type="index" label="序号" align="center" width="50" />
    <el-table-column
      prop="attributeName"
      label="专家属性"
      width="75"
      align="center"
      show-overflow-tooltip
    />
    <el-table-column align="center" label="分组" min-width="75">
      <template slot-scope="{ row }">
        <el-tag
          size="mini"
          style="margin: 2px"
          v-for="(item, index) of row.groupPositionVoList || []"
          :key="index"
          >{{ `${item.groupName}-${item.positionName}` }}</el-tag
        >
      </template>
    </el-table-column>
    <el-table-column
      prop="jobNo"
      label="工号"
      width="90"
      align="center"
      show-overflow-tooltip
    >
      <template slot-scope="{ row }">
        {{ row.jobNo || '--' }}
      </template>
    </el-table-column>
    <el-table-column
      prop="userName"
      label="姓名"
      width="75"
      align="center"
      show-overflow-tooltip
    />
    <el-table-column
      prop="orgName"
      label="所属单位"
      align="center"
      width="90"
      show-overflow-tooltip
    >
      <template slot-scope="{ row }">
        {{ row.orgName || '--' }}
      </template>
    </el-table-column>
    <el-table-column align="center" label="是否选择" fixed="right" width="75">
      <template slot-scope="scope">
        <el-switch
          :disabled="scope.row.isDisabled"
          v-model="scope.row.status"
          @click.native="stopDefault($event)"
          @change="() => $emit('selectEmit', scope.row, scope.row.status)"
        >
        </el-switch>
      </template>
    </el-table-column>
    <el-table-column align="center" label="是否线上" fixed="right" width="75">
      <template slot-scope="scope">
        <el-switch
          :disabled="!scope.row.status || scope.row.isDisabled"
          v-model="scope.row.onlineStatus"
          @click.native="stopDefault($event)"
          @change="
            () => $emit('selectOnline', scope.row, scope.row.onlineStatus)
          "
        >
        </el-switch>
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
  export default {
    name: 'list',
    props: {
      arr: {
        type: Array,
        default: function () {
          return [];
        }
      },
      loading: {
        type: Boolean,
        default: false
      }
    },
    methods: {
      stopDefault(e) {
        e.stopPropagation();
      }
    }
  };
</script>
<style lang="scss" scoped>
  ::v-deep .headerTh {
    color: #515a6e !important;
    background-color: #f8f8f9 !important;
  }

  // ::v-deep .rowClassName {
  //   cursor: pointer;
  // }
</style>
