<template>
  <div class="right-monitoring-container">
    <div class="right-search">
      <div class="search-item">
        <span>排名：</span>
        <el-select
          v-model="form.year"
          filterable
          placeholder="请选择"
          style="width: 100px"
          clearable
        >
          <el-option
            v-for="dict in yearList"
            :key="dict.key"
            :label="dict.name"
            :value="dict.key"
          ></el-option>
        </el-select>
      </div>
    </div>
    <div class="right-content">
      <div class="right-content-item">
        <div class="content-title">登录人数</div>
        <div class="chart-wrap">
          <BarCrosswiseEchart></BarCrosswiseEchart>
        </div>
      </div>
      <div class="right-content-item">
        <div class="content-title">登录率</div>
        <div class="chart-wrap">
          <BarCrosswiseEchart></BarCrosswiseEchart>
        </div>
      </div>
      <div class="right-content-item">
        <div class="content-title">活跃人数</div>
        <div class="chart-wrap">
          <BarCrosswiseEchart></BarCrosswiseEchart>
        </div>
      </div>
      <div class="right-content-item">
        <div class="content-title">活跃率</div>
        <div class="chart-wrap">
          <BarCrosswiseEchart></BarCrosswiseEchart>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
  import { BarCrosswiseEchart } from './components';
  export default {
    components: { BarCrosswiseEchart },
    name: 'normalize-monitoring',
    data() {
      return {
        form: {},
        yearList: []
      };
    },
    methods: {}
  };
</script>

<style scoped lang="scss">
  @import './css/right';
</style>
