import request from '@/router/axios';

export const getList = (current, size, params, deptId) => {
  return request({
    url: '/api/szyk-user/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
      deptId
    }
  });
};
// 漏洞-列表
export const getAccountList = (current, size, params, deptId) => {
  return request({
    url: '/api/account/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
      deptId
    }
  });
};

export const remove = (ids) => {
  return request({
    url: '/api/szyk-user/remove',
    method: 'post',
    params: {
      ids
    }
  });
};

export const add = (row) => {
  return request({
    url: '/api/szyk-user/submit',
    method: 'post',
    data: row
  });
};

export const update = (row) => {
  return request({
    url: '/api/szyk-user/update',
    method: 'post',
    data: row
  });
};
// 漏洞-编辑
export const accountUpdate = (row) => {
  return request({
    url: '/api/account/update',
    method: 'post',
    data: row
  });
};

export const updatePlatform = (userId, userType, userExt) => {
  return request({
    url: '/api/szyk-user/update-platform',
    method: 'post',
    params: {
      userId,
      userType,
      userExt
    }
  });
};

export const getUser = (id) => {
  return request({
    url: '/api/szyk-user/detail',
    method: 'get',
    params: {
      id
    }
  });
};
// 漏洞-详情
export const getAccountUser = (id) => {
  return request({
    url: '/api/account/detail',
    method: 'get',
    params: {
      id
    }
  });
};
// 获取业务范围
export const getBusinessScope = (params) => {
  return request({
    url: '/api/szyk-system/dept/getDeptScope',
    method: 'get',
    params
  });
};
// 切换用户范围
export const changeScope = (row) => {
  return request({
    url: '/api/szyk-auth/oauth/changeScope',
    method: 'post',
    params: row
  });
};

export const getUserPlatform = (id) => {
  return request({
    url: '/api/szyk-user/platform-detail',
    method: 'get',
    params: {
      id
    }
  });
};

export const getUserInfo = () => {
  return request({
    url: '/api/szyk-user/info',
    method: 'get'
  });
};

export const resetPassword = (userIds) => {
  return request({
    url: '/api/szyk-user/reset-password',
    method: 'post',
    params: {
      userIds
    }
  });
};

export const updatePassword = (oldPassword, newPassword, newPassword1) => {
  return request({
    url: '/api/szyk-user/update-password',
    method: 'post',
    params: {
      oldPassword,
      newPassword,
      newPassword1
    }
  });
};

export const updateInfo = (row) => {
  return request({
    url: '/api/szyk-user/update-info',
    method: 'post',
    data: row
  });
};

export const grant = (userIds, roleIds) => {
  return request({
    url: '/api/szyk-user/grant',
    method: 'post',
    params: {
      userIds,
      roleIds
    }
  });
};

export const unlock = (userIds) => {
  return request({
    url: '/api/szyk-user/unlock',
    method: 'post',
    params: {
      userIds
    }
  });
};

// 根据部门ID查询用户列表
export const userListBydeptId = (deptId) => {
  return request({
    url: `/api/szyk-user/dept/list?deptId=${deptId}`,
    method: 'get'
  });
};
// 根据多个部门ID查询责任人
export const getUsersList = (data) => {
  return request({
    url: `/api/zbusiness-task/task/getUsers`,
    method: 'post',
    data
  });
};
// 统一认证用户的停用
export const updateIamUserStatus = (data) => {
  return request({
    url: `/api/system/updateIamUserStatus`,
    method: 'post',
    data
  });
};

// 根据部门ID查询单位的用户列表
export const getListUnitUserByDeptId = (deptId) => {
  return request({
    url: `/api/szyk-user/listUnitUserByDeptId?deptId=${deptId}`,
    method: 'get'
  });
};
