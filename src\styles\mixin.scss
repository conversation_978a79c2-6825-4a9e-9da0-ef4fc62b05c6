@mixin clearfix {
    &::after {
        display: table;
        clear: both;
        content: "";
    }
}

/* stylelint-disable-next-line scss/at-mixin-pattern */
@mixin scrollBar {
     ::-webkit-scrollbar-track-piece {
        background-color: transparent;
    }

     ::-webkit-scrollbar {
        width: 15px;
        height: 15px;

        // margin-top: 3px;
        background-color: transparent;
    }

     ::-webkit-scrollbar-thumb {
        background-color: hsla(220deg, 4%, 58%, 30%);
        border-radius: 5px;
    }
}

@mixin radius($width, $size, $color) {
    width: $width;
    height: $width;
    line-height: $width;
    text-align: center;
    border-color: $color;
    border-style: solid;
    border-width: $size;
    border-radius: $width;
}

@mixin relative {
    position: relative;
    width: 100%;
    height: 100%;
}

@mixin pct($pct) {
    position: relative;
    width: #{$pct};
    margin: 0 auto;
}

@mixin triangle($width, $height, $color, $direction) {
    $width: $width / 2;
    $color-border-style: $height solid $color;
    $transparent-border-style: $width solid transparent;
    width: 0;
    height: 0;
    @if $direction == up {
        border-bottom: $color-border-style;
        border-left: $transparent-border-style;
        border-right: $transparent-border-style;
    } @else if $direction == right {
        border-left: $color-border-style;
        border-top: $transparent-border-style;
        border-bottom: $transparent-border-style;
    } @else if $direction == down {
        border-top: $color-border-style;
        border-left: $transparent-border-style;
        border-right: $transparent-border-style;
    } @else if $direction == left {
        border-right: $color-border-style;
        border-top: $transparent-border-style;
        border-bottom: $transparent-border-style;
    }
}
