<template>
  <div class="DeptTranferbox">
    <div class="conbox">
      <div class="all-select">
        <el-checkbox
          v-if="!isRadio"
          v-model="checkAll"
          @change="handleCheckAllChange"
          :indeterminate="indeterminate"
          >全选</el-checkbox
        >
        <span v-else>选择区</span>
      </div>
      <div class="titbox">
        <el-input
          v-model="filterText"
          placeholder="搜索"
          suffix-icon="el-icon-search"
        ></el-input>
      </div>
      <div class="wordbox">
        <el-tree
          ref="tree"
          @check="getData"
          show-checkbox
          class="filter-tree"
          node-key="id"
          default-expand-all
          :data="treeData"
          :props="defaultProps"
          :filter-node-method="filterNode"
          :check-strictly="checkStrictly"
        >
          <span slot-scope="{ node, data }" class="custom-tree-node">
            <span>{{ node.hasChildren }}</span>
            <img v-if="data.dataType === 1" v-oss src="/apply/oa-people.png" />
            <div v-if="data.dataType === 3">
              <div class="avatar">
                <img v-if="data.avatar" :src="data.avatar" />
                <img v-else v-oss src="/launch/default-photo.png" />
              </div>
            </div>
            <span class="tree-label">{{
              radio === '3'
                ? data.title
                : radio === '2'
                ? data.jobName
                : radio === '4'
                ? data.postName
                : data.label
            }}</span>
          </span>
        </el-tree>
      </div>
    </div>
    <div class="conbox">
      <div class="all-select">
        <div>已选 {{ checkedIds.length }} {{ radioName[radio] }}</div>
      </div>
      <div class="wordbox select-box">
        <div class="dept-selected">
          <div v-for="(item, index) in checkedIds" :key="index">
            <div class="inli">
              <img
                v-if="item.dataType === 1"
                v-oss
                src="/apply/oa-people.png"
              />
              <div v-if="item.dataType === 3">
                <div class="avatar">
                  <img v-if="item.avatar" :src="item.avatar" />
                  <img v-else v-oss src="/launch/default-photo.png" />
                </div>
              </div>
              <span class="tree-label">{{
                item[
                  radio === '4'
                    ? 'postName'
                    : radio === '3'
                    ? 'title'
                    : radio === '2'
                    ? 'jobName'
                    : 'label'
                ]
              }}</span>
              <i @click="removeData(item)" class="iconfont icon-default"></i>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
  export default {
    props: {
      // 当前选中人的id
      checkedList: {
        type: Array,
        default: () => []
      },
      // 当前接口类型， 1 人员，2职务， 3部门 4岗位
      radio: {
        type: String,
        default: '1'
      },
      // 当前是否为单选
      isRadio: {
        type: Boolean,
        default: false
      },
      // 当前数据集合
      treeData: {
        type: Array,
        default: () => []
      },
      // 父子是否强关联
      checkStrictly: {
        type: Boolean,
        default: false
      },
      // 选择的最大数量，0为不限制数量。 id不能重复
      maxSelectLength: {
        type: Number,
        default: 0
      },
      // 禁止选中某项
      exclude: {
        type: Array,
        default: () => []
      }
    },
    data() {
      return {
        radioName: ['', '人员', '职务', '部门', '岗位'],
        checkAll: false,
        indeterminate: false,
        checkedIds: [],
        filterText: '',
        noWatch: true,
        listBg: {},
        defaultProps: {
          children: 'children',
          label: 'label',
          isLeaf: 'hasChildren',
          disabled: (data) => {
            return (
              // 单选时而且是人的话屏蔽部门
              (this.radio === '1' && data.dataType !== 3 && this.isRadio) ||
              // 选项等于最大可选数量时屏蔽
              (this.maxSelectLength &&
                this.checkedIds.length >= this.maxSelectLength) ||
              // 按成员选择时用id判断
              this.exclude.includes(data.id) ||
              // 按岗位职务选择时用userId判断
              this.exclude.includes(data.userId)
            );
          }
        }
      };
    },
    watch: {
      filterText(val) {
        this.$refs.tree.filter(val);
      },
      checkedIds() {
        this.indeterminate = !!this.checkedIds.length;
        const checkList = this.$refs.tree.getCheckedNodes();
        const list = this.getAllNode(this.treeData);
        if (checkList.length && checkList.length === list.length) {
          this.indeterminate = false;
          this.checkAll = true;
        }
        this.$emit('setCheckIds', this.checkedIds);
      },
      treeData() {
        // 首次初始化 mounted会修改数据，此时不触发watch
        if (this.noWatch) {
          this.noWatch = false;
          return;
        }
        this.checkAll = false;
        this.indeterminate = false;
        this.filterText = '';
        this.setTree();
      }
    },
    mounted() {
      this.setTree();
    },
    methods: {
      getAllNode(list) {
        let temp = [];
        const getNode = (array) => {
          array.forEach((item) => {
            // !item.hasChildren 不加这个判断,排除某些人时exclude全选人不能用
            // 多选时人全选中上级部门自然就选中
            // 选部门时hasChildren应该为false
            if (!this.exclude.includes(item.id) && !item.hasChildren) {
              temp.push(item);
            }
            if (item.children && item.children.length) {
              getNode(item.children);
            }
          });
        };
        getNode(list);
        return temp;
      },
      setCheckIds(list) {
        this.selectList = list;
      },
      setTree() {
        if (!this.treeData.length) {
          return;
        }
        if (
          this.checkedList.length &&
          typeof this.checkedList[0] === 'string'
        ) {
          this.$refs.tree.setCheckedKeys(this.checkedList);

          this.$nextTick(() => {
            this.getData();
          });
        } else {
          this.checkedIds = [];
          this.$refs.tree.setCheckedKeys([]);
          this.$refs.tree.setCheckedNodes([]);
        }
      },
      // 全选
      handleCheckAllChange() {
        this.checkedIds = [];
        if (this.checkAll) {
          let allchecked = this.getAllNode(this.treeData);
          this.$refs.tree.setCheckedNodes(allchecked);
          let checkList = this.$refs.tree.getCheckedNodes();

          // 岗位职务选人id可能重复，需要把重复的人去掉
          let ids = this.checkStrictly
            ? checkList
            : checkList.filter(
                (c) =>
                  (this.radio !== '1' && !c.hasChildren) || c.dataType === 3
              );
          ids.forEach((l) => {
            if (l.userId) {
              const isIdLength = this.checkedIds.filter(
                (c) => c.userId === l.userId
              ).length;
              !isIdLength && this.checkedIds.push(l);
            } else {
              this.checkedIds.push(l);
            }
          });

          if (
            this.maxSelectLength &&
            this.checkedIds.length > this.maxSelectLength
          ) {
            this.checkedIds = this.checkedIds.slice(0, this.maxSelectLength);
            this.$refs.tree.setCheckedNodes(this.checkedIds);
            this.$message.error('可选项已达上限');
          }
        } else {
          this.$refs.tree.setCheckedKeys([]);
          this.$refs.tree.setCheckedNodes(this.checkedIds);
        }
      },
      filterNode(value, data) {
        if (!value) {
          return true;
        }
        const label =
          this.radio === '4'
            ? 'postName'
            : this.radio === '3'
            ? 'title'
            : this.radio === '2'
            ? 'jobName'
            : 'label';
        return data[label].indexOf(value) !== -1;
      },
      // 控制单选
      theRadio(data) {
        this.$refs.tree.setCheckedNodes([]);
        this.$refs.tree.setCheckedNodes([data]);
      },
      // 点击选择
      getData(data) {
        this.checkedIds = [];
        this.isRadio && data && this.theRadio(data);
        let checkList = this.$refs.tree.getCheckedNodes();

        // 岗位职务选人id可能重复，需要把重复的人去掉
        let ids = this.checkStrictly
          ? checkList
          : checkList.filter(
              (c) => (this.radio !== '1' && !c.hasChildren) || c.dataType === 3
            );
        ids.forEach((l) => {
          if (l.userId) {
            const isIdLength = this.checkedIds.filter(
              (c) => c.userId === l.userId
            ).length;
            if (!isIdLength) {
              this.checkedIds.push(l);
            }
          } else {
            this.checkedIds.push(l);
          }
        });

        if (
          this.maxSelectLength &&
          this.checkedIds.length > this.maxSelectLength
        ) {
          this.checkedIds = this.checkedIds.slice(0, this.maxSelectLength);
          this.$refs.tree.setCheckedNodes(this.checkedIds);
          this.$message.error('可选项已达上限');
        }

        let treeDataList = this.getAllNode(this.treeData);
        this.checkAll = this.checkedIds.length === treeDataList.length;
      },
      clearData() {
        this.checkedIds = [];
        this.checkAll = false;
        this.indeterminate = false;
        this.$refs.tree.setCheckedKeys([]);
        this.$refs.tree.setCheckedNodes([]);
      },
      // 删除已选部门
      removeData(data) {
        this.checkedIds = this.checkedIds.filter(({ id }) => id !== data.id);
        this.$refs.tree.setCheckedNodes(this.checkedIds);
        let treeDataList = this.getAllNode(this.treeData);
        this.checkAll = this.checkedIds.length === treeDataList.length;
      }
    }
  };
</script>

<style lang="scss">
  .DeptTranferbox {
    display: flex;
    justify-content: space-between;
    padding: 12px 24px 0;

    .el-tree {
      padding: 4px 0;
    }

    .el-tree-node {
      div {
        margin: 0;
      }
    }

    .is-leaf {
      background: #fff !important;
    }

    .el-tree-node__content {
      height: auto !important;
      margin: 6px 0;
      padding: 9px 4px !important;

      .el-icon-caret-right {
        padding: 0 7px 0 6px;
      }

      .tree-label {
        line-height: 14px !important;
      }

      &:hover {
        .is-leaf {
          background: #f5f7fa !important;
        }
      }
    }

    .avatar {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 30px;
      height: 30px;
      margin-left: 12px;
      color: #fff;
      font-size: 14px;
      border-radius: 50%;

      img {
        width: 100% !important;
        height: 100% !important;
        margin: 0 !important;
        border-radius: 50%;
      }
    }

    .conbox {
      display: flex;
      flex-direction: column;
      width: 240px;
      height: 388px;
      border: 1px solid #d9d9d9;
      border-radius: 4px;

      .all-select {
        padding: 12px 16px;
        color: #333;
        font-weight: 400;
        font-size: 14px;
        line-height: 19px;
        border-bottom: 1px solid #d9d9d9;
      }

      .titbox {
        padding: 12px 16px 0;
      }

      .wordbox {
        flex: 1;
        overflow-y: auto;

        .custom-tree-node {
          position: relative;
          display: flex;
          align-items: center;
          width: 100%;
          width: 150px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;

          img {
            width: 16px;
            height: 16px;
            margin-right: 4px;
          }

          .icon-tree-depart {
            width: 16px;
            height: 16px;
            margin-right: 4px;
            margin-left: 4px;
          }

          .tree-label {
            display: flex;
            display: flow-root;
            align-items: center;
            margin-left: 8px;
            overflow: hidden;
            color: #333;
            font-weight: 400;
            font-size: 14px;
            line-height: 23px;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
        }
      }

      .dept-selected {
        padding-top: 4px;

        .icon-default {
          color: #a0a0a0;
          cursor: pointer;

          &:hover {
            color: #6a6a6a;
          }
        }

        .tree-label {
          display: inline-block;
          width: 108px;
          margin-left: 12px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }

        .inli {
          position: relative;
          display: flex;
          align-items: center;
          padding: 9px 16px 9px 4px;

          &:hover {
            background: #f5f7fa;
          }

          img {
            width: 16px;
            height: 16px;
            margin-right: 4px;
          }

          span {
            color: #333;
            font-weight: 400;
            font-size: 14px;
          }

          i {
            position: absolute;
            right: 16px;
          }
        }
      }
    }
  }
</style>
