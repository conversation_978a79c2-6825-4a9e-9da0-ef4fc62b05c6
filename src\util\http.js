// import qs from 'qs'
import axios from 'axios';
import store from '../store';
// import { toRawType } from '@/utils'
import { baseUrl } from '@/config/env.js';
// import { getLocalToken } from './local';
import { getToken } from './auth';
// import { Base64 } from 'js-base64';
import website from '@/config/website';

const service = axios.create({
  baseURL: baseUrl,
  method: 'post',
  headers: { 'Content-Type': 'application/json;charset=UTF-8' }
});

service.interceptors.request.use((config) => {
  // console.log('config', config) // eslint-disable-line
  // store.dispatch('app/setLoading', true);
  // const token = getLocalToken();
  // let { data = {} } = config
  // config.headers.token = token;
  // config.headers.Authorization = token;
  config.headers['Captcha-Key'] = config.data.key;
  config.headers['Captcha-Code'] = config.data.code;
  config.headers['Szyk-Auth'] = config.data.code;
  config.headers['Szyk-Auth'] = `bearer ${getToken()}`;
  config.headers['Authorization'] = `Basic ${website.correspondentSec}`;
  return config;
});

service.interceptors.response.use(
  (response) => {
    store.dispatch('app/setLoading', false);
    return response;
  },
  () => {
    store.dispatch('app/setLoading', false);
    // console.log(err)  // eslint-disable-line
    // return Promise.reject(err);
  }
);

export default service;
