import request from '@/router/axios';

// 批量 添加分页查询
export const getGroupTree = (params) => {
  return request({
    url: '/api/zbusiness-project/personexpertgroup/tree',
    method: 'get',
    params
  });
};

// 保存专家库分组
export const saveGroup = (data) => {
  return request({
    url: '/api/zbusiness-project/personexpertgroup/save',
    method: 'post',
    data
  });
};

// 删除专家库分组
export const delGroup = (data) => {
  return request({
    url: '/api/zbusiness-project/personexpertgroup/delete',
    method: 'post',
    data
  });
};

// 获取专家库列表
export const getExpertList = (params) => {
  return request({
    url: '/api/zbusiness-project/personexpert/page',
    method: 'get',
    params
  });
};

// 漏洞-列表
export const expertUserPage = (params) => {
  return request({
    url: '/api/account/expertUserPage',
    method: 'get',
    params: {
      ...params
    }
  });
};

// 删除专家库分组
export const saveExpert = (data) => {
  return request({
    url: '/api/zbusiness-project/personexpert/save',
    method: 'post',
    data
  });
};

// 获取专家库详情
export const getExpertDetail = (params) => {
  return request({
    url: '/api/zbusiness-project/personexpert/detail',
    method: 'get',
    params
  });
};

// 专家库分组启用停用
export const setExpertStatus = (data) => {
  return request({
    url: '/api/zbusiness-project/personexpert/status',
    method: 'post',
    data
  });
};

// 专家库删除
export const setExpertDel = (data) => {
  return request({
    url: '/api/zbusiness-project/personexpert/delete',
    method: 'post',
    data
  });
};

// 单个项目一键转发
export const oneProjectSend = (data) => {
  return request({
    url: '/api/zbusiness-project/projectexpert/oneProjectSend',
    method: 'post',
    data
  });
};

// 多个项目一键转发
export const manyProjectSend = (data) => {
  return request({
    url: '/api/zbusiness-project/projectexpert/manyProjectSend',
    method: 'post',
    data
  });
};

// 专家评审-查看评审意见list
export const commentViewList = (params) => {
  return request({
    url: '/api/zbusiness-project/projectexpert/commentViewList',
    method: 'get',
    params
  });
};

// 专家评审列表
export const projectexpertList = (data) => {
  return request({
    url: '/api/zbusiness-project/projectexpert/page',
    method: 'post',
    data
  });
};

// 批量添加评审意见
export const batchExpertComment = (data) => {
  return request({
    url: '/api/zbusiness-project/projectexpert/batchExpertComment',
    method: 'post',
    data
  });
};

// 更新评审意见
export const updaterExpertComment = (data) => {
  return request({
    url: '/api/zbusiness-project/projectexpert/updaterExpertComment',
    method: 'post',
    data
  });
};

// 专家评审详情
export const commentViewDetail = (params) => {
  return request({
    url: '/api/zbusiness-project/projectexpert/getCommentDetailByAssignId',
    method: 'get',
    params
  });
};

// 专家已读-修改已读的状态
export const expertRead = (params) => {
  return request({
    url: '/api/zbusiness-project/projectexpert/expertRead',
    method: 'get',
    params
  });
};
