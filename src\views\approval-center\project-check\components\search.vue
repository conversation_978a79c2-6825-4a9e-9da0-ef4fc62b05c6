<template>
  <div>
    <el-form ref="form" :model="queryParams" :inline="true" label-suffix="：">
      <!-- <el-form-item label="流程名称" prop="flowName">
        <el-input
          v-model="queryParams.flowName"
          style="width: 120px"
          placeholder="请输入"
          clearable
          :maxlength="20"

        />
      </el-form-item> -->
      <el-form-item label="项目名称" prop="projectName">
        <el-input
          v-model="queryParams.projectName"
          placeholder="请输入"
          clearable
          style="width: 120px"
          :maxlength="20"
        />
      </el-form-item>
      <el-form-item
        v-if="activeName !== '2'"
        label="发起人"
        prop="applyUserName"
      >
        <el-input
          v-model="queryParams.applyUserName"
          placeholder="请输入"
          style="width: 120px"
          clearable
          :maxlength="20"
        />
      </el-form-item>
      <el-form-item label="发起时间" prop="applyTime">
        <el-date-picker
          v-model="queryParams.applyTime"
          type="daterange"
          style="width: 240px"
          align="right"
          range-separator="至"
          value-format="yyyy-MM-dd HH:mm:ss"
          :default-time="['00:00:00', '23:59:59']"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="当前审核状态" prop="curStatus">
        <el-select
          v-model="queryParams.curStatus"
          filterable
          placeholder="请选择"
          style="width: 100px"
          clearable
        >
          <el-option
            v-for="dict in labelList"
            :key="dict.dictKey"
            :label="dict.dictValue"
            :value="dict.dictKey"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-delete" @click="resetQuery">清空</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
  import { getDictList } from '@/api/common.js';
  const query = {
    flowName: '',
    applyUserName: '',
    projectName: '',
    curStatus: '',
    applyTime: []
  };
  export default {
    name: 'ProjectSubmitSearch',
    props: {
      activeName: {
        type: String,
        default: ''
      }
    },
    data() {
      return {
        queryParams: { ...query },
        unfold: false,
        loading: false,
        labelList: []
      };
    },
    mounted() {
      this.getLabel();
    },
    methods: {
      async getLabel() {
        try {
          const {
            data: { data }
          } = await getDictList({ code: 'review_status' }); // dictStatus 0 启用 1 停用
          this.labelList = data || [];
        } catch (e) {
          console.error(e);
        }
      },
      // 重置
      resetQuery() {
        this.$refs['form'].resetFields();
        this.handleQuery(1);
      },
      // 查询
      handleQuery(isResetQuery) {
        let { applyTime } = this.queryParams;
        let applyStartTime = undefined;
        let applyEndTime = undefined;
        if (Array.isArray(applyTime) && applyTime.length) {
          applyStartTime = applyTime[0];
          applyEndTime = applyTime[1];
        }
        let query = {
          ...this.queryParams,
          applyStartTime,
          applyEndTime,
          applyTime: undefined,
          isResetQuery
        };
        this.$emit('search', query);
      }
    }
  };
</script>
<style lang="scss"></style>
