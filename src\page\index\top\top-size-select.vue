<template>
  <el-dropdown trigger="click" @command="handleSetSize">
    <div>
      <yk-svg-icon icon-class="size" />
    </div>
    <el-dropdown-menu slot="dropdown">
      <el-dropdown-item
        v-for="item of sizeOptions"
        :key="item.value"
        :disabled="size === item.value"
        :command="item.value"
      >
        {{ item.label }}
      </el-dropdown-item>
    </el-dropdown-menu>
  </el-dropdown>
</template>

<script>
  import { mapMutations } from 'vuex';
  export default {
    name: 'top-size-select',
    data() {
      return {
        sizeOptions: [
          { label: '大号', value: 'default' },
          { label: '中等', value: 'medium' },
          { label: '默认', value: 'small' },
          { label: '小号', value: 'mini' }
        ]
      };
    },
    computed: {
      size() {
        return this.$store.getters.size;
      }
    },
    methods: {
      ...mapMutations(['SET_SIZE', 'SET_ROUTER_KEY']),
      handleSetSize(size) {
        this.$ELEMENT.size = size;
        this.$AVUE.size = size;
        this.SET_SIZE(size);
        this.refreshView();
        this.$message({
          message: '切换成功！',
          type: 'success'
        });
      },
      refreshView() {
        // 通过重置全局routerKey, 刷新页面
        this.SET_ROUTER_KEY();
      }
    }
  };
</script>

<style scoped></style>
