<template>
  <el-select
    ref="select"
    style="width: 100%"
    :placeholder="placeholder"
    clearable
    v-model="menu"
    :disabled="disabled"
    popper-class="jkk"
    @change="handleChange"
  >
    <div style="max-height: 275px; overflow-y: auto">
      <div style="padding: 6px 10px">
        <el-input
          type="text"
          v-model.trim="filterName"
          placeholder="输入关键字进行过滤"
          autocomplete="off"
          clearable
        />
      </div>
      <el-option :value="optionValue" style="height: auto">
        <el-tree
          ref="tree"
          node-key="key"
          highlight-current
          :data="treeList"
          :props="defaultProps"
          :filter-node-method="filterNode"
          @node-click="nodeClick"
        ></el-tree>
      </el-option>
    </div>
  </el-select>
</template>

<script>
  import { getMenuTree } from '@/api/system/menu';

  export default {
    name: 'YkMenuSelect',
    props: {
      disabled: {
        type: Boolean,
        default: false
      },
      optionValue: {
        type: String,
        default: ''
      },
      placeholder: {
        type: String,
        default: '请选择菜单'
      },
      type: {
        type: String,
        default: 'PC'
      }
    },
    model: {
      prop: 'optionValue',
      event: 'change'
    },
    data() {
      return {
        defaultProps: {
          label: 'title'
        },
        menu: '',
        filterName: '',
        treeList: []
      };
    },
    watch: {
      filterName(newVal) {
        this.$refs.tree.filter(newVal);
      },
      optionValue() {
        const menuName = this.menuArr(this.treeList);
        this.menu = menuName;
      }
    },
    mounted() {
      this.init();
    },
    methods: {
      // scroll bug 处理
      initScroll() {
        this.$nextTick(() => {
          let scrollWrap = document.querySelectorAll(
            '.jkk .el-scrollbar .el-select-dropdown__wrap'
          );
          let scrollBar = document.querySelectorAll(
            '.jkk .el-scrollbar .is-vertical'
          );
          scrollWrap.forEach((ele) => {
            ele.style.cssText =
              'margin: 0px; max-height: none; overflow: hidden;';
          });
          scrollBar.forEach((ele) => (ele.style.width = 0));
        });
      },
      // 初始化数据请求
      async init() {
        try {
          const res = await getMenuTree(undefined, this.type);
          this.treeList = res.data.data;
          this.initScroll();
          const menuName = this.menuArr(this.treeList);
          this.menu = menuName;
        } catch (e) {
          console.error(e);
        }
      },
      // select change
      handleChange() {
        if (!this.menu.length) {
          this.$emit('change', '');
          this.$refs.tree.setCurrentKey(null);
        }
      },
      // 节点点击选中
      nodeClick({ title, value }) {
        this.$emit('change', value);
        this.menu = title;
        this.$refs.select.blur();
      },
      // 过滤节点
      filterNode(value, data) {
        if (!value) return true;
        return data.title.indexOf(value) !== -1;
      },
      // 拆分菜单结构
      menuArr(list = []) {
        if (this.optionValue === '') {
          return '';
        }
        let name = '';
        for (let i = 0; i < list.length; i++) {
          if (list[i].id === this.optionValue) {
            name = list[i].title;
            break;
          }
          if (list[i].hasChildren && !name.length) {
            const a = this.menuArr(list[i].children);
            if (a.length) {
              name = a;
              break;
            }
          }
        }
        return name;
      }
    }
  };
</script>
