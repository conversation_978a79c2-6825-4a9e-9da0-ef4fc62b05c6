<template>
  <div class="monitoring-container">
    <div class="monitoring-top">
      <span>系统监控</span>
      <span>信息技术管控常态化应用监控平台</span>
      <span>单位监控</span>
    </div>
    <div class="normalize-content">
      <div class="normalize-content-left">
        <LeftContent></LeftContent>
      </div>
      <div class="normalize-content-center">
        <CenterContent></CenterContent>
      </div>
      <div class="normalize-content-right">
        <RightContent></RightContent>
      </div>
    </div>
  </div>
</template>
<script>
  import LeftContent from './left';
  import CenterContent from './center';
  import RightContent from './right';
  export default {
    components: {
      LeftContent,
      CenterContent,
      RightContent
    },
    name: 'normalize-monitoring',
    data() {
      return {
        detail: {}
      };
    },
    methods: {}
  };
</script>

<style scoped lang="scss">
  @import './css/index';
</style>
