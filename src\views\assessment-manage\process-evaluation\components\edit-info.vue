<template>
  <div>
    <!-- v-if="formInfo.constructionUnitId" v-if="(isEdit && !id) || formInfo.schemeNo" -->
    <el-form
      ref="form"
      :rules="rules"
      :model="formInfo"
      label-width="170px"
      label-position="right"
      label-suffix="："
    >
      <el-row>
        <el-col :span="8" v-if="isEdit && id"
          ><el-form-item label="考核单号" prop="rwNo">
            {{ formInfo.rwNo || '--' }}</el-form-item
          ></el-col
        >
        <el-col :span="8"
          ><el-form-item label="发起组织" prop="initiateOrgId">
            <!-- v-model="formInfo.initiateOrgId" -->
            <el-select
              filterable
              :value="formInfo.initiateOrgId"
              ref="initiateOrg"
              placeholder="请选择"
              style="width: 100%"
              @change="handleInitiate"
            >
              <el-option
                v-for="dict in deliveryList"
                :key="dict.id"
                :label="dict.deptName"
                :value="dict.id"
              ></el-option>
            </el-select> </el-form-item
        ></el-col>
        <el-col :span="8"
          ><el-form-item label="考核周期" prop="periodName">
            <el-input
              placeholder="请选择"
              v-model="formInfo.periodName"
              @focus="dispatchHandle"
              suffix-icon="el-icon-arrow-down"
            >
            </el-input> </el-form-item
        ></el-col>
      </el-row>
      <el-row>
        <el-col :span="8"
          ><el-form-item label="被考核机构" prop="assessedOrgId">
            <!-- <el-select
              :value="formInfo.assessedOrgId"
              filterable
              ref="assessedOrg"
              placeholder="请选择"
              style="width: 100%"
              @change="handleInstitution"
            >
              <el-option
                v-for="dict in lowerDepartment"
                :key="dict.id"
                :label="dict.deptName"
                :value="dict.id"
              ></el-option>
            </el-select>  -->
            <!-- <InputTree
              v-model="formInfo.assessedOrgId"
              lazy
              :form="formInfo"
              :dic="deptData"
              style="width: 100%"
              :props="{
                label: 'title',
                value: 'id',
                isLeaf: 'isLeaf',
                formLabel: 'assessedOrgName',
                formValue: 'assessedOrgId'
              }"
              :load="lazyLoad"
              :lazyLoading="lazyLoading"
              @search="lazySearch"
            ></InputTree>  -->
            <!-- @change="assessedHandle" -->
            <InputTree
              v-model="formInfo.assessedOrgId"
              lazy
              :form="formInfo"
              :disabled="!!id"
              :samePort="true"
              :dic="deptData"
              style="width: 100%"
              :props="{
                label: 'title',
                value: 'id',
                isLeaf: 'isLeaf',
                formLabel: 'assessedOrgName',
                formValue: 'assessedOrgId'
              }"
              :load="lazyLoad"
              @change="handleInstitution"
              :lazyLoading="lazyLoading"
              @search="lazySearch"
            ></InputTree>
            <!-- <el-select
              :value="formInfo.assessedOrgId"
              filterable
              ref="assessedOrg"
              placeholder="请选择"
              style="width: 100%"
              @change="handleInstitution"
            >
              <el-option
                v-for="dict in assessedList"
                :key="dict.id"
                :label="dict.deptName"
                :value="dict.id"
              ></el-option>
            </el-select> -->
          </el-form-item></el-col
        >
        <el-col :span="8">
          <el-form-item label="考核日期" prop="rwDate">
            <el-date-picker
              style="width: 100%"
              value-format="yyyy-MM-dd"
              v-model="formInfo.rwDate"
              type="date"
              :clearable="false"
              placeholder="请选择"
            >
            </el-date-picker
          ></el-form-item>
        </el-col>
        <el-col :span="8"
          ><el-form-item label="体系名称" prop="schemeId">
            <el-select
              :value="formInfo.schemeId"
              filterable
              :disabled="!formInfo.initiateOrgId"
              ref="schemeOrg"
              placeholder="请选择"
              style="width: 100%"
              @change="handleClassification"
            >
              <el-option
                v-for="dict in systemNameList"
                :key="dict.id"
                :label="dict.schemeName"
                :value="dict.id"
              ></el-option>
            </el-select> </el-form-item
        ></el-col>

        <el-col :span="8"
          ><el-form-item label="来源单号" class="two-wrap" prop="associateId">
            <div style="width: 100%" v-if="detail.sourceType === '5'">
              {{ detail.sourceName || '/' }}
            </div>
            <template v-if="isEdit && detail.sourceType !== '5'">
              <el-select
                v-model="formInfo.sourceType"
                filterable
                :disabled="!formInfo.assessedOrgId"
                style="width: 120px"
                ref="company"
                clearable
                placeholder="无"
                @change="handleSourceType"
              >
                <el-option
                  v-for="dict in serviceDicts.type['source_type']"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
              <el-select
                v-model="formInfo.associateId"
                filterable
                ref="company"
                :disabled="!formInfo.sourceType"
                placeholder="请选择"
                style="width: 65%"
                @change="handleBill"
              >
                <el-option
                  v-for="dict in sourceList"
                  :key="dict.id"
                  :label="dict.approvalNo"
                  :value="dict.id"
                ></el-option>
              </el-select>
            </template> </el-form-item
        ></el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="备注" prop="remark">
            <el-input
              v-model="formInfo.remark"
              placeholder="请输入 200字以内"
              :maxlength="200"
              type="textarea"
              clearable
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="附件" prop="processFileIds">
            <h-upload
              ref="upload"
              v-model="formInfo.processFileIds"
              showFileList
              show-loading
              :disabled="
                !isEdit && formInfo.assessedOrgId === currentBusinessScope['id']
              "
              multiple
            >
              <el-button type="primary" icon="el-icon-upload"
                >上传文件
              </el-button>
            </h-upload>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row v-if="isEdit && id">
        <el-col :span="8">
          <el-form-item label="创建人" prop="createUserName">
            {{ formInfo.createUserName || '--' }}</el-form-item
          >
        </el-col>
        <el-col :span="8">
          <el-form-item label="创建日期" prop="createTime">
            {{ formInfo.createTime || '--' }}</el-form-item
          >
        </el-col>
      </el-row>
    </el-form>
    <AddAssessmentCycle
      ref="cycle"
      @saveSuccess="saveSuccess"
    ></AddAssessmentCycle>
  </div>
</template>
<script>
  import { formatTreeData } from '@/util/util';
  import { getDeptTree, getLazyDeptTree_3 } from '@/api/system/dept';
  import { getDeliveryList } from '@/api/project-dispatch';
  // import { getSourceNumber } from '@/api/assess-manage';
  import AddAssessmentCycle from '@/views/assessment-manage/evaluation-system/components/add-assessment-cycle.vue';
  import { mapGetters } from 'vuex';
  import { InputTree } from '@/components/yk-select-tree';
  import { getCurrentChild } from '@/api/assess-manage';
  import { getStore } from '@/util/store';
  export default {
    serviceDicts: ['source_type'],
    name: 'editInfo',
    components: {
      AddAssessmentCycle,
      InputTree
    },
    props: {
      isEdit: { type: Boolean, default: false },
      id: { type: String, default: '' },
      detail: {
        type: Object,
        default: () => {}
      },
      formInfo: {
        type: Object,
        default: () => {}
      },
      formMember: {
        type: Object,
        default: () => {}
      },
      lowerDepartment: {
        type: Array,
        default: () => []
      },
      systemNameList: {
        type: Array,
        default: () => []
      },
      sourceList: {
        type: Array,
        default: () => []
      },
      assessedList: {
        type: Array,
        default: () => []
      }
    },
    data() {
      return {
        rules: {
          initiateOrgId: [
            {
              required: true,
              message: '请选择发起组织',
              trigger: ['change']
            }
          ],
          assessedOrgId: [
            {
              required: true,
              message: '请选择被考核机构',
              trigger: ['change']
            }
          ],
          rwDate: [
            {
              required: true,
              message: '请选择考核日期',
              trigger: ['change']
            }
          ],
          schemeId: [
            {
              required: true,
              message: '请选择体系名称',
              trigger: ['change']
            }
          ],
          periodName: [
            {
              required: true,
              message: '请选择考核周期',
              trigger: ['blur', 'change']
            }
          ]
        },
        constructionList: [],
        deliveryList: [],
        props: {
          lazy: true,
          label: 'title',
          value: 'id',
          children: 'children',
          leaf: 'leaf',
          checkStrictly: true,
          emitPath: false,
          async lazyLoad(node, resolve) {
            const { data } = node;
            const { id } = data || {};
            let params = {
              tenantId: '000000',
              parentId: id || 0
            };
            const {
              data: { data: list }
            } = await getLazyDeptTree_3(params);
            // 通过调用resolve将子节点数据返回，通知组件数据加载完成
            list.forEach((item) => (item.leaf = !item.hasChildren));
            resolve(list || []);
          }
        },
        deptData: [],
        node: null,
        resolveFunc: null,
        lazyLoading: false
        // sourceList: []
      };
    },
    computed: {
      ...mapGetters(['permission', 'userInfo']),
      pickerOptions() {
        return {
          disabledDate(time) {
            return time.getTime() < Date.now() - 24 * 60 * 60 * 1000;
          }
        };
      },
      currentBusinessScope() {
        let org = getStore({ name: 'current-organization' });
        return org || {};
      }
    },
    mounted() {
      this.getDeliveryList();
    },
    methods: {
      async lazySearch(title) {
        if (!title) {
          this.node.childNodes = [];
          this.lazyLoad(this.node, this.resolveFunc);
          return;
        }
        this.lazyLoad(this.node, this.resolveFunc, title);
      },
      async lazyLoad(node, resolve, title) {
        const { data } = node;
        const { id } = data || {};
        if (node.level === 0) {
          this.node = node;
          this.resolveFunc = resolve;
        }
        let parentId = this.currentBusinessScope['id'];
        let params = {
          tenantId: '000000',
          parentId: title ? parentId : id || parentId,
          title: title || undefined
        };
        this.lazyLoading = true;
        try {
          const {
            data: { data: list }
          } = await getCurrentChild(params);
          // 通过调用resolve将子节点数据返回，通知组件数据加载完成
          let arr = list || [];
          arr.forEach((item) => (item.isLeaf = !item.hasChildren));
          if (title) {
            this.deptData = arr;
          } else {
            resolve(arr);
          }
        } catch (error) {
          console.log(error);
        } finally {
          this.lazyLoading = false;
        }
      },
      // 弹窗确定
      saveSuccess(row) {
        let { id, periodName } = row;
        this.formInfo.rwPeriodDetailId = id;
        this.formInfo.periodName = periodName;
      },
      dispatchHandle() {
        this.$refs.cycle.show({ ...this.formInfo });
      },
      async getDeliveryList() {
        try {
          const Func = getDeliveryList;
          const {
            data: { data }
          } = await Func({ userId: this.userInfo['user_id'] });
          // sendCompanyName
          this.deliveryList = data || [];
          // 初始化当前用户所属组织
          if (!this.id && this.isEdit) {
            this.formInfo.initiateOrgId = this.deliveryList[0].id;
            this.formInfo.initiateOrgName = this.deliveryList[0].deptName;
            this.formInfo.oldinitiateOrgId = this.deliveryList[0].id;
            this.formInfo.oldinitiateOrgName = this.deliveryList[0].deptName;
          }
          // this.handleConstructe();
          // this.isEdit && !this.id && this.$emit('setsolution');
        } catch (e) {
          console.log(e);
        }
      },
      validForm() {
        let bool = false;
        this.$refs.form.validate((valid) => {
          bool = valid;
        });
        return bool;
      },
      setcompanyName(val) {
        let id = val;
        let obj = this.deliveryList.find((item) => item.id === id);
        this.formInfo.initiateOrgName = obj ? obj.deptName : '';
        this.formInfo.oldinitiateOrgName = obj ? obj.deptName : '';
        this.formInfo.oldinitiateOrgId = id;
        this.formInfo.initiateOrgId = id;
      },
      assessedHandle(val) {
        let id = val;
        let obj = this.assessedList.find((item) => item.id === id);
        this.formInfo.assessedOrgName = obj ? obj.deptName : '';
      },
      setChangeOrg(val) {
        this.setcompanyName(val);

        this.formInfo.assessedOrgId = '';
        this.formInfo.assessedOrgName = '';
        this.formInfo.schemeId = '';
        this.formInfo.schemeName = '';
        // this.formInfo.rwPeriodDetailId = '';
        // this.formInfo.periodName = '';
        this.formInfo.sourceType = '';
        this.formInfo.associateId = '';
        this.formInfo.associateNo = '';
        this.$emit('setsolution');
      },
      // 单号
      async handleBill() {
        // approvalNo
        let id = this.formInfo.associateId;
        let obj = this.sourceList.find((item) => item.id === id);
        this.formInfo.associateNo = obj ? obj.approvalNo : '';
      },
      handleSourceType() {
        this.formInfo.associateId = '';
        this.formInfo.associateNo = '';
      },
      setAssessedOrg() {
        // let obj = this.assessedList.find((item) => item.id === id);
        // const [
        //   {
        //     data: { title: label }
        //   }
        // ] = this.$refs.assessedOrg.getCheckedNodes() || [];
        // if (!label) return;
        let { assessedOrgName, assessedOrgId } = this.formInfo;
        this.formInfo.assessedOrgName = assessedOrgName;
        this.formInfo.assessedOrgId = assessedOrgId;
        this.formInfo.oldassessedOrgName = assessedOrgName;
        this.formInfo.oldassessedOrgId = assessedOrgId;
        // console.log('-----------------', this.formInfo.assessedOrgId, id);
        // this.formInfo.schemeId = '';
        // this.formInfo.schemeName = '';

        this.formInfo.sourceType = '';
        this.formInfo.associateId = '';
        this.formInfo.associateNo = '';
        this.$emit('setsolution', true);
      },
      setSystemName(id) {
        let obj = this.systemNameList.find((item) => item.id === id);
        this.formInfo.oldschemeId = obj ? obj.id : '';
        this.formInfo.schemeId = obj ? obj.id : '';
        this.formInfo.schemeName = obj ? obj.schemeName : '';
        this.formInfo.oldschemeName = obj ? obj.schemeName : '';
        this.$emit('setsolution', true);
      },
      // 体系名称
      async handleClassification(val) {
        let length = this.formMember.stageList.length;
        if (!length) {
          this.setSystemName(val);
          return;
        }
        // debugger;
        this.$confirm('修改体系名称将清空考核指标, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.setSystemName(val);
            this.$refs.schemeOrg.blur();
          })
          .catch(() => {
            let { oldschemeId, oldschemeName } = this.formInfo;
            this.formInfo.schemeId = oldschemeId;
            this.formInfo.oldschemeName = oldschemeName;
            this.$refs.schemeOrg.blur();
          });
      },
      // 被考核机构
      async handleInstitution(val) {
        // let { assessedOrgId } = this.formInfo;
        // const [
        //   {
        //     data: { id: val }
        //   }
        // ] = this.$refs.assessedOrg.getCheckedNodes() || [];
        // let val = assessedOrgId;
        // if (!val && this.id) return;
        let length = this.formMember.stageList.length;

        // console.log('length, schemeId', length, schemeId);
        if (!length) {
          this.setAssessedOrg(val);
          // this.$refs.assessedOrg.dropDownVisible = false;
          return;
        }
        // await this.confirm('确定要撤回吗？').then(() => {
        //   debugger;
        // });
        // debugger;
        this.$confirm(
          '修改被考核机构将清空体系名称、来源单号、考核指标, 是否继续?',
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
          .then(() => {
            this.setAssessedOrg(val);
            // this.$refs.assessedOrg.blur();
            this.formInfo.isInit = 0;
            // this.$refs.assessedOrg.dropDownVisible = false;
          })
          .catch(() => {
            let { oldassessedOrgId, oldassessedOrgName } = this.formInfo;
            this.formInfo.assessedOrgId = oldassessedOrgId;
            this.formInfo.assessedOrgName = oldassessedOrgName;
            this.formInfo.isInit = 1;
            // this.$refs.assessedOrg.checkedValue = oldassessedOrgId;
            // this.$refs.assessedOrg.presentText = oldassessedOrgName;
            // this.$refs.assessedOrg.blur();
          });
      },
      // 发起组织
      async handleInitiate(val) {
        let length = this.formMember.stageList.length;
        let { assessedOrgId, schemeId, periodName, sourceType, associateId } =
          this.formInfo;
        if (
          !(
            length ||
            schemeId ||
            assessedOrgId ||
            periodName ||
            sourceType ||
            associateId
          )
        ) {
          this.setChangeOrg(val);
          return;
        }
        // debugger;
        this.$confirm(
          '修改发起组织将清空被考核机构、体系名称、考核周期、来源单号、考核指标, 是否继续?',
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
          .then(() => {
            this.setChangeOrg(val);
            this.$refs.initiateOrg.blur();
          })
          .catch(() => {
            let { oldinitiateOrgId, oldinitiateOrgName } = this.formInfo;
            this.formInfo.initiateOrgId = oldinitiateOrgId;
            this.formInfo.oldinitiateOrgName = oldinitiateOrgName;
            this.$refs.initiateOrg.blur();
          });
      },
      async getDept() {
        try {
          const {
            data: { data }
          } = await getDeptTree();
          this.constructionList = formatTreeData(data, {
            label: 'title',
            value: 'id'
          });
          this.initGetConstrutionName();
        } catch (e) {
          console.error(e);
        }
      },
      initGetConstrutionName() {
        if (!this.constructionList.length) return;
        let id = this.formInfo.initiateOrgId;
        const row = this.treeFind(
          this.constructionList,
          (row) => row.value === id
        );
        this.formInfo.initiateOrgName = row ? row.label : '';
        this.formInfo.oldinitiateOrgName = row ? row.label : '';
      },
      treeFind(tree, func) {
        for (const data of tree) {
          if (func(data)) return data;
          if (data.children) {
            const res = this.treeFind(data.children, func);
            if (res) return res;
          }
        }
        return null;
      }
    }
  };
</script>
<style lang="scss">
  .active-placeholder {
    .el-input__inner::placeholder {
      color: #606266 !important;
    }
  }

  .two-wrap {
    .el-form-item__content {
      display: flex;
      flex-wrap: nowrap;
      justify-content: space-between;
    }
  }
</style>
