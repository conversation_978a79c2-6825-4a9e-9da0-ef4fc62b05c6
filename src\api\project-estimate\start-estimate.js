import request from '@/router/axios';
// 列表
export const getPage = (data) => {
  return request({
    url: '/api/project_evaluate/evaluate/page',
    method: 'post',
    data
  });
};
// 保存-编辑
export const submitFetch = (data) => {
  return request({
    url: '/api/project_evaluate/evaluate/save',
    method: 'post',
    data
  });
};

// 根据ID获取数据
export const getDetail = (params) => {
  return request({
    url: `/api/project_evaluate/evaluate/detail`,
    method: 'get',
    params
  });
};
