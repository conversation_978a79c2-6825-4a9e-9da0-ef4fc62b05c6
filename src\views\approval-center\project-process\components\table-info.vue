<template>
  <div class="table_wrapper">
    <el-table
      ref="multipleTable"
      :data="source"
      stripe
      border
      row-key="id"
      v-loading="loading"
      @select="handleSelectionChange"
      @select-all="handleSelectionAll"
      style="width: 100%"
    >
      <el-table-column
        align="center"
        type="selection"
        :selectable="selectable"
        width="55"
      >
      </el-table-column>
      <el-table-column
        align="center"
        type="index"
        label="序号"
      ></el-table-column>
      <el-table-column align="center" show-overflow-tooltip label="流程单号">
        <template slot-scope="{ row }">
          {{ row.flowNo || '--' }}
        </template>
      </el-table-column>
      <el-table-column align="center" show-overflow-tooltip label="流程名称">
        <template slot-scope="{ row }">
          {{ row.flowName || '--' }}
        </template>
      </el-table-column>
      <el-table-column align="center" show-overflow-tooltip label="项目名称">
        <template slot-scope="{ row }">
          {{ row.description || '--' }}
        </template>
      </el-table-column>
      <el-table-column align="center" show-overflow-tooltip label="发起时间"
        ><template slot-scope="{ row }">
          {{ row.applyTime || '--' }}
        </template>
      </el-table-column>
      <el-table-column align="center" show-overflow-tooltip label="发起人姓名">
        <template slot-scope="{ row }">
          {{ row.applyUserName || '--' }}
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        show-overflow-tooltip
        label="发起人部门名称"
      >
        <template slot-scope="{ row }">
          {{ row.applyUserDeptName || '--' }}
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        show-overflow-tooltip
        label="当前审批流状态"
      >
        <template slot-scope="{ row }">
          <el-tag v-if="row.curStatus == 0">审核中</el-tag>
          <el-tag type="success" v-if="row.curStatus == 1">审核通过</el-tag>
          <el-tag type="danger" v-if="row.curStatus == 2">已退回</el-tag>
          <el-tag type="danger" v-if="row.curStatus == 3">审核不通过</el-tag>
          <el-tag type="warning" v-if="row.curStatus == 4">已撤回</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        v-if="activeName === '1'"
        show-overflow-tooltip
        label="审批操作"
        ><template slot-scope="{ row }">
          <el-tag type="success" v-if="row.approveStatus == 1">提交</el-tag>
          <el-tag type="danger" v-if="row.approveStatus == 2">退回</el-tag>
          <template v-if="[null].includes(row.approveStatus)">--</template>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="120">
        <template slot-scope="{ row }">
          <el-button
            type="text"
            icon="el-icon-view"
            @click="$emit('dispatch', 'view', row)"
            >查看
          </el-button>
          <el-button
            v-if="activeName === '2'"
            type="text"
            :disabled="!row.cancelFlag"
            icon="el-icon-back"
            @click="$emit('dispatch', 'commit', row)"
            >撤回
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
  // import { cloneDeep } from 'lodash';
  import { delLabel } from '@/api/project-submit';
  import { mapGetters } from 'vuex';

  export default {
    name: 'ProjectLibraryTableInfo',
    props: {
      loading: {
        type: Boolean,
        default: false
      },
      activeName: {
        type: String,
        default: ''
      },
      source: {
        type: Array,
        default() {
          return [];
        }
      }
    },
    // watch: {
    //   source: {
    //     handler(arr) {
    //       this.list = cloneDeep(arr);
    //     },
    //     deep: true
    //   }
    // },
    data() {
      return {
        list: [],
        visited: false
      };
    },
    methods: {
      async handleClose(tag) {
        let params = {
          id: tag.id,
          labelKey: tag.dictKey
        };
        await delLabel(params);
        this.$message({
          type: 'success',
          message: '删除成功!'
        });
        this.$emit('dispatch', 'refresh');
      },
      // 可选的 退回 3 待提交 0 什么情况都可以打标签
      selectable() {
        if (this.activeName === '0') {
          return true;
        } else {
          return false;
        }
      },
      commit(row) {
        this.$emit('dispatch', 'commit', row);
      },
      // 项目删除
      del(row) {
        this.$emit('dispatch', 'delete', row);
      },
      // 多选框
      // no-unused-vars
      handleSelectionChange(selection, row) {
        console.log('selection', row);
        this.$emit('dispatch', 'selection', row);
      },
      // 全选
      handleSelectionAll(selection) {
        console.log('selectionAll', selection);
        this.$emit('dispatch', 'selectionAll', selection);
      }
    },
    computed: {
      ...mapGetters(['permission']),
      menuName() {
        let bool = this.$route.name.includes('schedule');
        return bool ? 'schedule' : 'check';
      }
    }
  };
</script>

<style lang="scss" scoped>
  .project-label {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;

    // justify-content: space-between;
  }

  .show-center {
    justify-content: center;
  }
</style>
