<template>
  <el-dialog
    :title="title"
    :visible.sync="open"
    :close-on-click-modal="false"
    append-to-body
    width="500px"
    @close="close"
  >
    <el-form
      ref="form"
      label-width="80px"
      :model="form"
      :rules="rules"
      v-loading="loading"
    >
      <el-form-item label="上级分类" prop="classify">
        <select-tree v-model="form.classify" />
      </el-form-item>
      <el-form-item label="菜单名称" prop="name">
        <el-input
          v-model="form.name"
          autocomplete="off"
          placeholder="请输入菜单名称"
        />
      </el-form-item>
      <el-form-item label="排序" prop="sort">
        <el-input-number v-model.number="form.sort" autocomplete="off" />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="close">取 消</el-button>
      <el-button type="primary" @click="save">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
  import SelectTree from './select-tree';

  const rules = {
    classify: [
      { required: true, message: '请选择上级分类', trigger: 'change' }
    ],
    name: [{ required: true, message: '请输入菜单名称', trigger: 'blur' }]
  };
  export default {
    name: 'model',
    components: {
      SelectTree
    },
    model: {
      prop: 'open',
      event: 'change'
    },
    props: {
      open: {
        type: Boolean,
        required: true
      },
      id: {
        type: String,
        required: true
      }
    },
    data() {
      return {
        rules,
        title: '新增',
        form: {
          classify: '',
          name: '',
          sort: 0
        },
        loading: false
      };
    },
    watch: {
      id(val) {
        if (val === '') {
          this.title = '新增';
        } else {
          this.title = '编辑';
          this.request();
        }
      }
    },
    methods: {
      async request() {
        this.loading = true;
        try {
          console.log('编辑详情');
        } catch (e) {
          console.error(e);
        } finally {
          this.loading = false;
        }
      },
      save() {
        this.$refs.form.validate((valid) => {
          // eslint-disable-next-line no-empty
          if (valid) {
          } else {
            return false;
          }
        });
      },
      close() {
        this.$refs.form.resetFields();
        this.$emit('change', false);
      }
    }
  };
</script>

<style scoped></style>
