<template>
  <div class="avue-contail" :class="{ 'avue--collapse': isCollapse }">
    <div class="avue-header">
      <!-- 顶部导航栏 -->
      <top ref="top" />
    </div>
    <div class="avue-layout">
      <div class="avue-left">
        <!-- 左侧导航栏 -->
        <sidebar />
      </div>
      <div class="avue-main">
        <!-- 顶部标签卡 -->
        <tags />
        <transition name="fade-scale">
          <search class="avue-view" v-show="isSearch"></search>
        </transition>
        <!-- 主体视图层 -->
        <div
          style="height: 100%; overflow-x: hidden; overflow-y: auto"
          id="avue-view"
          v-show="!isSearch"
        >
          <keep-alive>
            <router-view
              class="avue-view"
              v-if="$route.meta.keepAlive"
              :key="$route.fullPath + routerViewkey"
            />
          </keep-alive>
          <router-view
            class="avue-view"
            v-if="!$route.meta.keepAlive"
            :key="$route.fullPath + routerViewkey"
          />
        </div>
      </div>
    </div>
    <div class="avue-shade" @click="showCollapse"></div>
    <!-- 浮窗 -->
    <float-win />
  </div>
</template>

<script>
  import FloatWin from '@/components/float-window';
  import { mapGetters } from 'vuex';
  import tags from './tags';
  import search from './search';
  import top from './top/';
  import sidebar from './sidebar/';
  import admin from '@/util/admin';
  import { validatenull } from '@/util/validate';
  import { calcDate } from '@/util/date.js';
  import { getStore, removeStore } from '@/util/store.js';
  import website from '@/config/website';
  import { postCookies } from '@/api/user';

  export default {
    components: {
      top,
      tags,
      search,
      sidebar,
      FloatWin
    },
    name: 'index',
    provide() {
      return {
        index: this
      };
    },
    data() {
      return {
        //搜索控制
        isSearch: false,
        //刷新token锁
        refreshLock: false,
        //刷新token的时间
        refreshTimer: null,
        // 定时发送cookie定时器
        cookieTimer: null
      };
    },
    created() {
      //实时检测刷新token
      this.refreshToken();
      // 定时向后台发送cookie
      this.sendCookie();
    },
    mounted() {
      this.init();
    },
    computed: {
      ...mapGetters([
        'userInfo',
        'isMenu',
        'isLock',
        'isCollapse',
        'website',
        'menu',
        'routerViewkey'
      ])
    },
    props: [],
    methods: {
      showCollapse() {
        this.$store.commit('SET_COLLAPSE');
      },
      // 初始化
      init() {
        this.$store.commit('SET_SCREEN', admin.getScreen());
        window.onresize = () => {
          setTimeout(() => {
            this.$store.commit('SET_SCREEN', admin.getScreen());
          }, 0);
        };
        this.$store.dispatch('FlowRoutes').then(() => {});
      },
      //打开菜单
      openMenu(item = {}) {
        this.$store
          .dispatch('GetMenu', item.id)
          .then((data) => {
            if (data.length !== 0) {
              this.$router.$avueRouter.formatRoutes(data, true);
            }
            //当点击顶部菜单后默认打开第一个菜单
            /*if (!this.validatenull(item)) {
            let itemActive = {},
              childItemActive = 0;
            if (item.path) {
              itemActive = item;
            } else {
              if (this.menu[childItemActive].length === 0) {
                itemActive = this.menu[childItemActive];
              } else {
                itemActive = this.menu[childItemActive].children[childItemActive];
              }
            }
            this.$store.commit('SET_MENU_ID', item);
            this.$router.push({
              path: this.$router.$avueRouter.getPath({
                name: (itemActive.label || itemActive.name),
                src: itemActive.path
              }, itemActive.meta)
            });
          }*/
          })
          .catch(() => {
            setTimeout(() => {
              let loading = this.$refs.top && this.$refs.top.fullLoading;
              loading && loading.close();
            }, 1000);
          });
      },
      // 定时检测token
      refreshToken() {
        this.refreshTimer = setInterval(() => {
          const token =
            getStore({
              name: 'token',
              debug: true
            }) || {};
          const date = calcDate(token.datetime, new Date().getTime());
          if (validatenull(date)) return;
          if (date.seconds >= website.tokenTime && !this.refreshLock) {
            this.refreshLock = true;
            this.$store
              .dispatch('refreshToken')
              .then(() => {
                this.refreshLock = false;
              })
              .catch(() => {
                this.refreshLock = false;
              });
          }
        }, 10000);
        // 页面销毁前, 清空计时器
        this.$on('hook:beforeDestroy', () => {
          // 清除定时器
          clearInterval(this.refreshTimer);
          this.refreshTimer = null;
        });
        if (website.WS.enable) {
          // this.$websocket.initWebSocket(this.userInfo.user_id);
        }
      },
      // 定时向后端发送cookie
      sendCookie() {
        this.cookieTimer = setInterval(async () => {
          // 获取pvkey
          const pvKey = website.pvCookieKe;
          // 获取pvcookie
          const behaviors = getStore({ name: pvKey });
          if (behaviors === '' || behaviors === undefined) return;
          try {
            const { status } = await postCookies(behaviors);
            if (status === 200) {
              removeStore({ name: pvKey });
            }
          } catch (e) {
            console.error(e);
          }
        }, website.sendCookieInterval);
        // 页面销毁前, 清空计时器
        this.$on('hook:beforeDestroy', () => {
          // 清除定时器
          clearInterval(this.cookieTimer);
          this.cookieTimer = null;
        });
      }
    }
  };
</script>
