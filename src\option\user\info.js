import { regexp } from '@/constant/common';

const validatePass = (rule, value, callback) => {
  if (value === '') {
    callback(new Error('请输入密码'));
  } else if (!regexp.test(value)) {
    callback(
      new Error(
        '密码必须包含大写字母、小写字母、特殊符号、数字四种类型的8~16位字符。'
      )
    );
  } else {
    callback();
  }
};
const validatePass2 = (rule, value, callback) => {
  if (value === '') {
    callback(new Error('请再次输入密码'));
    // } else if (value !== this.obj.password) {
    //   callback(new Error('两次输入密码不一致!'));
  } else {
    callback();
  }
};
export default {
  tabs: true,
  tabsActive: 1,
  group: [
    {
      label: '个人信息',
      prop: 'info',
      column: [
        {
          label: '头像',
          type: 'upload',
          listType: 'picture-img',
          propsHttp: {
            res: 'data',
            url: 'link'
          },
          canvasOption: {
            text: ' ',
            ratio: 0.1
          },
          action: '/api/szyk-resource/oss/endpoint/put-file',
          tip: '只能上传jpg/png用户头像，且不超过500kb',
          span: 12,
          row: true,
          prop: 'avatar'
        },
        {
          label: '姓名',
          span: 12,
          row: true,
          prop: 'realName'
        },
        // {
        //   label: '用户名',
        //   span: 12,
        //   row: true,
        //   prop: 'name'
        // },
        {
          label: '登录账号',
          span: 12,
          row: true,
          disabled: true,
          prop: 'account'
        },
        {
          label: '单位',
          span: 12,
          row: true,
          disabled: true,
          prop: 'unitName'
        },
        {
          label: '部门',
          span: 12,
          row: true,
          disabled: true,
          prop: 'deptName'
        },
        {
          label: '角色',
          span: 12,
          row: true,
          disabled: true,
          prop: 'roleName'
        },
        {
          label: '手机号',
          span: 12,
          row: true,
          prop: 'phone'
        },
        {
          label: '邮箱',
          prop: 'email',
          span: 12,
          row: true
        }
      ]
    },
    {
      label: '修改密码',
      prop: 'password',
      column: [
        {
          label: '原密码',
          span: 12,
          row: true,
          type: 'password',
          prop: 'oldPassword',
          rules: [
            {
              required: true,
              message: '请输入原密码',
              trigger: 'blur'
            },
            {
              pattern:
                /^(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z])(?=.*[^a-z0-9])(?=.*[^A-Z0-9])(?=.*[^a-zA-Z0-9])(?=.*[^a-zA-Z]).[^\u4E00-\u9FA5]{7,15}$/,
              message:
                '密码必须包含大写字母、小写字母、特殊符号、数字四种类型的8~16位字符。',
              trigger: 'blur'
            }
          ]
        },
        {
          label: '新密码',
          span: 12,
          row: true,
          type: 'password',
          prop: 'newPassword',
          rules: [{ validator: validatePass, trigger: 'blur' }]
        },
        {
          label: '确认密码',
          span: 12,
          row: true,
          type: 'password',
          prop: 'newPassword1',
          rules: [{ validator: validatePass2, trigger: 'blur' }]
        }
      ]
    }
  ]
};
