<template>
  <div>
    <ul class="document-model-crumbs">
      <li
        v-for="(item, index) in folderInfo.breadList || []"
        :key="item.id"
        @click="switchCrumbs(item, index)"
        :class="breadListDisableClass(index)"
      >
        <div v-if="index !== 0" class="document-model-crumbs-division">/</div>
        <!-- <div class="document-model-crumbs-division">/</div> -->
        <div class="document-model-crumbs-name">{{ item.name }}</div>
      </li>
    </ul>
    <div v-if="groupTableList === 'all'">
      <h-table
        v-loading="loading"
        @size-change="handleSizeChange"
        @pagination-current-change="handleCurrentChange"
        class="select-document-list"
        :columns="groupTableColumns"
        show-pagination
        height="270"
        row-key="id"
        :data="pageData.records || []"
        :total="pageData.total"
        :page-size="pageData.size"
        :current-page="pageData.current"
      >
        <!-- 文件名称 -->
        <template slot="docName" slot-scope="scope">
          <div
            @click="selectList(scope.row)"
            class="document-table-list-item-name"
          >
            <img
              v-oss
              class="document-table-list-icon-all"
              :src="getDocIcon(scope.row)"
              alt=""
            />
            <el-tooltip
              class="item"
              effect="dark"
              :open-delay="500"
              :content="scope.row.docName"
              placement="top-start"
            >
              <div class="document-table-list-name">
                {{ scope.row.docName }}
              </div>
            </el-tooltip>
          </div>
        </template>
        <!-- 创建者 -->
        <template slot="createUserName">
          <div>系统</div>
        </template>
        <!-- 最近修改者 -->
        <template slot="updateUserName">
          <div>--</div>
        </template>
        <!-- 最近修改时间 -->
        <template slot="updateTimeStr">
          <div>--</div>
        </template>
      </h-table>
    </div>
    <div v-if="groupTableList === 'ordinary'">
      <el-table
        ref="multipleTable"
        v-loading="loading"
        @select="handleSelection"
        @select-all="handleSelectionAll"
        class="select-document-list"
        height="270"
        :data="pageData.records || []"
        style="width: 100%"
      >
        <el-table-column type="selection" :selectable="checkboxDisable">
        </el-table-column>
        <el-table-column prop="name" label="名称" width="250">
          <!-- 文件名称 -->
          <template slot-scope="scope">
            <div
              @click="selectList(scope.row)"
              class="document-table-list-item-name"
            >
              <img
                v-oss
                class="document-table-list-icon"
                :src="getDocIcon(scope.row)"
                alt=""
              />
              <el-tooltip
                class="item"
                effect="dark"
                :content="scope.row.docName"
                placement="top-start"
              >
                <div class="document-table-list-name">
                  {{ scope.row.docName }}
                </div>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="createUserName" label="创建者"></el-table-column>
        <el-table-column
          prop="createTimeStr"
          label="创建时间"
          width="140"
        ></el-table-column>
        <el-table-column
          prop="updateUserName"
          label="最近修改者"
        ></el-table-column>
        <el-table-column prop="updateTimeStr" label="最近修改时间">
        </el-table-column>
        <el-table-column prop="size" label="大小" width="120">
        </el-table-column>
      </el-table>
      <div class="document-model-pagination">
        <el-pagination
          v-if="!loading"
          @size-change="(val) => documentTableChange('size', val)"
          @current-change="(val) => documentTableChange('current', val)"
          background
          :current-page="pageData.current"
          :page-size="pageData.size"
          :total="pageData.total"
          layout="jumper, prev, pager, next, sizes, total"
        ></el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
  export default {
    props: {
      pageData: {
        type: Object
      },
      folderInfo: {
        type: Object
      },
      loading: {
        type: Boolean
      },
      selected: {
        type: Object
      },
      selectedNum: {
        type: Number
      }
    },
    data() {
      return {
        folderTable: {
          size: 10,
          current: 1
        },
        selectListObj: {},
        groupTableColumns: [
          {
            label: '名称',
            value: 'docName',
            slotName: 'docName',
            width: '250'
          },
          {
            label: '创建者',
            value: 'createUserName',
            slotName: 'createUserName'
          },
          { label: '创建时间', value: 'createTimeStr' },
          {
            label: '最近修改者',
            value: 'updateUserName',
            slotName: 'updateUserName'
          },
          {
            label: '最近修改时间',
            value: 'updateTimeStr',
            slotName: 'updateTimeStr'
          },
          { label: '大小', value: 'size' }
        ],
        groupTableList: 'ordinary'
      };
    },
    watch: {
      'folderInfo.breadList'() {
        this.folderType();
      },
      // 已选文档列表勾选
      'pageData.records'() {
        //   console.log("updated1111");
        this.$nextTick(() => {
          if (this.pageData.records) {
            this.pageData.records.forEach((item, index) => {
              // debugger;
              if (this.selectListObj[item.id]) {
                this.$refs.multipleTable.toggleRowSelection(
                  this.pageData.records[index]
                );
                // _this.$refs.multipleTable.toggleRowSelection(row);
              }
            });
          }
        });
      }
    },
    updated() {
      this.folderType();
    },
    methods: {
      // 判定当前是否是在全部文件所在页面
      folderType() {
        if (this.folderInfo && this.folderInfo.breadList) {
          // if (this.type === "group") {
          if (this.folderInfo.breadList.length > 1) {
            this.groupTableList = 'ordinary';
          } else if (this.folderInfo.breadList.length === 1) {
            this.groupTableList = 'all';
          }
        } else {
          this.groupTableList = 'all';
        }
      },
      // 列表分页
      documentTableChange(key, val) {
        // let params = {};
        if (key === 'size') {
          this.handleSizeChange(val);
        } else {
          this.handleCurrentChange(val);
        }
      },
      // 文档列表选择框禁用
      checkboxDisable(row) {
        // if (this.selectedNum >= 30) {
        //   return false;
        // } else {
        //   return true;
        // }

        // // 文件夹类型：1全员文件夹；2部门文件夹根目录；3群文件夹根目录；4部门文件夹；5群文件夹；6普通文件夹
        let { folderType, isDeptDel } = row;
        if ((folderType === 5 || folderType === 4) && isDeptDel !== 1) {
          return false;
        } else {
          return true;
        }
      },
      // 全选操作
      handleSelectionAll(rows) {
        // 如果是取消全选操作
        if (rows.length === 0 || (rows.length === 1 && !rows[0])) {
          this.pageData.records.map((item) => {
            let { id } = item;
            if (this.selectListObj[id]) {
              delete this.selectListObj[id];
              this.$emit('removeSelect', item);
            }
          });
        } else {
          // 禁止全选
          let length = rows.length;
          if (this.selectedNum >= 30) {
            // const _this = this;
            let newRows = JSON.parse(JSON.stringify(rows));
            for (let i = 0; i < newRows.length; i++) {
              console.log('rows[i]', newRows[i], newRows);
              if (newRows[i]) {
                let { id } = newRows[i];
                let item = JSON.parse(JSON.stringify(this.selectListObj[id]));
                if (this.selectListObj[id]) {
                  delete this.selectListObj[id];
                  this.$emit('removeSelect', item);
                }
                for (let i = 0; i < this.pageData.records.length; i++) {
                  if (this.pageData.records[i].id === id) {
                    this.$refs.multipleTable.toggleRowSelection(
                      this.pageData.records[i],
                      false
                    );
                  }
                }
              }
            }
            this.$message({
              message: `已选${this.selectedNum},本次最多选择${
                30 - this.selectedNum
              }个`,
              type: 'warning'
            });
          } else if (this.selectedNum + length > 30) {
            this.$message({
              message: `已选${this.selectedNum},本次最多选择${
                30 - this.selectedNum
              }个`,
              type: 'warning'
            });
            let newRows = JSON.parse(JSON.stringify(rows));
            // const _this = this;
            for (let i = 0; i < newRows.length; i++) {
              console.log('rows[i]', newRows[i], newRows);
              if (newRows[i]) {
                let { id } = newRows[i];
                for (let i = 0; i < this.pageData.records.length; i++) {
                  if (this.pageData.records[i].id === id) {
                    delete this.selectListObj[id];
                    this.$emit('removeSelect', this.pageData.records[i]);

                    this.$refs.multipleTable.toggleRowSelection(
                      this.pageData.records[i],
                      false
                    );
                  }
                }
              }
            }
          } else {
            this.addSelectListData(rows);
          }
        }
      },
      handleSelection(rows, row) {
        let selected = rows.length && rows.indexOf(row) !== -1;
        // 移除取消选中的文件
        if (!selected) {
          let { id } = row;
          if (this.selectListObj[id]) {
            delete this.selectListObj[id];
            this.$emit('removeSelect', row);
          }
        } else {
          if (this.selectedNum >= 30 || this.selectedNum + 1 > 30) {
            this.$refs.multipleTable.toggleRowSelection(row);
            this.$message({
              message: `已选${this.selectedNum},本次最多选择${
                30 - this.selectedNum
              }个`,
              type: 'warning'
            });
          } else {
            this.addSelectListData(rows);
          }
        }
        console.log(selected); // true就是选中，0或者false是取消选中
      },
      addSelectListData(rows) {
        let location = window.location.origin;
        if (location.indexOf('http://localhost:') > -1) {
          location = 'https://dev-company.atila.cn';
        }
        let url = location + '/api/attila-resource/attila-view/history';
        rows.forEach((item) => {
          let { id } = item;
          // 如果是第一次选中文件添加排序时间戳
          if (!this.selectListObj[id]) {
            item.optionTime = new Date().getTime();
            // docVerson 文件版本号默认是1
            let { docVerson = 1, docName } = item;
            item.fileUrl = `${url}/${id}/${docVerson}/${docName}`;
            this.selectListObj[id] = item;
          }
        });
        this.$emit('addselect', rows);
      },
      // 面包屑切换方法
      switchCrumbs(item, index) {
        if (index === this.folderInfo.breadList.length - 1) {
          return false;
        }
        let params = {
          current: 1,
          size: 10,
          parentId: item.id
        };
        this.$emit('search', params);
      },
      // 表格面包屑样式添加
      breadListDisableClass(index) {
        if (this.folderInfo && this.folderInfo.breadList) {
          if (index === this.folderInfo.breadList.length - 1) {
            return 'disabled';
          }
          return '';
        }
      },
      // 每页显示数量切换
      handleSizeChange(val) {
        this.folderTable.size = val;
        this.folderTable.parentId = this.folderInfo.id;
        this.$emit('search', this.folderTable);
      },
      // 当前页变化
      handleCurrentChange(val) {
        this.folderTable.current = val;
        this.folderTable.parentId = this.folderInfo.id;
        this.$emit('search', this.folderTable);
      },
      // 选择文件夹
      selectList(row) {
        let { id, docDataType } = row;
        if (docDataType === 1) {
          let params = {
            size: 10,
            current: 1,
            parentId: id
          };
          this.$emit('search', params);
          this.searchInput = '';
        } else {
          this.handleView(row);
        }
      },
      // 文件数据类型：1文件夹；2文件
      getDocIcon(row) {
        let { docDataType, fileType, folderType } = row;
        let arr = {
          f: '/document/file-PDF-icon.png',
          w: '/document/file-Word-icon.png',
          p: '/document/file-PPT-icon.png',
          s: '/document/file-Excel-icon.png'
        };
        if (docDataType === 1) {
          // 文件夹类型：1全员文件夹；2部门文件夹根目录；3群文件夹根目录；4部门文件夹；5群文件夹；6普通文件夹
          if (folderType === 1 || folderType === 4 || folderType === 2) {
            return '/document/file-upload-icon-top-file.png';
          }
          return '/document/file-folder-icon.png';
        } else {
          if (arr[fileType]) {
            return arr[fileType];
          }
          return '/document/file-other-icon.png';
        }
      }
    }
  };
</script>

<style scoped lang="scss">
  .document-model-pagination {
    padding-top: 48px;
    text-align: right;
  }

  .select-document-list {
    // height: 270px;
    .document-table-list-item-name {
      cursor: pointer;

      .document-table-list-icon-all {
        width: 24px;
        height: 20px;
        margin-right: 8px;
        vertical-align: middle;
      }
    }

    .document-table-list-icon {
      width: 22px;
      height: 26px;
      margin-right: 8px;
      vertical-align: middle;
    }

    .document-table-list-name {
      display: inline-block;

      // width: 200px;
      width: 165px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;

      // color: #3b7cff;
      vertical-align: middle;
    }
  }

  .document-model-crumbs {
    display: inline-block;
    width: calc(100% - 300px);
    margin: 0;
    margin-bottom: 18px;
    padding: 0;
    list-style: none;

    li {
      display: inline-block;

      .document-model-crumbs-division {
        display: inline-block;
        padding: 0 8px;
        color: #999;
      }

      .document-model-crumbs-name {
        display: inline-block;
        cursor: pointer;

        &:hover {
          color: #409eff;
        }
      }

      &.disabled {
        .document-model-crumbs-name {
          color: #999;
          cursor: no-drop;

          &:hover {
            color: #999;
          }
        }
      }
    }
  }
</style>
