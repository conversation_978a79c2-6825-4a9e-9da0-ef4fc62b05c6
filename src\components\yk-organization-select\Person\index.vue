<template>
  <span class="wrapper">
    <el-button
      v-if="!btnTitle.length && circle"
      type="primary"
      :circle="circle"
      :icon="icon"
      @click="visible = true"
    ></el-button>
    <el-button v-else type="primary" :icon="icon" @click="visible = true">{{
      btnTitle
    }}</el-button>
    <!--  弹框  -->
    <el-dialog
      :title="modelTitle"
      :visible.sync="visible"
      :close-on-click-modal="false"
      :append-to-body="appendToBody"
      width="85%"
      custom-class="org_dialog"
      @close="cancel"
    >
      <div style="height: 500px" v-if="visible">
        <!--   选择操作区域     -->
        <div class="content-wrapper">
          <tree-wrapper left-val="left_person" mid-val="mid_person">
            <template v-slot:left>
              <!--       部门检索       -->
              <el-input
                clearable
                placeholder="请输入检索组织"
                v-model="queryDeptName"
                @change="queryOrg"
              >
                <i slot="suffix" class="el-input__icon el-icon-search"></i>
              </el-input>
              <!--  组织架构人员  -->
              <tree-org
                v-if="resetStatus"
                :expanded-arr="expandedArr"
                @getNodeInfo="getNode"
              />
              <tree-org-sync
                v-else
                :tree="treeAll"
                :expanded-query-arr="expandedQueryArr"
                :query="queryDeptName"
                :loading="orgLoading"
                :status="queryStatus"
                @getNodeInfo="getNode"
              />
            </template>
            <template v-slot:mid>
              <!--      人员检索        -->
              <div style="height: 40px">
                <el-form inline>
                  <el-form-item v-if="showUnitRange" label="数据范围">
                    <el-select
                      v-model="unitRange"
                      style="width: 140px"
                      @change="request"
                      placeholder="请选择"
                    >
                      <el-option
                        v-for="item in unitRangeList"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      >
                      </el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="检索人员">
                    <el-input
                      style="width: 180px"
                      v-model.trim="queryUser"
                      @keyup.enter.native.stop.prevent="queryText"
                      type="text"
                      placeholder="请输入人员名称"
                    />
                  </el-form-item>
                  <el-form-item>
                    <el-button
                      class="mb10"
                      type="primary"
                      icon="el-icon-search"
                      @click="queryText"
                      >查询</el-button
                    >
                  </el-form-item>
                </el-form>
              </div>
              <el-row :gutter="15">
                <el-col :md="16" :lg="16">
                  <div class="list_wrapper">
                    <!--   人员列表   -->
                    <list
                      :loading="loading"
                      :arr="tableData"
                      @selectEmit="selectFn"
                    />
                    <yk-pagination
                      small
                      layout="total, prev, pager, next, jumper"
                      v-show="total > 0"
                      :total="total"
                      :page.sync="pages.pageNum"
                      :limit.sync="pages.pageSize"
                      :page-sizes="[10, 20]"
                      @pagination="getList"
                    />
                  </div>
                </el-col>
                <el-col :md="8" :lg="8" class="h100">
                  <!--  已选择人员  -->
                  <select-people
                    :loading="selectLoad"
                    :list="personArr"
                    @removeEmit="removeFn"
                  />
                </el-col>
              </el-row>
            </template>
          </tree-wrapper>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="ok">确 定</el-button>
      </span>
    </el-dialog>
  </span>
</template>

<script>
  import List from './list';
  import TreeOrg from './tree-org-dept';
  import TreeOrgSync from '../components/tree-org-person-sync';
  import SelectPeople from './select';
  import {
    getUserList,
    getDeptIdList,
    getDeptNameList,
    getDeptAll
  } from '@/api/system/dept';
  import { cloneDeep, throttle } from 'lodash';
  import TreeWrapper from '../components/tree-wrapper';
  import website from '@/config/website';

  export default {
    name: 'Person',
    components: {
      TreeOrgSync,
      SelectPeople,
      List,
      TreeOrg,
      TreeWrapper
    },
    model: {
      prop: 'userArr',
      event: 'change'
    },
    props: {
      // 按钮标题
      btnTitle: {
        type: String,
        default: '人员选择'
      },
      // 弹框标题
      modelTitle: {
        type: String,
        default: '人员选择'
      },
      // 是否为圆形按钮
      circle: {
        type: Boolean,
        default: false
      },
      // 选择按钮 icon
      icon: {
        type: String,
        default: 'el-icon-s-custom'
      },
      // 已选人员数组
      userArr: {
        type: Array,
        default: function () {
          return [];
        }
      },
      // 查询条件是否展示数据范围
      showUnitRange: {
        type: Boolean,
        default: false
      },
      // 单选/多选 默认多选
      single: {
        type: Boolean,
        default: false
      },
      appendToBody: {
        type: Boolean,
        default: true
      }
    },
    watch: {
      async visible(val) {
        if (val) {
          this.showUnitRange && this.request();
          // 打开弹框
          await this.initDept();
          let arr = this.userArr;
          const ids = arr.map((item) => item.deptId);

          if (ids.length) {
            // 设置返回展开ids
            try {
              const res = await getDeptIdList(
                ids.toString(),
                website.tenantName
              );
              this.expandedArr = res.data.data;
            } catch (e) {
              console.error(e);
            }
          } else {
            this.expandedArr = [];
          }
          // 单选
          if (this.single && arr.length > 1) {
            arr.length = 1;
          }
          // 已选人员ids,存储本地详细信息
          if (arr.length) {
            this.personArr = arr;
          } else {
            this.personArr = [];
          }
        }
      },
      queryDeptName(val) {
        // 重置
        if (val === '') {
          this.resetFn();
        }
      }
    },
    data() {
      return {
        visible: false,
        loading: false,
        queryStatus: false,
        resetStatus: true,
        selectLoad: false,
        orgLoading: false,
        total: 0,
        pages: {
          pageNum: 1,
          pageSize: 10
        },
        unitRange: '1',
        queryUser: '',
        queryDeptName: '',
        tableData: [],
        node: null,
        personArr: [],
        expandedArr: [],
        expandedQueryArr: [],
        tree: [],
        treeAll: {},
        unitRangeList: [
          { label: '仅看本级', value: '1' },
          { label: '本级及所有下级', value: '2' }
        ]
      };
    },
    methods: {
      // 分页查询
      getList({ page, limit }) {
        Object.assign(this.pages, {
          pageNum: page,
          pageSize: limit
        });
        this.request();
      },
      // 取消按钮
      cancel() {
        this.visible = false;
        this.total = 0;
        Object.assign(this.pages, {
          pageNum: 1,
          pageSize: 10
        });
        this.queryUser = '';
        this.queryDeptName = '';
        this.tableData = [];
        this.node = null;
      },
      // 确定按钮
      ok() {
        const _arr = this.personArr.map((item) => {
          return {
            id: item.id,
            realName: item.realName,
            deptId: item.deptId,
            deptName: item.deptName,
            ancestorName: item.ancestorName
          };
        });
        this.$emit('change', _arr);
        this.dispatch('ElFormItem', 'el.form.change', _arr);
        this.cancel();
      },
      // 名字检索
      queryText() {
        Object.assign(this.pages, {
          pageNum: 1,
          pageSize: 10
        });
        this.request();
      },
      // 部门检索
      queryOrg: throttle(
        function () {
          if (this.queryDeptName.length) {
            this.orgLoading = true;
            this.resetStatus = false;
            this.queryDept(this.queryDeptName);
            setTimeout(() => {
              this.orgLoading = false;
            }, 3 * 1000);
          }
        },
        2000,
        {
          throttle: false
        }
      ),
      // 查询部门 API
      async queryDept(deptName) {
        this.queryStatus = false;
        try {
          const res = await getDeptNameList(deptName, website.tenantName);
          this.expandedQueryArr = res.data.data;
          this.queryStatus = true;
        } catch (e) {
          console.error(e);
        }
      },
      // 组织重置
      resetFn() {
        this.resetStatus = true;
        this.expandedQueryArr = [];
      },
      // 人员改动
      selectFn(person, status) {
        if (status) {
          // 新增
          if (this.single) {
            this.personArr = [];
            this.tableData.map((item) => {
              item.status = false;
              if (person.id === item.id && person.deptId === item.deptId) {
                item.status = true;
              }
              return item;
            });
          }
          this.personArr.push(person);
        } else {
          // 移除
          const index = this.personArr.findIndex(
            (item) => item.id === person.id && person.deptId === item.deptId
          );
          if (index !== -1) {
            this.personArr.splice(index, 1);
          }
        }
      },
      // 删除人员
      removeFn(index, id, deptId) {
        this.personArr.splice(index, 1);
        // 修改状态
        this.tableData.map((item) => {
          if (id === item.id && deptId === item.deptId) {
            item.status = false;
          }
          return item;
        });
        if (!this.personArr.length) {
          this.oneKey = true;
        } else {
          this.oneKey = false;
        }
      },
      // 选择部门
      getNode(node) {
        this.node = node;
        Object.assign(this.pages, {
          pageNum: 1,
          pageSize: 10
        });
        this.request();
      },
      // 请求人员列表
      async request() {
        this.loading = true;
        const deptId = this.node ? this.node.id : undefined;
        const params = {
          deptId,
          unitRange: this.unitRange || undefined,
          userName: this.queryUser || undefined,
          current: this.pages.pageNum,
          size: this.pages.pageSize,
          tenantId: website.tenantName
        };
        try {
          const res = await getUserList(params);
          const { records, total } = res.data.data;
          this.total = total;
          this.tableData = records.map((item) => {
            item.status = false;
            this.personArr.forEach((person) => {
              if (person.id === item.id) {
                item.status = true;
              }
            });
            return item;
          });
        } catch (e) {
          console.error(e);
        } finally {
          this.loading = false;
        }
      },
      // 所有组织
      async initDept() {
        try {
          const res = await getDeptAll(website.tenantName);
          const treeData = cloneDeep(res.data.data);
          treeData.push(website.deptRootSync);
          const treeMap = {};
          for (let d of treeData) {
            let parentId = d['parentId'];
            if (!treeMap[parentId]) {
              treeMap[parentId] = [d];
            } else {
              treeMap[parentId].push(d);
            }
          }
          this.treeAll = treeMap;
        } catch (e) {
          console.error(e);
        }
      },
      // 触发校验
      dispatch(componentName, eventName, params) {
        let parent = this.$parent || this.$root;
        let name = parent.$options.componentName;

        while (parent && (!name || name !== componentName)) {
          parent = parent.$parent;

          if (parent) {
            name = parent.$options.componentName;
          }
        }
        if (parent) {
          parent.$emit.apply(parent, [eventName].concat(params));
        }
      }
    }
  };
</script>

<style scoped>
  .content-wrapper {
    height: 100%;
  }

  .h100 {
    height: 100%;
  }

  .list_wrapper {
    width: 100%;
    padding-bottom: 20px;
    overflow: hidden;
  }
</style>
