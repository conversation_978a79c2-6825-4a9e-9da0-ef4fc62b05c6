<template>
  <div>
    <Dialog
      width="1100px"
      :visible="visible"
      :title="dialogTitle"
      @closed="reset"
    >
      <el-table :data="list" stripe border style="width: 100%" row-key="id">
        <el-table-column type="index" width="60" align="center" label="序列" />
        <el-table-column align="center" label="姓名" show-overflow-tooltip>
          <template slot-scope="{ row }">
            {{ row.userName }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="评审状态" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <el-tag type="warning" v-if="row.reviewStatus == 1">{{
              row.reviewStatusName
            }}</el-tag>
            <el-tag v-if="row.reviewStatus == 2">{{
              row.reviewStatusName
            }}</el-tag>
            <el-tag type="success" v-if="row.reviewStatus == 3">{{
              row.reviewStatusName
            }}</el-tag>
            <el-tag type="info" v-if="row.reviewStatus == 4">{{
              row.reviewStatusName
            }}</el-tag>
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button @click="reset">关 闭</el-button>
      </div>
    </Dialog>
  </div>
</template>
<script>
  import Dialog from '@/components/basic-dialog';
  import { mapGetters } from 'vuex';
  export default {
    components: { Dialog },
    data() {
      return {
        visible: false,
        dialogTitle: '专家名单',
        list: []
      };
    },
    methods: {
      show(arr) {
        this.visible = true;
        this.list = arr || [];
      },
      reset() {
        this.visible = false;
      }
    },
    computed: {
      ...mapGetters(['userInfo'])
    }
  };
</script>

<style lang="scss" scoped>
  .user_tag {
    margin-top: 10px;
    margin-right: 10px;
  }

  .dialog-footer {
    .el-button + .el-button {
      margin-left: 10px !important;
    }
  }
</style>
