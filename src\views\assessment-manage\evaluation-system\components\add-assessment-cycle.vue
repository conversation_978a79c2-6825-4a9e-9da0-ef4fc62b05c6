<template>
  <Dialog
    v-if="visible"
    :visible="visible"
    :title="title"
    width="1100px"
    @closed="hide"
  >
    <el-form
      label-suffix=":"
      ref="queryForm"
      label-width="80px"
      :model="queryForm"
      inline
    >
      <el-form-item label="周期类型" prop="periodType">
        <el-select
          v-model="queryForm.periodType"
          filterable
          placeholder="全部"
          style="width: 100%"
          clearable
        >
          <el-option
            v-for="item in serviceDicts.type['period_type']"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="期间名称" prop="periodName">
        <el-input
          class="mb10"
          clearable
          :maxlength="20"
          v-model.trim="queryForm.periodName"
          @keyup.enter.native.stop.prevent="queryText"
          type="text"
          placeholder="请输入期间名称"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="queryText"
          >搜索</el-button
        >
        <el-button icon="el-icon-delete" @click="reset">清空</el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="tableData"
      v-loading="loading"
      border
      stripe
      :header-cell-style="{ backgroundColor: '#fafafa' }"
      style="width: 100%"
      class="mt-10"
      row-key="id"
      ref="multipleTable"
    >
      <el-table-column width="50" align="center">
        <template slot-scope="{ row }">
          <el-radio
            :label="row"
            v-model="tableRadio"
            @change.native="getCurrentRow(row)"
            >&nbsp;</el-radio
          >
        </template>
      </el-table-column>
      <el-table-column type="index" width="60" align="center" label="序列" />
      <el-table-column
        prop="periodTypeName"
        width="150"
        align="center"
        label="周期类型"
      >
      </el-table-column>
      <el-table-column prop="year" width="150" align="center" label="年">
      </el-table-column>
      <el-table-column prop="periodName" align="center" label="期间名称">
        <template slot-scope="{ row }">
          {{ row.periodName || '--' }}
        </template>
      </el-table-column>
      <el-table-column align="center" prop="periodStartTime" label="开始时间">
      </el-table-column>
      <el-table-column align="center" prop="periodEndTime" label="结束时间">
      </el-table-column>
    </el-table>
    <yk-pagination
      small
      v-show="total > 0"
      :total="total"
      :page.sync="queryForm.current"
      :limit.sync="queryForm.size"
      @pagination="getList"
    />
    <div slot="footer">
      <el-button type="primary" plain @click="hide">取消</el-button>
      <el-button type="primary" @click="onSubmit">确定 </el-button>
    </div>
  </Dialog>
</template>
<script>
  import Dialog from '@/components/basic-dialog';
  // import { getAllMaterialList } from "@/api/distribution-apply";
  import { getCyclePage } from '@/api/assess-manage';
  import { trimAll } from '@/util/util';
  const queryForm = {
    periodType: undefined,
    periodName: undefined,
    size: 10,
    current: 1
  };
  export default {
    serviceDicts: ['period_type'],
    components: { Dialog },
    props: {
      deptTree: { type: Array, require: () => [] },
      categoryTree: { type: Array, require: () => [] }
    },
    data() {
      return {
        visible: false,
        loading: false,
        tableData: [],
        total: 0,
        tableRadio: {},
        title: '考核周期',
        // ------------
        editSelected: {}, // 已选中编辑过的列表
        queryForm: { ...queryForm },
        cycleOptions: []
      };
    },
    methods: {
      getCurrentRow() {
        // this.editSelected = row;
      },
      // 检索
      async queryText() {
        Object.assign(this.queryForm, {
          current: 1,
          size: 10
        });
        await this.request();
      },
      // 请求列表
      async request() {
        this.loading = true;
        let { current, size, periodType, periodName } = this.queryForm;
        const params = {
          periodType: periodType,
          periodName: trimAll(periodName),
          deptId: this.editSelected.initiateOrgId,
          periodDetailStatus: '0',
          current,
          size
        };
        try {
          const {
            data: { data }
          } = await getCyclePage(params);
          const { records = [], total = 0 } = data || {};
          this.total = total;
          this.tableData = records || [];
          this.resetSelected();
        } catch (e) {
          console.error(e);
        } finally {
          this.loading = false;
        }
      },
      // 分页查询
      getList({ page, limit }) {
        Object.assign(this.queryForm, {
          current: page,
          size: limit
        });
        this.request();
      },
      // 提交
      onSubmit() {
        if (!this.tableRadio) return this.$message.warning('请选择考核周期');
        // this.$message.success('保存成功');
        this.$emit('saveSuccess', this.tableRadio);
        this.hide();
      },
      async show(row = {}) {
        // debugger;
        this.visible = true;
        this.editSelected = row;
        if (row.rwPeriodDetailId) {
          this.tableRadio.id = row.rwPeriodDetailId;
        }
        await this.queryText();
      },
      // 重置table选中显示
      resetSelected() {
        let keys = Object.keys(this.tableRadio);
        if (!keys.length) return;
        let obj = this.tableData.find((item) => item.id == this.tableRadio.id);
        // debugger;
        this.tableRadio = obj || this.tableRadio;
      },
      resetPage() {
        this.queryForm = { ...queryForm };
      },
      reset() {
        this.resetPage();
        this.queryText();
      },
      hide() {
        this.tableRadio = {};
        this.editSelected = {};
        this.resetPage();
        this.visible = false;
      }
    }
  };
</script>
