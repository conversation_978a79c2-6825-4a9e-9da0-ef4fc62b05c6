import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/szyk-desk/notice/list',
    method: 'get',
    params: {
      ...params,
      current,
      size
    }
  });
};

export const remove = (ids) => {
  return request({
    url: '/api/szyk-desk/notice/remove',
    method: 'post',
    params: {
      ids
    }
  });
};

export const add = (row) => {
  return request({
    url: '/api/szyk-desk/notice/submit',
    method: 'post',
    data: row
  });
};

export const update = (row) => {
  return request({
    url: '/api/szyk-desk/notice/submit',
    method: 'post',
    data: row
  });
};

export const getNotice = (id) => {
  return request({
    url: '/api/szyk-desk/notice/detail',
    method: 'get',
    params: {
      id
    }
  });
};
// 待办已办任务 消息
export const getTasksNotice = (params) => {
  return request({
    url: '/api/szyk-system/szyk-task/page',
    method: 'get',
    params
  });
};
export const getNewsNotice = (params) => {
  return request({
    url: '/api/szyk-system/szyk-msg/page',
    method: 'get',
    params
  });
};
// 消息已读
export const setReaded = (params) => {
  return request({
    url: '/api/szyk-system/szyk-msg/msgRead',
    method: 'get',
    params
  });
};
// end
// 首页项目管理统计
export const projectStatistics = (row) => {
  return request({
    url: '/api/zbusiness-stat/project-stat/homeProjectStat',
    method: 'post',
    data: row
  });
};
// 首页资源管理统计
export const resourceStatistics = (row) => {
  return request({
    url: '/api/zbusiness-stat/resource-stat/homeResourceStat',
    method: 'post',
    data: row
  });
};
// 首页任务管理统计
export const workStatistics = (row) => {
  return request({
    url: '/api/zbusiness-stat/project-task/homeTaskStat',
    method: 'post',
    data: row
  });
};
