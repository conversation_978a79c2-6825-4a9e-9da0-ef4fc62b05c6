import request from '@/router/axios';
// 列表
export const getPage = (data) => {
  return request({
    url: '/api/project_evaluate/upload/page',
    method: 'post',
    data
  });
};
// 保存-编辑
export const submitFetch = (data) => {
  return request({
    url: '/api/project_evaluate/upload/submit',
    method: 'post',
    data
  });
};

// 根据ID获取数据
export const getDetail = (params) => {
  return request({
    url: `/api/project_evaluate/detail`,
    method: 'get',
    params
  });
};

// 后评价结果-分页
export const getResultPage = (data) => {
  return request({
    url: '/api/project_evaluate/evaluate/result',
    method: 'post',
    data
  });
};

// 后评价结果查看详情
export const resultDetail = (params) => {
  return request({
    url: `/api/project_evaluate/evaluate/resultDetail`,
    method: 'get',
    params
  });
};
