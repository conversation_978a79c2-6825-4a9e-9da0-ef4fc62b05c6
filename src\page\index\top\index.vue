<template>
  <div class="avue-top">
    <div class="top-bar__left">
      <div
        class="avue-breadcrumb"
        :class="[{ 'avue-breadcrumb--active': isCollapse }]"
        v-if="showCollapse"
      >
        <i class="icon-navicon" @click="setCollapse"></i>
      </div>
    </div>
    <!-- <div class="top-bar__title">
      <div class="top-bar__item top-bar__item--show" v-if="showMenu">
        <top-menu ref="topMenu"></top-menu>
      </div>
      <span class="top-bar__item" v-if="showSearch">
        <top-search></top-search>
      </span>
    </div> -->
    <div class="top-bar__right">
      <el-tooltip
        v-if="showColor"
        effect="dark"
        :content="$t('navbar.color')"
        placement="bottom"
      >
        <div class="top-bar__item">
          <top-color></top-color>
        </div>
      </el-tooltip>
      <el-tooltip
        v-if="showDebug"
        effect="dark"
        :content="logsFlag ? $t('navbar.bug') : logsLen + $t('navbar.bugs')"
        placement="bottom"
      >
        <div class="top-bar__item">
          <top-logs></top-logs>
        </div>
      </el-tooltip>
      <el-tooltip
        v-if="showLock"
        effect="dark"
        :content="$t('navbar.lock')"
        placement="bottom"
      >
        <div class="top-bar__item">
          <top-lock></top-lock>
        </div>
      </el-tooltip>
      <a
        class="to_index_wrapper"
        href="http://safe.ykjt.cc/datashare/f/issue/tkIssue/form?tenantCode=98000001&systemId=392"
        target="_blank"
      >
        <i style="font-size: 16px" class="el-icon-question"></i>
      </a>
      <!--演示环境 <a
        class="to_index_wrapper"
        href="http://************:40004/datashare/f/issue/tkIssue/form?tenantCode=98000001&systemId=392"
        target="_blank"
      >
        <i style="font-size: 16px" class="el-icon-question"></i>
      </a> -->
      <div
        v-if="permission['cockpit-view'] && currentOrgParentId === '0'"
        class="to_index_wrapper"
        @click="$router.push('/cockpit')"
      >
        <span>{{ $t('navbar.cockpit') }}</span>
        <i class="el-icon-data-analysis"></i>
      </div>
      <!-- 切换组织 -->
      <el-dropdown @command="handleCommand">
        <span class="el-dropdown-link" style="cursor: pointer">
          业务范围：{{ orgName }}
          <i class="el-icon-arrow-down el-icon--right"></i>
        </span>
        <el-dropdown-menu slot="dropdown" class="custom-userInfo-name">
          <el-dropdown-item
            v-for="item of organization"
            :key="item.id"
            :command="item.id"
          >
            {{ item.ancestorName }}
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <el-tooltip
        v-if="showTheme"
        effect="dark"
        :content="$t('navbar.theme')"
        placement="bottom"
      >
        <div class="top-bar__item top-bar__item--show">
          <top-theme></top-theme>
        </div>
      </el-tooltip>
      <!-- <el-tooltip
        effect="dark"
        :content="$t('navbar.notice')"
        placement="bottom"
      >
        <div class="top-bar__item top-bar__item--show">
          <top-notice></top-notice>
        </div>
      </el-tooltip> -->
      <el-tooltip effect="dark" content="字号" placement="bottom">
        <top-size-select
          style="margin: 0 5px; font-size: 18px; cursor: pointer"
        />
      </el-tooltip>
      <el-tooltip
        v-if="showLanguage"
        effect="dark"
        :content="$t('navbar.language')"
        placement="bottom"
      >
        <div class="top-bar__item top-bar__item--show">
          <top-lang></top-lang>
        </div>
      </el-tooltip>
      <el-tooltip
        v-if="showFullScren"
        effect="dark"
        :content="
          isFullScren ? $t('navbar.screenfullF') : $t('navbar.screenfull')
        "
        placement="bottom"
      >
        <div class="top-bar__item">
          <i
            :class="isFullScren ? 'icon-tuichuquanping' : 'icon-quanping'"
            @click="handleScreen"
          ></i>
        </div>
      </el-tooltip>
      <!-- <img class="top-bar__img"
           :src="userInfo.avatar"> -->
      <el-dropdown>
        <span class="el-dropdown-link">
          {{ userInfo['nick_name'] }}
          <i class="el-icon-arrow-down el-icon--right"></i>
        </span>
        <el-dropdown-menu slot="dropdown" class="custom-userInfo-name">
          <el-dropdown-item @click.native="$router.push('/')">
            {{ $t('navbar.dashboard') }}
          </el-dropdown-item>
          <el-dropdown-item @click.native="$router.push('/info/index')">
            {{ $t('navbar.userinfo') }}
          </el-dropdown-item>
          <el-dropdown-item
            v-if="this.website.switchMode"
            @click.native="switchDept"
            >{{ $t('navbar.switchDept') }}
          </el-dropdown-item>
          <el-dropdown-item @click.native="logout" divided
            >{{ $t('navbar.logOut') }}
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <el-dialog
        title="用户信息选择"
        append-to-body
        :visible.sync="userBox"
        width="350px"
      >
        <avue-form
          ref="form"
          :option="userOption"
          v-model="userForm"
          @submit="submitSwitch"
        />
      </el-dialog>
    </div>
  </div>
</template>
<script>
  // import { env } from '@/config/env';
  import { resetRouter } from '@/router/router';
  import { mapGetters, mapState } from 'vuex';
  import { fullscreenToggel, listenfullscreen } from '@/util/util';
  import topLock from './top-lock';
  import topMenu from './top-menu';
  import topSearch from './top-search';
  import topTheme from './top-theme';
  import topLogs from './top-logs';
  import topColor from './top-color';
  import topNotice from './top-notice';
  import topLang from './top-lang';
  import { getBusinessScope, changeScope } from '@/api/system/user';
  import { setStore, getStore, clearStore } from '@/util/store';
  import topSizeSelect from './top-size-select';

  export default {
    components: {
      topLock,
      topMenu,
      topSearch,
      topTheme,
      topLogs,
      topColor,
      topNotice,
      topLang,
      topSizeSelect
    },
    name: 'top',
    data() {
      return {
        loading: false,
        userBox: false,
        userForm: {
          deptId: '',
          roleId: ''
        },
        userOption: {
          labelWidth: 70,
          submitBtn: true,
          emptyBtn: false,
          submitText: '切换',
          column: [
            {
              label: '部门',
              prop: 'deptId',
              type: 'select',
              props: {
                label: 'deptName',
                value: 'id'
              },
              dicUrl: '/api/szyk-system/dept/select',
              span: 24,
              display: false,
              rules: [
                {
                  required: true,
                  message: '请选择部门',
                  trigger: 'blur'
                }
              ]
            },
            {
              label: '角色',
              prop: 'roleId',
              type: 'select',
              props: {
                label: 'roleName',
                value: 'id'
              },
              dicUrl: '/api/szyk-system/role/select',
              span: 24,
              display: false,
              rules: [
                {
                  required: true,
                  message: '请选择角色',
                  trigger: 'blur'
                }
              ]
            }
          ]
        },
        // 组织
        organization: [],
        orgName: '',
        currentOrgParentId: '',
        fullLoading: {}
      };
    },
    filters: {},
    inject: ['index', 'reload'],
    async created() {
      await this.getOrgList();
      this.setOrg();
    },
    mounted() {
      listenfullscreen(this.setScreen);
    },
    computed: {
      ...mapState({
        showDebug: (state) => state.common.showDebug,
        showTheme: (state) => state.common.showTheme,
        showLock: (state) => state.common.showLock,
        showFullScren: (state) => state.common.showFullScren,
        showCollapse: (state) => state.common.showCollapse,
        showSearch: (state) => state.common.showSearch,
        showMenu: (state) => state.common.showMenu,
        showColor: (state) => state.common.showColor,
        showLanguage: (state) => state.common.showLanguage
      }),
      ...mapGetters([
        'userInfo',
        'isFullScren',
        'tagWel',
        'tagList',
        'isCollapse',
        'tag',
        'logsLen',
        'logsFlag',
        'permission'
      ]),
      // dingding跳转后的部门id 不考虑
      deptScopeId() {
        return '';
        // return this.$route.query.deptScopeId;
      }
    },
    methods: {
      setOrg() {
        // 初始化
        let org = getStore({ name: 'current-organization' });
        this.orgName = org ? org['ancestorName'] : '';
        this.currentOrgParentId = org ? org['parentId'] : '';

        let deptScopeId = this.deptScopeId;
        if (deptScopeId) {
          let org = getStore({ name: 'current-organization' });
          if (org['id'] === deptScopeId) return;
          return this.handleCommand(deptScopeId);
        }
        // 刚登录
        if (!org) {
          let content = this.organization.find((item) => item.defaultDept);
          setStore({
            name: 'current-organization',
            content
          });
          this.orgName = content['ancestorName'];
          this.currentOrgParentId = content['parentId'];
        }
      },
      // 组织列表数据
      async getOrgList() {
        try {
          this.loading = true;
          const res = await getBusinessScope();
          const data = res.data.data || [];
          this.organization = data || [];
        } catch (e) {
          console.error(e);
        } finally {
          this.loading = false;
        }
      },
      async handleCommand(deptId) {
        let content = this.organization.find((item) => item.id === deptId);
        setStore({
          name: 'current-organization',
          content
        });
        this.orgName = content['ancestorName'];
        this.currentOrgParentId = content['parentId'];
        await this.changeScope(deptId);
        this.index.openMenu();
        this.fullLoading.close();
        this.reload();
        // 关闭tags
        if (!this.deptScopeId) {
          this.closeAllTags();
        }
        resetRouter();
        // this.$router.go(0);

        // this.$store.commit('SET_REFRESHKEY');
        // setTimeout(() => {

        // }, 1000);
      },
      async changeScope(deptId) {
        try {
          this.fullLoading = this.$loading({
            lock: true,
            text: '切换中',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          });
          let { data } = await changeScope({ deptId });
          this.$store.commit('SET_TOKEN', data.access_token);
          this.$store.commit('SET_REFRESH_TOKEN', data.access_token);
          this.$store.commit('SET_USER_INFO', data);
        } catch (error) {
          console.log(error);
        }
      },
      closeAllTags() {
        this.$store.commit('DEL_ALL_TAG');
        this.$router.push({
          path: this.$router.$avueRouter.getPath({
            src: this.tagWel.value
          })
          // query: this.tagWel.query
        });
      },
      handleScreen() {
        fullscreenToggel();
      },
      setCollapse() {
        this.$store.commit('SET_COLLAPSE');
      },
      setScreen() {
        this.$store.commit('SET_FULLSCREN');
      },
      switchDept() {
        const userId = this.userInfo.user_id;
        const deptColumn = this.findObject(this.userOption.column, 'deptId');
        deptColumn.dicUrl = `/api/szyk-system/dept/select?userId=${userId}`;
        deptColumn.display = true;
        const roleColumn = this.findObject(this.userOption.column, 'roleId');
        roleColumn.dicUrl = `/api/szyk-system/role/select?userId=${userId}`;
        roleColumn.display = true;
        this.userBox = true;
      },
      submitSwitch(form, done) {
        this.$store.dispatch('refreshToken', form).then(() => {
          this.userBox = false;
          this.$router.push({ path: '/' });
        });
        done();
      },
      logout() {
        let env = process.env.VUE_APP_ENV;
        this.$confirm(this.$t('logoutTip'), this.$t('tip'), {
          confirmButtonText: this.$t('submitText'),
          cancelButtonText: this.$t('cancelText'),
          type: 'warning'
        }).then(() => {
          this.$store.dispatch('LogOut').then(() => {
            resetRouter();
            // this.$store.commit('SET_THEME_NAME', 'theme-bule');
            clearStore();
            if (['test', 'development'].includes(env)) {
              return this.$router.push({ path: '/login' });
            }
            if (['show'].includes(env)) {
              return (window.location.href =
                'https://iamtest.ykjt.cc/ngw/logout?returnurl=http://xxhdev.shandong-energy.com'); // 要改统一认证
            }
            window.location.href =
              'https://iam.ykjt.cc:8443/ngw/logout?returnurl=https://xxh.shandong-energy.com'; // 要改统一认证
            // window.location.href =
            //   'https://iamtest.shandong-energy.com/ngw/logout'; // 要改统一认证
          });
        });
      }
    }
  };
</script>

<style lang="scss" scoped>
  .custom-userInfo-name {
    margin-top: -10px;
  }

  .to_index_wrapper {
    margin-right: 20px;
    color: #fff;
    font-size: 14px;
    cursor: pointer;

    .el-icon-data-analysis {
      margin-left: 4px;
      font-size: 16px;
    }
  }
</style>
