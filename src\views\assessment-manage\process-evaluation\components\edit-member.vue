<template>
  <!-- @click.capture="cancelcurrentRow" -->
  <div>
    <el-button
      v-if="isEdit"
      type="primary"
      :disabled="!formInfo.schemeId || !formInfo.assessedOrgId"
      icon="el-icon-circle-plus-outline"
      style="margin-bottom: 15px"
      @click="handleAdd"
    >
      选择评分标准</el-button
    >
    <el-form
      label-width="150px"
      :model="form"
      ref="form"
      :rules="rules"
      label-suffix="："
    >
      <!-- :row-class-name="tableRowClassName"  -->
      <el-table
        :data="form.stageList"
        border
        :header-cell-style="{ backgroundColor: '#fafafa' }"
        ref="singleTable"
        :span-method="objectSpanMethod"
        show-summary
        :summary-method="getSummaries"
        style="width: 100%"
        ><el-table-column
          align="center"
          width="55"
          type="index"
          label="序号"
        ></el-table-column>
        <el-table-column width="75" align="center" label="指标分类">
          <template slot-scope="{ row }">
            {{ row.classifyName || '--' }}
          </template>
        </el-table-column>
        <!-- <el-table-column align="center" label="分类分值">
          <template slot-scope="{ row }">
            {{ row.classifyScore || '--' }}
          </template>
        </el-table-column> -->
        <el-table-column width="100" align="center" label="是否加分项">
          <template slot-scope="{ row }">
            {{ row.addScoreName }}
          </template>
        </el-table-column>
        <el-table-column header-align="center" label="评价指标">
          <template slot-scope="{ row }">
            {{ row.evaluateTarget || '--' }}
          </template>
        </el-table-column>
        <!-- <el-table-column
          width="80"
          show-overflow-tooltip
          align="center"
          label="指标解释"
        >
          <template slot-scope="{ row }">
            {{ row.targetExplain || '--' }}
          </template>
        </el-table-column> -->
        <el-table-column width="70" align="center" label="分值">
          <template slot-scope="{ row }">
            {{ row.score || '--' }}
          </template>
        </el-table-column>
        <!-- <el-table-column show-overflow-tooltip align="center" label="剩余分值">
          <template slot-scope="{ row }">
            {{ row.leftScore || '--' }}
          </template>
        </el-table-column> -->
        <el-table-column header-align="center" label="评分标准">
          <template slot-scope="{ row }">
            {{ row.scoreMethod || '--' }}
          </template>
        </el-table-column>

        <el-table-column
          :width="isEdit ? '140' : '75'"
          prop="rwScore"
          align="center"
        >
          <template slot="header"
            ><span v-if="isEdit" style="color: #f56c6c">*</span> 扣/加分
          </template>
          <template slot-scope="{ row }">
            <!-- <el-form-item
              label-width="0"
              :prop="'stageList.' + scope.$index + '.rwScore'"
              :rules="rules.rwScore"
            > -->
            <!-- :placeholder="`请输入不大于${row.currentAssessScore}不小于0的分数,两位小数`" -->
            <!-- :placeholder="`0-${row.currentAssessScore},两位小数`" -->
            <el-input
              v-if="isEdit"
              v-model.trim="row.rwScore"
              clearable
              :maxLength="13"
              @clear="calculateScore(row, 'clear')"
              @input="calculateScore(row)"
            >
              <template slot="prepend">{{
                row.addScore === '0' ? '-' : '+'
              }}</template></el-input
            >
            <!-- </el-form-item> -->
            <template v-else>
              <!-- {{ row.rwScore || '--' }} -->
              {{ !row.rwScore ? '' : row.addScore === '0' ? '-' : '+'
              }}{{ row.rwScore || '--' }}
            </template>
          </template>
        </el-table-column>
        <el-table-column width="140" header-align="center">
          <template slot="header"> 扣/加分原因 </template>
          <template slot-scope="{ row }">
            <el-input
              v-if="isEdit"
              v-model.trim="row.rwCause"
              type="textarea"
              :maxlength="50"
              clearable
              placeholder="请输入字符，50字以内"
            ></el-input>
            <template v-else>
              {{ row.rwCause || '无' }}
            </template>
          </template>
        </el-table-column>
        <el-table-column width="100" align="center" label="操作">
          <template slot-scope="{ row, $index }">
            <!-- <el-button

              type="text"
              v-if="!!id && $route.query.status === '1'"
              icon="el-icon-upload2"
              @click="supervise(row)"
              >发起督办
            </el-button> -->
            <el-button
              type="text"
              v-if="!isEdit"
              :disabled="row.linkDataList && !row.linkDataList.length"
              @click="subquery(row)"
            >
              关联查询
            </el-button>
            <el-button type="text" v-if="isEdit" @click="del(row, $index)"
              >删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-form>
    <AddAssessMethod
      :isEdit="isEdit"
      :id="id"
      :formInfo="formInfo"
      ref="asessMethod"
      @save-success="methodSaveSuccess"
    />
    <correlatedSubqueryVue ref="correlated"></correlatedSubqueryVue>
  </div>
</template>
<script>
  import { ShowFile } from '@/components/yk-upload-file';
  import AddAssessMethod from './add-assess-method.vue';
  import { deepClone, calculate } from '@/util/util.js';
  import correlatedSubqueryVue from '../../special-examination/specialized-check/components/correlated-subquery.vue';
  import reg from '@/util/regexp';
  export default {
    name: 'solution-configuration',
    components: {
      ShowFile,
      AddAssessMethod,
      correlatedSubqueryVue
    },
    props: {
      formInfo: {
        type: Object,
        default: () => {}
      },
      form: {
        type: Object,
        default: () => {}
      },
      isEdit: {
        type: Boolean,
        default: false
      },
      id: {
        type: String,
        default: ''
      }
    },
    data() {
      return {
        isLoading: false,
        currentRow: {},
        rules: {
          rwScore: [
            {
              required: true,
              message: '请输入',
              trigger: 'blur'
            },
            {
              pattern: reg.regTwoNumber,
              message: '10位正整数或2位小数',
              trigger: 'blur'
            }
          ]
        }
      };
    },
    methods: {
      supervise(row) {
        // 业务类型 rwSourceType 1 项目申报 2专项检查 3 过程考核 4 任务下发"
        this.$router.push({
          path: '/supervise-business/initiate-supervise/add',
          query: {
            businessType: 3, // 过程考核
            businessId: row.evaluateId
          }
        });
      },
      subquery(row) {
        let list = row.linkDataList || [];
        // type 1 源单 2目标单
        let obj = {};
        obj.source = list.filter((item) => item.type === 1);
        obj.target = list.filter((item) => item.type === 2);
        this.$refs.correlated.show(obj);
      },
      getSummaries(param) {
        const { columns, data } = param;
        const sums = [];
        columns.forEach((column, index) => {
          if (index === 0) {
            sums[index] = '合计';
            return;
          }
          const values = data.map((item) => item[column.property]);
          if (!values.every((value) => isNaN(value))) {
            if (['rwScore'].includes(column.property)) {
              const values = data.map((item) => {
                let val = isNaN(Number(item[column.property] || 0))
                  ? 0
                  : Number(item[column.property] || 0);
                if (item.addScore === '0') {
                  return 0 - val;
                } else {
                  return val;
                }
              });
              sums[index] = calculate('add', ...values).toFixed(2);
              if (['rwScore'].includes(column.property)) {
                this.formInfo.rwTotalScore = sums[index] + '';
              }
            } else {
              const res = new Map();
              const key = 'evaluateTargetId';
              for (const item of data) {
                res.set(item[key], item);
              }
              let uniqueArr = [...res.values()];
              const values = uniqueArr.map((item) =>
                Number(item[column.property])
              );
              sums[index] = calculate('add', ...values).toFixed(2);
            }
            // sums[index] += ' 元';
          } else {
            sums[index] = ' ';
          }
        });
        console.log(sums);
        return sums;
      },
      calculateScore(row) {
        console.log('calculateScore', row);
        this.setGetScore(row);
      },
      // 设置得分 考核
      setGetScore(row) {
        let list = this.form.stageList;
        let evaluateList = list.filter(
          (item) => item.evaluateTargetId === row.evaluateTargetId
        );
        let currentAssessScore = Number(row.currentAssessScore || 0);

        let total = row.addScore === '0' ? currentAssessScore : 0;
        let isAdd = row.addScore === '0' ? 'sub' : 'add';
        evaluateList.forEach((item) => {
          total = calculate(isAdd, total, Number(item.rwScore || 0));
        });

        console.log('total', total);
        if (total > currentAssessScore || total < 0) {
          row.rwScore = '';
          // this.calculateScore(row, 'clear');
          return this.$message.warning(
            '扣/加分不得大于' + currentAssessScore + '分不得小于0分'
          );
        }
        let isNum = reg.regTwoNumber.test(total);
        if (!isNum) {
          row.rwScore = '';
          // this.calculateScore(row, 'clear');
          return this.$message.warning('请输入，数值、两位小数');
        }
        // 打分是否为空
        // let isEmput = evaluateList.every((item) => !item.rwScore);
        evaluateList.forEach((item) => {
          item.getScore = total + '';
        });
      },
      cancelcurrentRow() {
        this.$refs.singleTable.setCurrentRow();
        this.currentRow = {};
      },
      // 合并
      objectSpanMethod({ row, rowIndex, columnIndex }) {
        let classifyColumn = [1];
        if (classifyColumn.includes(columnIndex)) {
          let colspan = this.getDynamicColspan('classifyNumber', 'classifyId');
          // console.log('colspan classifyNumber', colspan);
          if (colspan.includes(rowIndex)) {
            return {
              rowspan: row.classifyNumber,
              colspan: 1
            };
          } else {
            return {
              rowspan: 0,
              colspan: 0
            };
          }
        }
        let mergeColumn = [2, 3, 4];
        if (mergeColumn.includes(columnIndex)) {
          let colspan = this.getDynamicColspan(
            'evaluateNumber',
            'evaluateTargetId'
          );
          // console.log('colspan evaluateNumber', colspan);
          if (colspan.includes(rowIndex)) {
            return {
              rowspan: row.evaluateNumber,
              colspan: 1
            };
          } else {
            return {
              rowspan: 0,
              colspan: 0
            };
          }
        }
      },
      // 获取动态开始合并行号 = 排序、数量
      getDynamicColspan(flag, key) {
        let list = this.form.stageList;
        // debugger;
        let uniqueArr = this.uniqueFunc(list, flag, key);
        // let arr = list.map((item) => item.columnIndicator);
        return uniqueArr;
      },
      // 计算开始合并的初始行号
      uniqueFunc(arr, flag, key) {
        // 去重
        const res = new Map();
        for (const item of arr) {
          res.set(item[key], item);
        }
        let uniqueArr = [...res.values()];

        let classifyId = uniqueArr.map((item) => item[flag]);
        // debugger;
        // 计算开始合并的初始行号
        let additionArr = [];
        classifyId.forEach((e, i) => {
          if (i === 0) {
            additionArr.push(0, e);
          } else {
            additionArr.push(additionArr[i] + e);
          }
        });
        return additionArr;
      },
      validForm() {
        let stageList = this.form.stageList;
        for (let index = 0; index < stageList.length; index++) {
          const element = stageList[index];
          if (!element.rwScore) {
            this.$message.warning(`请填写第${index + 1}行考核扣/加分字段`);
            return false;
          }
        }
        return true;
        // return !!length;
        // let bool = false;
        // this.$refs.form.validate((valid) => {
        //   bool = valid;
        // });
        // return bool;
      },
      methodSaveSuccess(methodArr) {
        this.form.stageList = deepClone(methodArr) || [];
      },
      // 弹窗确定
      saveSuccess(arr) {
        const list = deepClone(arr);
        this.$emit('setStageList', list);
        this.cancelcurrentRow();
      },
      handleAdd() {
        let stageList = this.form.stageList;
        const list = deepClone(stageList);
        this.$refs.asessMethod.show(list, this.currentRow.classifyId);
      },
      // 删除
      del(row, index) {
        let stageList = this.form.stageList;
        stageList.splice(index, 1);
        // 重新计算行号
        // stageList.forEach((item) => {
        //   let columnIndicator = this.getNum(stageList, item.classifyId);
        //   item.columnIndicator = columnIndicator;
        // });
        stageList.forEach((item) => {
          let classifyNumber = this.getNum(
            stageList,
            'classifyId',
            item.classifyId
          );
          item.classifyNumber = classifyNumber;
          let evaluateNumber = this.getNum(
            stageList,
            'evaluateTargetId',
            item.evaluateTargetId
          );
          item.evaluateNumber = evaluateNumber;
        });
      },
      getNum(arr, key, id) {
        let filterList = arr.filter((item) => item[key] === id);
        return filterList.length;
      },
      // 保存、提交
      save(submitFlag) {
        this.$refs.form.validate((valid) => {
          if (valid) {
            this.isLoading = true;
            if (this.form.stageList && this.form.stageList.length) {
              let param = {
                ...this.form,
                submitFlag
              };
              this.applyAdd(param);
            } else {
              this.isLoading = false;
              this.$message.error('请选择配送物料后提交/保存！');
            }
          }
        });
      },
      // 保存、提交接口
      async applyAdd(param) {
        try {
          // let url = this.statusFlag === '0' ? applyEdit : applyAgainEdit;
          // await url(this.queryId, param);
          let msg = param.submitFlag ? '提交成功' : '保存成功';
          this.$message.success(msg);
          this.back();
        } catch (error) {
          console.log(error);
        } finally {
          this.isLoading = false;
        }
      }
    }
  };
</script>
<style lang="scss">
  .el-table .warning-row-sty {
    background: #fffbf2;
  }

  .table-info {
    margin-bottom: 15px;
    padding: 5px 8px;
    color: #666;
    font-size: 14px;
    background-color: rgba(230, 247, 255, 100%);
    border-color: rgba(145, 213, 255, 100%);
    border-style: solid;
    border-width: 1px;
    border-radius: 4px;

    i {
      margin: 0 5px;
      color: #108ee9;
    }

    span {
      color: #f39a23;
    }
  }

  .past,
  .precede {
    font-size: 18px;
    font-style: normal;
  }

  .past {
    color: rgba(230, 52, 34, 100%);
  }

  .precede {
    color: #34c2a2;
  }

  .delay,
  .normality,
  .notStarted {
    padding: 2px 5px;
    font-style: normal;
  }

  .delay {
    color: #fff;
    background-color: rgba(230, 52, 34, 100%);
  }

  .normality {
    color: #34c2a2;
    background-color: #ddf7f2;
  }

  .notStarted {
    color: #b3b3b3;
    background-color: #efefef;
  }
</style>
