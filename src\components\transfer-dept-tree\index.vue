<template>
  <el-dialog
    @open="getDeptList"
    :title="title"
    :visible.sync="hVisible"
    width="544px"
    append-to-body
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="transfer-tree-dialog"
  >
    <div v-loading="loading" class="tranferbox">
      <div class="conbox">
        <div class="all-select">
          <el-checkbox v-model="checkAll" :indeterminate="indeterminate">
            全选
          </el-checkbox>
        </div>
        <div class="titbox">
          <el-input
            v-model="filterText"
            placeholder="搜索"
            suffix-icon="el-icon-search"
          ></el-input>
        </div>
        <div class="wordbox">
          <el-tree
            ref="tree"
            @check="onCheck"
            :data="data"
            :props="props"
            show-checkbox
            node-key="id"
            default-expand-all
            :check-strictly="true"
            :filter-node-method="filterNode"
            class="filter-tree"
          >
            <span slot-scope="{ node }" class="custom-tree-node">
              <img
                v-oss
                class="icon-tree-depart"
                src="/org/icon_tree_depart.png"
              />
              <span class="tree-label">{{ node.data.title }}</span>
            </span>
          </el-tree>
        </div>
      </div>
      <div class="conbox">
        <div class="all-select">
          <div>已选 {{ checkedNodes.length }} 个部门</div>
        </div>
        <div class="wordbox">
          <div class="dept-selected">
            <div v-for="(item, index) in checkedNodes" :key="index">
              <div class="inli">
                <img v-oss src="/org/icon_tree_depart.png" />
                <span>{{ item.title }}</span>
                <el-button
                  @click="removeData(item)"
                  type="text"
                  icon="el-icon-circle-close"
                ></el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button @click="submit" type="primary">确定</el-button>
    </span>
  </el-dialog>
</template>
<script>
  import { getTreeList } from '@/api/system/dept';
  import { treeToList, deepClone } from '@/util/util';
  export default {
    props: {
      visible: {
        type: Boolean,
        default: false
      },
      defaultCheckedIds: {
        type: Array,
        default() {
          return [];
        }
      },
      multiple: {
        type: Boolean,
        default: false
      },
      title: {
        type: String,
        default: '选择部门'
      }
    },
    data() {
      return {
        loading: false,
        data: [],
        props: {
          disabled: this.treeDisabled
        },
        deptList: [],
        checkedNodes: [],
        filterText: ''
      };
    },
    computed: {
      hVisible: {
        get() {
          return this.visible;
        },
        set(val) {
          this.$emit('update:visible', val);
        }
      },
      checkAll: {
        get() {
          return this.checkedNodes.length === this.deptList.length;
        },
        set(val) {
          this.checkedNodes = val ? this.deptList : [];
          this.$refs.tree.setCheckedNodes(this.checkedNodes);
        }
      },
      indeterminate() {
        return (
          !!this.checkedNodes.length &&
          this.checkedNodes.length < this.deptList.length
        );
      }
    },
    watch: {
      filterText(val) {
        this.$refs.tree.filter(val);
      }
    },
    methods: {
      getDeptList() {
        this.loading = true;
        getTreeList()
          .then((res) => {
            this.loading = false;
            this.data = res.data.data || [];
            this.deptList = treeToList(this.data);
            this.$nextTick(() => {
              if (this.$refs.tree) {
                this.$refs.tree.setCheckedKeys(this.defaultCheckedIds);
                this.onCheck();
              }
            });
          })
          .catch(() => {
            this.loading = false;
          });
      },
      filterNode(value, data) {
        if (!value) {
          return true;
        }
        return data.title.indexOf(value) !== -1;
      },
      treeDisabled() {
        return !this.multiple && this.checkedNodes.length;
      },
      // 点击选择部门
      onCheck() {
        this.checkedNodes = this.$refs.tree.getCheckedNodes();
      },
      // 删除已选部门
      removeData(data) {
        let index = this.checkedNodes.findIndex((item) => item.id === data.id);
        if (index > -1) {
          this.checkedNodes.splice(index, 1);
        }
        this.$refs.tree.setChecked(data, false);
      },
      submit() {
        // 返回参数：1.ids；2.list
        this.$emit(
          'change',
          Array.from(this.checkedNodes, (item) => item.id),
          deepClone(this.checkedNodes)
        );
        this.handleClose();
      },
      handleClose() {
        this.filterText = '';
        this.hVisible = false;
      }
    }
  };
</script>
<style lang="scss">
  .transfer-tree-dialog {
    .el-tree-node__expand-icon.is-leaf {
      background: transparent !important;
    }
  }
</style>
<style lang="scss" scoped>
  .tranferbox {
    display: flex;
    justify-content: space-between;

    .conbox {
      display: flex;
      flex-direction: column;
      width: 240px;
      height: 400px;
      border: 1px solid #d9d9d9;
      border-radius: 4px;

      .all-select {
        padding: 12px 16px;
        color: #333;
        font-weight: 400;
        font-size: 14px;
        line-height: 19px;
        border-bottom: 1px solid #d9d9d9;
      }

      .titbox {
        padding: 5px 16px;
      }

      .wordbox {
        flex: 1;
        padding: 0 16px;
        overflow: auto;

        .custom-tree-node {
          position: relative;
          display: flex;
          align-items: center;
          width: 100%;

          .icon-tree-depart {
            width: 16px;
            height: 16px;
            margin-right: 4px;
            margin-left: 4px;
          }

          .tree-label {
            color: #333;
            font-weight: 400;
            font-size: 14px;
            line-height: 23px;
          }
        }
      }

      .dept-selected {
        padding: 16px;

        .inli {
          position: relative;
          display: flex;
          align-items: center;
          padding: 3px 0;

          img {
            width: 16px;
            height: 16px;
            margin-right: 4px;
          }

          span {
            width: 85%;
            color: #333;
            font-weight: 400;
            font-size: 14px;
            line-height: 1.4;
          }

          i {
            position: absolute;
            right: 0;
          }

          .el-button {
            padding: 0;
          }
        }
      }
    }
  }
</style>
