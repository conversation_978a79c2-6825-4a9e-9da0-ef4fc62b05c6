import request from '@/router/axios';
// 获取工作流列表
export const getflowPage = (params) => {
  return request({
    url: `/api/zbusiness-flow/workflow/page`,
    method: 'get',
    params
  });
};
// 获取工作流列表
export const getOperatorList = (params) => {
  return request({
    url: `/api/zbusiness-flow/workflow/getOperatorList`,
    method: 'get',
    params
  });
};
// 获取工作流详情
export const getflowDetail = (params) => {
  return request({
    url: `/api/zbusiness-flow/workflow/detail`,
    method: 'get',
    params
  });
};
// 保存编辑工作流
export const saveflow = (data) => {
  return request({
    url: `/api/zbusiness-flow/workflow/saveOrUpdate`,
    method: 'post',
    data
  });
};
// 获取工作流配置
export const getflowConfig = (params) => {
  return request({
    url: `/api/zbusiness-flow/workflow/fetchByFlowId`,
    method: 'get',
    params
  });
};
// 获取角色
export const getroleList = (params) => {
  return request({
    url: `/api/szyk-system/role/tree`,
    method: 'get',
    params
  });
};
// 编辑配置节点
export const saveConfig = (data) => {
  return request({
    url: `/api/zbusiness-flow/workflow/updateStep`,
    method: 'post',
    data
  });
};
// -----
// 获取下个节点信息
export const getNext = (data) => {
  return request({
    url: `/api/zbusiness-flow/instance/getNextStep`,
    method: 'post',
    data
  });
};
// 提报业务流程
export const initiateFlow = (data) => {
  return request({
    url: `/api/zbusiness-flow/instance/initiateFlow`,
    method: 'post',
    data
  });
};
// 获取审核流详情
export const getInstance = (params) => {
  return request({
    url: `/api/zbusiness-flow/instance/getInstance`,
    method: 'get',
    params
  });
};
// 获取流程图
export const getInstanceGraph = (params) => {
  return request({
    url: `/api/zbusiness-flow/instance/getInstanceGraph`,
    method: 'get',
    params
  });
};
// 审批中心-分页接口
export const approvePage = (data) => {
  return request({
    url: `/api/zbusiness-flow/instance/approvePage`,
    method: 'post',
    data
  });
};
// 审批中心-验收审批列表
export const approveCheckPage = (data) => {
  return request({
    url: `/api/zbusiness-flow/instance/projectAccept/approvePage`,
    method: 'post',
    data
  });
};
// 撤销审批
export const approveCancel = (data) => {
  return request({
    url: `/api/zbusiness-flow/instance/cancel`,
    method: 'post',
    data
  });
};
// 退回
export const returnFlow = (data) => {
  return request({
    url: `/api/zbusiness-flow/instance/returnFlow`,
    method: 'post',
    data
  });
};
// 审批退回-获取退回节点列表
export const getReturnProcess = (data) => {
  return request({
    url: `/api/zbusiness-flow/instance/getReturnProcess`,
    method: 'post',
    data
  });
};
// 审批 通过
export const approvedVia = (data) => {
  return request({
    url: `/api/zbusiness-flow/instance/approved`,
    method: 'post',
    data
  });
};
// 项目申报 提报校验数据
export const checkSubmit = (data) => {
  return request({
    url: `/api/project_base/checkData`,
    method: 'post',
    data
  });
};
// 项目进度验收 提报校验数据
export const checkProgressSubmit = (data) => {
  return request({
    url: `/api/project_progress/checkData`,
    method: 'post',
    data
  });
};
// 获取获取事前事件校验标识 是否显示 审查意见 按钮
export const getReviewBtn = (params) => {
  return request({
    url: `/api/zbusiness-flow/instance/getBeforeEvent`,
    method: 'get',
    params
  });
};
// 审批校验数据 是否提报过 审查意见
export const checkReviewComment = (data) => {
  return request({
    url: `/api/zbusiness-flow/instance/checkData`,
    method: 'post',
    data
  });
};
// 校验提报数据
export const instanceCheckSubmit = (data) => {
  return request({
    url: `/api/zbusiness-flow/instance/checkSubmit`,
    method: 'post',
    data
  });
};

// 获取 审查意见 详情
export const getReviewDetail = (params) => {
  return request({
    url: `/api/project_base/getApprovalOpinion`,
    method: 'get',
    params
  });
};
// 审查意见 保存
export const reviewSave = (data) => {
  return request({
    url: `/api/project_base/saveApprovalOpinion`,
    method: 'post',
    data
  });
};
// 上传审查意见附件
export const uploadReviewFile = (data) => {
  return request({
    url: `/api/zbusiness-flow/instance/uploadFile`,
    method: 'post',
    data
  });
};
// 获取审核时上传的附件
export const getReviewFile = (params) => {
  return request({
    url: `/api/zbusiness-flow/instance/getInstanceFile`,
    method: 'get',
    params
  });
};

// 懒加载当前组织下的树形结构
export const getFlowLazyTree = (params) => {
  return request({
    url: `/api/szyk-system/dept/current/child/lazy-tree`,
    method: 'get',
    params
  });
};
// 懒加载当前组织下的树形结构
export const getnextStepList = (params) => {
  return request({
    url: `/api/zbusiness-flow/instance/getStep`,
    method: 'get',
    params
  });
};
// 查询当前用户的联络人信息
export const userContact = (params) => {
  return request({
    url: `/api/szyk-user/userContact`,
    method: 'get',
    params
  });
};
// 项目申报-刷新项目联络人
export const flushContractPerson = (data) => {
  return request({
    url: `/api/zbusiness-flow/instance/flushContractPerson`,
    method: 'post',
    data
  });
};
// 批量导出项目申报办理
export const exportExcel = (data) => {
  return request({
    url: '/api/zbusiness-flow/instance/downloadFile',
    method: 'post',
    responseType: 'blob',
    data
  });
};
// 更新集团联络人的办理状态
export const updateContactApprovalStatus = (data) => {
  return request({
    url: `/api/zbusiness-flow/instance/updateContactApprovalStatus`,
    method: 'post',
    data
  });
};
