import request from '@/router/axios';

// 列表
export const getPage = (data) => {
  return request({
    url: '/api/zbusiness-task/task/page',
    method: 'post',
    data
  });
};

// 验证业务范围是否变更
export const issueValid = (data) => {
  return request({
    url: '/api/zbusiness-task/task/issueValid',
    method: 'post',
    data
  });
};

// 归档
export const pigeonhole = (data) => {
  return request({
    url: '/api/zbusiness-task/task/placeOnFile',
    method: 'post',
    data
  });
};

// 保存-编辑
export const submitFetch = (data) => {
  return request({
    url: '/api/zbusiness-task/task/issue',
    method: 'post',
    data
  });
};

// 详情
export const getDetail = (params) => {
  return request({
    url: '/api/zbusiness-task/task/detail',
    method: 'get',
    params
  });
};

// 关联查询
export const getLinkData = (params) => {
  return request({
    url: '/api/zbusiness-task/task/linkData',
    method: 'get',
    params
  });
};

// 一键选择联络人
export const getUsersByRole = (data) => {
  return request({
    url: '/api/zbusiness-task/task/getUsersByRole',
    method: 'post',
    data
  });
};

// 导出填报
export const exportNoteContent = (data) => {
  return request({
    url: '/api/zbusiness-task/task/exportNoteContent',
    method: 'post',
    responseType: 'blob',
    data
  });
};

// 任务退回
export const taskBack = (data) => {
  return request({
    url: '/api/zbusiness-task/task/back',
    method: 'post',
    data
  });
};

// 进度跟踪排序
export const postIssueSort = (data) => {
  return request({
    url: '/api/zbusiness-task/task/issueSort',
    method: 'post',
    data
  });
};

// 退回记录查询
export const getBackLog = (params) => {
  return request({
    url: '/api/zbusiness-task/task/listBackHistory',
    method: 'get',
    params
  });
};
// 任务撤销
export const taskRevoke = (data) => {
  return request({
    url: '/api/zbusiness-task/task/revoke',
    method: 'post',
    data
  });
};
// 任务删除
export const batchDelete = (data) => {
  return request({
    url: '/api/zbusiness-task/task/deleteTask',
    method: 'post',
    data
  });
};
