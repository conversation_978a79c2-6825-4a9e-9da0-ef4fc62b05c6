import request from '@/router/axios';

// 列表
export const getPage = (data) => {
  return request({
    url: '/api/zbusiness-task/childtask/page',
    method: 'post',
    data
  });
};

// 保存
export const save = (data) => {
  return request({
    url: '/api/zbusiness-task/childtask/save',
    method: 'post',
    data
  });
};

// 转发
export const dispatch = (data) => {
  return request({
    url: '/api/zbusiness-task/childtask/dispatch',
    method: 'post',
    data
  });
};
// 保存并提报
export const saveAndSubmit = (data) => {
  return request({
    url: '/api/zbusiness-task/childtask/saveAndSubmit',
    method: 'post',
    data
  });
};
// 填报内容是否为空,提报时使用
export const isHaveSubmitNote = (params) => {
  return request({
    url: '/api/zbusiness-task/childtask/isHaveSubmitNote',
    method: 'get',
    params
  });
};
// 提报
export const submit = (data) => {
  return request({
    url: '/api/zbusiness-task/childtask/submit',
    method: 'post',
    data
  });
};

// 详情
export const getDetail = (params) => {
  return request({
    url: '/api/zbusiness-task/childtask/detail',
    method: 'get',
    params
  });
};

// 导出填报
export const exportNoteContent = (data) => {
  return request({
    url: '/api/zbusiness-task/childtask/exportNoteContent',
    method: 'post',
    responseType: 'blob',
    data
  });
};

// 下载
export const downloadFile = (data) => {
  return request({
    url: '/api/zbusiness-task/childtask/downloadFile',
    method: 'post',
    responseType: 'blob',
    data
  });
};

// 填报内容分页,子级提报的
export const getChildSubmitNotePage = (data) => {
  return request({
    url: '/api/zbusiness-task/task/childSubmitNotePage',
    method: 'post',
    data
  });
};

// 填报内容分页,自己提报的
export const getSelfSubmitNotePage = (data) => {
  return request({
    url: '/api/zbusiness-task/childtask/selfSubmitNotePage',
    method: 'post',
    data
  });
};
