import { setStore, getStore, removeStore } from '@/util/store';
import website from '@/config/website';
import Cookies from 'js-cookie';

const common = {
  state: {
    language: getStore({ name: 'language' }) || 'zh',
    isCollapse: false,
    isFullScren: false,
    isMenu: true,
    socket: null,
    isShade: false,
    screen: -1,
    isLock: getStore({ name: 'isLock' }) || false,
    showTag: true,
    showDebug: false, // 右上角错误日志
    showCollapse: true,
    showSearch: true,
    showLock: false, // 右上角锁屏
    showLanguage: false, // 右上角显示语言切换
    showFullScren: true,
    showTheme: true,
    showMenu: true,
    showColor: false, // 右上角皮肤换色
    colorName: getStore({ name: 'colorName' }) || '#409EFF',
    themeName: getStore({ name: 'themeName' }) || 'theme-bule',
    lockPasswd: getStore({ name: 'lockPasswd' }) || '',
    website: website,
    refreshKey: '', // 刷新
    cockpitChartsList: [],
    size: Cookies.get('size') || 'small',
    routerViewkey: undefined
  },
  mutations: {
    SET_ROUTER_KEY: (state) => {
      state.routerViewkey = Math.random().toString(36).slice(-8);
    },
    SET_SIZE: (state, size) => {
      state.size = size;
      Cookies.set('size', size);
    },
    SET_LANGUAGE: (state, language) => {
      state.language = language;
      setStore({
        name: 'language',
        content: state.language
      });
    },
    SET_SHADE: (state, active) => {
      state.isShade = active;
    },
    SET_REFRESHKEY: (state) => {
      let key = new Date().getTime();
      state.refreshKey = key;
    },
    SET_SOCKET: (state, socket) => {
      state.socket = socket;
    },
    SET_COLLAPSE: (state) => {
      state.isCollapse = !state.isCollapse;
    },
    SET_FULLSCREN: (state) => {
      state.isFullScren = !state.isFullScren;
    },
    SET_IS_MENU: (state, menu) => {
      state.isMenu = menu;
    },
    SET_LOCK: (state) => {
      state.isLock = true;
      setStore({
        name: 'isLock',
        content: state.isLock,
        type: 'session'
      });
    },
    SET_SCREEN: (state, screen) => {
      state.screen = screen;
    },
    SET_COLOR_NAME: (state, colorName) => {
      state.colorName = colorName;
      setStore({
        name: 'colorName',
        content: state.colorName
      });
    },
    SET_THEME_NAME: (state, themeName) => {
      state.themeName = themeName;
      setStore({
        name: 'themeName',
        content: state.themeName
      });
    },
    SET_LOCK_PASSWD: (state, lockPasswd) => {
      state.lockPasswd = lockPasswd;
      setStore({
        name: 'lockPasswd',
        content: state.lockPasswd,
        type: 'session'
      });
    },
    CLEAR_LOCK: (state) => {
      state.isLock = false;
      state.lockPasswd = '';
      removeStore({
        name: 'lockPasswd',
        type: 'session'
      });
      removeStore({
        name: 'isLock',
        type: 'session'
      });
    },
    ADD_COCKPIT_CHART: (state, chart) => {
      !state.cockpitChartsList.includes(chart) &&
        state.cockpitChartsList.push(chart);
    },
    CLEAR_COCKPIT_CHARTS: (state) => {
      state.cockpitChartsList = [];
    }
  }
};
export default common;
