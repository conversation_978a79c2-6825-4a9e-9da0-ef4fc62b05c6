import request from '@/router/axios';
// 列表
export const getPage = (data) => {
  return request({
    url: '/api/rw-result/list',
    method: 'post',
    data
  });
};
// 导出 考核结果
export const exportExcel = (data) => {
  return request({
    url: '/api/rw-result/export',
    method: 'post',
    responseType: 'blob',
    data
  });
};
// 导出 考核结果明细
export const exportDetail = (data) => {
  return request({
    url: '/api/rw-result/exportDetail',
    method: 'post',
    responseType: 'blob',
    data
  });
};
// 明细
export const getDetail = (data) => {
  return request({
    url: '/api/rw-result/detail',
    method: 'post',
    data
  });
};
// 获取默认值
export const getDefaultParam = (data) => {
  return request({
    url: '/api/rw-result/getRwResultDefaultParam',
    method: 'post',
    data
  });
};
// 查询用户所在部门及上级下级部门
export const getDeptList = (params) => {
  return request({
    url: `/api/szyk-system/dept/up/dept/down`,
    method: 'get',
    params
  });
};
// 考核周期明细-列表
export const getPeriodList = (params) => {
  return request({
    url: `/api/rw-period/detail/list`,
    method: 'get',
    params
  });
};
// 上级和当前部门
export const getCurrentUp = (params) => {
  return request({
    url: `/api/szyk-system/dept/upDept`,
    method: 'get',
    params
  });
};
// 根据被考核记录过滤发起组织
export const filterInitiateOrgList = (params) => {
  return request({
    url: `/api/rw-manage/filterInitiateOrgList`,
    method: 'get',
    params
  });
};
// 根据被考核记录过滤考核周期
export const filterPeriodList = (params) => {
  return request({
    url: `/api/rw-manage/filterPeriodList`,
    method: 'get',
    params
  });
};
