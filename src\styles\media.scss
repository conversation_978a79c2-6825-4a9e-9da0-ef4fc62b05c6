.avue-left,
.avue-header,
.avue-top,
.avue-logo,
.avue-layout
.login-logo,
.avue-main {
  transition: all .3s;
}

.avue-contail {
  width: 100%;
  height: 100%;
  background: #f0f2f5;
  background-repeat: no-repeat;
  background-size: 100%;
}


.avue-left {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1025;
  width: 180px;
  height: 100%;
}

.avue--collapse {
  .avue-left,
  .avue-logo {
    width: 60px;
  }

  .avue-header {
    padding-left: 60px;
  }

  .avue-main {
    left: 60px;
    width: calc(100% - 60px);
  }
}

.avue-header {
  box-sizing: border-box;
  width: 100%;
  padding-left: 180px;
  background-color: #fff;
}

.avue-main {
  position: absolute;
  left: 180px;
  z-index: 1026;
  box-sizing: border-box;
  width: calc(100% - 180px);
  height: calc(100% - 64px);
  padding: 0;
  padding-bottom: 20px;
  overflow: hidden;
  background: #f0f2f5;
  transition: all 0.5s;

  &--fullscreen {
    left: 0;
    width: 100%;
  }
}

.avue-view {
  box-sizing: border-box;
  width: 100%;

  // padding: 0 10px !important;
}

.avue-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 1300px;
  margin: 0 auto;
  padding: 0 22px;

  .logo {
    margin-left: -50px;
  }

  .copyright {
    color: #666;
    font-size: 12px;
    line-height: 1.5;
  }
}

.avue-shade {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1024;
  display: none;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 30%);

  &--show {
    display: block;
  }
}

@media screen and (max-width: 992px) {
  $width: 240px;

  // ele的自适应
  .el-dialog,
  .el-message-box {
    width: 98% !important;
  }

  // 登录页面
  .login-left {
    display: none !important;
  }

  .login-logo {
    margin-left: -30px;
    padding-top: 30px !important;
  }

  .login-weaper {
    width: 96% !important;
    margin: 0 auto;
  }

  .login-border {
    float: none !important;
    width: 100% !important;
    margin: 0 auto;
    padding: 40px;
    border-radius: 5px;
  }

  .login-main {
    width: 100% !important;
  }

  // 主框架
  .avue-tags {
    display: none;
  }

  .avue-left,
  .avue-logo {
    left: -$width;
  }

  .avue-main {
    left: 0;
    width: 100%;
  }

  .avue-header {
    margin-bottom: 15px;
    padding-left: 15px;
  }

  .top-bar__item {
    display: none;
  }

  .avue--collapse {
    .avue-left,
    .avue-logo {
      left: 0;
      width: $width;
    }

    .avue-main {
      left: $width;
      width: 100%;
    }

    .avue-header {
      padding: 0;
      transform: translate3d(230px, 0, 0);
    }

    .avue-shade {
      display: block;
    }
  }
}
