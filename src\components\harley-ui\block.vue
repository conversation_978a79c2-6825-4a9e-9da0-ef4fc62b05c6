<template>
  <div class="h-block">
    <div v-if="title" class="h-block-title">
      <div class="h-block-title-area">
        <div class="h-block-title-line"></div>
        <i v-if="icon" :class="icon" class="h-block-title-icon"></i>
        {{ title }}
      </div>
      <slot name="title-append"></slot>
    </div>
    <div class="h-block-content">
      <slot></slot>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'HBlock',
    props: {
      title: String,
      icon: String
    }
  };
</script>

<style lang="scss" scoped>
  @import '@/styles/element-ui';

  .h-block {
    // padding: 32px;
    background: #fff;
    border-radius: 4px;

    .h-block-title {
      line-height: 15px !important;
      border-left: 4px solid #409eff !important;
    }

    &.flex-column {
      display: flex;
      flex: 1;
      flex-direction: column;

      .h-block-content {
        display: flex;
        flex: 1;
        flex-direction: column;
      }
    }

    & + .h-block {
      margin-top: 15px;
    }

    .h-block-title {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .h-block-title-area {
        display: flex;
        align-items: center;
        font-weight: 550;
        font-size: 20px;

        .h-block-title-line {
          display: inline-block;
          width: 5px;
          height: 20px;
          margin-right: 10px;

          // background: $--color-primary;
        }

        .h-block-title-icon {
          margin-right: 10px;
          font-size: 20px;
        }
      }

      & + .h-block-content {
        padding: 32px 0 0;
      }
    }
  }
</style>

<style lang="scss">
  .h-block {
    .h-block {
      padding: 12px;
    }
  }
</style>
