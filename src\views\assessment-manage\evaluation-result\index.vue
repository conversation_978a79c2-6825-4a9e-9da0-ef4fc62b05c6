<template>
  <basic-container autoHeight>
    <search
      ref="search"
      :form="form"
      :systemNameList="systemNameList"
      :checkList="checkList"
      :searchLoading="searchLoading"
      :exportLoading="exportLoading"
      @search="onSearch"
      @exportResult="exportResult"
    />
    <div class="table-content">
      <el-table
        ref="multipleTable"
        v-loading="loading"
        :data="list"
        border
        stripe
        :header-cell-style="{ backgroundColor: '#fafafa' }"
        style="width: 100%"
        row-key="id"
        @selection-change="handleSelectionChange"
      >
        <!-- <el-table-column type="selection" :reserve-selection="true" width="50">
        </el-table-column> -->
        <el-table-column
          type="index"
          align="center"
          label="序号"
          width="40"
        ></el-table-column>
        <el-table-column
          prop="assessedOrgName"
          width="110"
          align="center"
          label="被考核单位"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column align="center" min-width="130" label="体系名称">
          <template slot-scope="{ row }">
            {{ row.schemeName || '--' }}
          </template>
        </el-table-column>
        <el-table-column align="center" width="110" label="考核周期">
          <template slot-scope="{ row }">
            {{ row.periodName || '--' }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="过程考核扣/加分" width="130">
          <template slot-scope="{ row }">
            {{
              [null, undefined, ''].includes(row.processAddDeductScore)
                ? '--'
                : Number(row.processAddDeductScore) > 0
                ? `+${row.processAddDeductScore}`
                : row.processAddDeductScore
            }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="专项检查扣/加分" width="130">
          <template slot-scope="{ row }">
            {{
              [null, undefined, ''].includes(row.assessAddDeductScore)
                ? '--'
                : Number(row.assessAddDeductScore) > 0
                ? `+${row.assessAddDeductScore}`
                : row.assessAddDeductScore
            }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="扣/加分合计" width="130">
          <template slot-scope="{ row }">
            {{
              [null, undefined, ''].includes(row.totalAddDeductScore)
                ? '0'
                : Number(row.totalAddDeductScore) > 0
                ? `+${row.totalAddDeductScore}`
                : row.totalAddDeductScore
            }}
          </template>
        </el-table-column>
        <el-table-column align="center" width="75" label="总分">
          <template slot-scope="{ row }">
            {{ [null, undefined, ''].includes(row.score) ? '--' : row.score }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="等级评定" width="75">
          <template slot-scope="{ row }">
            {{
              [null, undefined, ''].includes(row.levelName)
                ? '--'
                : row.levelName
            }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="80" align="center">
          <template slot-scope="{ row }">
            <el-button
              type="text"
              v-if="permission['evaluation-result-detail']"
              @click="handleDetails(row)"
              >考核明细</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <YkPagination
        :hidden="total === 0"
        :total="total"
        :page.sync="form.current"
        :limit.sync="form.size"
        @pagination="getList"
      />
      <ShowDetail ref="detail"></ShowDetail>
    </div>
  </basic-container>
</template>

<script>
  import Search from './search';
  import { phaseStatus, phaseDel } from '@/api/system/biz-dict';
  import {
    exportExcel,
    getPage,
    getPeriodList,
    getDefaultParam
  } from '@/api/assess-manage/examine-result';
  import { getSystemName } from '@/api/assess-manage';
  import { getDictList } from '@/api/common';
  import { dateNow } from '@/util/date';
  import NProgress from 'nprogress';
  import 'nprogress/nprogress.css';
  import { mapGetters } from 'vuex';
  import { downloadXls } from '@/util/util';
  import ShowDetail from './show-detail.vue';
  const form = {
    initiateOrgId: undefined,
    rwPeriodDetailId: undefined,
    schemeId: undefined,
    current: 1,
    size: 20
  };
  export default {
    components: { Search, ShowDetail },
    data() {
      return {
        loading: false,
        searchLoading: false,
        dictList: [],
        list: [],
        total: 0,
        form: { ...form },
        multipleSelection: [],
        delList: [],
        systemNameList: [],
        checkList: [],
        exportLoading: false,
        defaultParam: {}
      };
    },
    computed: {
      ...mapGetters(['permission']),
      tableHeight() {
        let height = document.documentElement.clientHeight;
        let calcHeight = null;
        if (height > 800) {
          calcHeight = (height - (65 + 40 + 10 + 10 + 60 + 190)) / 0.9;
        } else {
          calcHeight = (height - (65 + 40 + 10 + 10 + 60 + 190)) / 1;
        }
        return `${calcHeight}px`;
      }
    },
    watch: {
      'form.initiateOrgId': {
        handler: async function (val) {
          this.searchLoading = true;
          await this.getSystemName(val);
          await this.getCheckList(val);
          let { initiateOrgId, rwPeriodDetailId, schemeId } = this.form;
          if (initiateOrgId && rwPeriodDetailId && schemeId) {
            this.refresh();
          }
          this.searchLoading = false;
        }
      }
    },
    created() {
      this.getDefaultParam();
    },
    async activated() {
      await this.getDefaultParam();
      // await this.getList();
      this.$refs.search.submit();
    },
    methods: {
      async getDefaultParam() {
        try {
          const Func = getDefaultParam;
          const {
            data: { data }
          } = await Func();
          this.defaultParam = data || {};
          let { initiateOrgId } = this.defaultParam;
          this.form.initiateOrgId = initiateOrgId;
          // this.form.rwPeriodDetailId = rwPeriodDetailId;
          // this.form.schemeId = schemeId;
        } catch (e) {
          console.log(e);
        }
      },
      async getCheckList() {
        try {
          let { initiateOrgId: deptId } = this.form;
          if (!deptId) return;
          const Func = getPeriodList;
          const {
            data: { data }
          } = await Func({ deptId });
          this.checkList = data || [];
        } catch (e) {
          console.log(e);
        }
      },
      async getSystemName() {
        try {
          let { initiateOrgId } = this.form;
          if (!initiateOrgId) return;
          let {
            data: { data }
          } = await getSystemName({
            initiateOrgId
            // schemeStatus: '0',
            // assessedOrgId
          });
          this.systemNameList = data || [];
        } catch (error) {
          console.log(error);
        }
      },
      handleDetails(row) {
        this.$refs.detail.show(row);
      },
      exportResult() {
        // this.$confirm('是否导出考核结果数据?', '提示', {
        //   confirmButtonText: '确定',
        //   cancelButtonText: '取消',
        //   type: 'warning'
        // }).then(() => {
        NProgress.start();
        this.exportLoading = true;
        let params = {};
        try {
          exportExcel(Object.assign(params, this.form))
            .then((res) => {
              downloadXls(res.data, `考核结果数据表${dateNow()}.xlsx`);
              NProgress.done();
              this.exportLoading = false;
            })
            .catch(() => {
              this.exportLoading = false;
            });
        } catch (error) {
          this.exportLoading = false;
        }
        // });
      },
      handleSelectionChange(val) {
        this.multipleSelection = val;
      },
      // 新增
      add() {
        this.$refs.operate.addshow();
      },
      // 编辑
      edit(item) {
        this.$refs.operate.editshow(item);
      },
      // 删除
      del(row) {
        this.$confirm('确定删除 指标分类 ，是否继续？')
          .then(() => {
            let idList = [];
            if (row) {
              idList = [row.id];
            } else {
              idList = this.findIds(this.multipleSelection);
            }
            this.batchDel(idList);
          })
          .catch(() => {});
      },
      findIds(arr) {
        let ids = [];
        arr.forEach((item) => {
          ids.push(item.id);
        });
        return ids;
      },
      async batchDel(idList) {
        try {
          let res = await phaseDel({ idList });
          this.delList = res.data.data;
          this.$refs.delDialog.viewShow();
          this.$refs.multipleTable.clearSelection();
          let { current } = this.form;
          if (
            (this.list.length === 1 && current > 1) ||
            (this.list.length === 0 && current > 1)
          ) {
            this.form.current = current - 1;
          }
          this.request();
        } catch (e) {
          console.log(e);
        }
      },
      // 分页查询
      getList({ page, limit }) {
        Object.assign(this.form, {
          current: page,
          size: limit
        });
        this.request();
      },
      async request() {
        this.loading = true;
        try {
          // let { initiateOrgId, rwPeriodDetailId, schemeId } = this.form;
          let params = {
            ...this.form
          };
          const {
            data: { data }
          } = await getPage(params);
          const { total = 0, records = [] } = data ? data : {};
          this.total = total;
          this.list = records || [];
        } catch (e) {
          this.list = [];
        }
        this.loading = false;
      },
      async init() {
        try {
          const res = await getDictList({ code: 'enable_type' });
          if (res) {
            this.dictList = res.data.data;
          } else {
            this.dictList = [];
          }
        } catch (e) {
          this.list = [];
        }
        this.loading = false;
      },
      onSearch(param) {
        if (Array.isArray(param)) {
          return (this.list = []);
        }
        Object.assign(this.form, param);
        if (param.reset) {
          let { initiateOrgId } = this.defaultParam;
          this.form.initiateOrgId = initiateOrgId;
        }
        this.form.current = 1;
        this.request();
      },
      // 启用、停用
      onDelete(item) {
        this.$confirm(
          item.classifyStatus === '0'
            ? '确定 停用 此指标分类，是否继续？'
            : '确定 启用 此指标分类，是否继续？',
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        ).then(async () => {
          this.delItem(item);
        });
      },
      async delItem(item) {
        try {
          let params = {
            id: item.id,
            classifyStatus: item.classifyStatus === '0' ? '1' : '0'
          };
          await phaseStatus(params);
          this.$message.success(
            item.classifyStatus === '0' ? '状态停用成功！' : '状态启用成功！'
          );
          this.request();
        } catch (e) {
          console.log(e);
        }
      },
      resetPage() {
        this.form.current = 1;
      },
      refresh() {
        this.resetPage();
        this.request();
      }
    }
  };
</script>

<style lang="scss" scoped>
  .table-content {
    padding-bottom: 1px;
  }

  .el-form-item {
    margin-bottom: 10px;
  }

  /deep/ .el-table .el-table__header .cell {
    color: #909399 !important;
  }

  /deep/ .el-button--small span {
    font-size: 13px !important;
  }

  /deep/ .el-table .cell {
    font-size: 14px;
  }
</style>
