<template>
  <div>
    <slick-list
      v-model="list"
      lock-axis="y"
      :use-drag-handle="true"
      class="selectList"
    >
      <slick-item
        v-for="(li, i) of list"
        :key="i"
        :index="i"
        class="slick-item-list"
      >
        <el-tooltip effect="dark" content="拖动排序" placement="top">
          <span v-handle class="iconfont icon-paixu cursor-sort" />
        </el-tooltip>
        <el-input v-model.trim="li.value" maxlength="20"></el-input>
        <span
          v-if="list.length > 2"
          @click="deleteList(i)"
          class="iconfont icon-01"
        ></span>
      </slick-item>
    </slick-list>
    <el-button @click="addList" type="text" class="select-list-footer">
      <span class="iconfont icon-plus-0"></span>添加选项
    </el-button>
  </div>
</template>
<script>
  import { HandleDirective, SlickList, SlickItem } from 'vue-slicksort';
  import { deepClone } from '@/util/util.js';

  export default {
    components: { SlickList, SlickItem },
    directives: {
      handle: HandleDirective
    },
    props: {
      value: {
        type: Array,
        default: () => []
      }
    },
    data() {
      return {
        list: []
      };
    },
    watch: {
      list: {
        handler() {
          this.$emit('input', deepClone(this.list));
        },
        deep: true
      }
    },
    mounted() {
      this.list = deepClone(this.value);
    },
    methods: {
      addList() {
        if (this.list.length >= 200) {
          this.$message.error('超过上限');
          return;
        }
        this.list.push({ value: '' });
      },
      deleteList(i) {
        this.list.splice(i, 1);
      }
    }
  };
</script>
<style lang="scss">
  .slick-item-list {
    display: flex;
    align-items: center;

    .cursor-sort {
      margin-right: 4px;
      color: #d9d9d9;
      cursor: move;
    }
  }

  .selectList {
    & > div {
      display: flex;
      margin-bottom: 24px;
    }

    .cursor-sort {
      margin-right: 4px;
      color: #d9d9d9;
      cursor: move;
    }

    .icon-01 {
      margin-left: 4px;
      color: #d9d9d9;
      cursor: pointer;
    }
  }

  .select-list-footer {
    display: flex;
    align-items: center;
    padding: 0;

    span {
      color: #409eff;
    }
  }
</style>
