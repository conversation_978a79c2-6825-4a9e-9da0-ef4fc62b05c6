<template>
  <div class="captcha-click">
    <div class="captcha-click-content">
      <div class="captcha-click-bottom">
        <div class="captcha-click-refresh-btn" @click="refresh"></div>
      </div>
      <div class="captcha-click-move">
        <span class="captcha-click-move-span">请依次点击:</span>
        <img :src="data.sliderImage" class="captcha-click-tip-img" />
      </div>
      <div class="captcha-click-content" @click="handleClick($event)">
        <div class="captcha-click-bg-img-div">
          <img id="captcha-click-bg-img" :src="data.backgroundImage" alt />
        </div>
        <div class="captcha-click-bg-click-div"></div>
      </div>
    </div>
  </div>
</template>

<script>
  import { getCaptchaCode } from '@/api/user';

  export default {
    name: 'captcha-click',
    props: {},
    data() {
      return {
        clientWidth: null,
        clientHeight: null,
        data: {
          backgroundImage: null,
          sliderImage: null,
          id: null
        },
        checkData: {
          id: null,
          track: null
        },
        startX: 0,
        startY: 0,
        end: 206,
        moveX: 0,
        movePercent: 0,
        // 滑动轨迹滑动时间等数据
        trackData: {
          bgImageWidth: 0,
          bgImageHeight: 0,
          sliderImageWidth: 0,
          sliderImageHeight: 0,
          startSlidingTime: null,
          endSlidingTime: null,
          trackList: []
        },
        clickCount: 0
      };
    },
    created() {
      this.gen();
    },
    mounted() {
      const bgElements = document.getElementsByClassName(
        'captcha-click-bg-img-div'
      );
      this.trackData.bgImageWidth = bgElements.item(0).clientWidth;
      this.trackData.bgImageHeight = bgElements.item(0).clientHeight;
    },
    methods: {
      /**
       * 生成滑动验证图片
       */
      gen() {
        this.reset();
        const type = 'WORD_IMAGE_CLICK';
        getCaptchaCode(type).then((res) => {
          this.data = res.data.captcha;
          this.checkData.id = res.data.id;

          this.trackData.startSlidingTime = new Date();
          console.log('init', JSON.stringify(this.trackData));
        });
      },
      /**
       * 验证
       */
      check() {
        this.$emit('goLogin', this.checkData.id, this.trackData);
      },
      /**
       * 刷新
       */
      refresh() {
        this.gen();
      },
      /**
       * 重置
       */
      reset() {
        this.trackData.startSlidingTime = null;
        this.trackData.endSlidingTime = null;
        this.trackData.trackList = [];
        this.$nextTick(() => {
          this.$el
            .querySelectorAll('.captcha-click-bg-click-div span')
            .forEach((item) => {
              item.remove();
            });
        });
        this.clickCount = 0;
        this.movePercent = 0;
        this.currentCaptchaId = null;
        this.startY = 0;
        window.removeEventListener('mousemove', this.move);
      },
      close() {
        this.show = false;
      },

      /**
       * 移动
       * @param event
       */
      move(event) {
        if (event instanceof TouchEvent) {
          event = event.touches[0];
        }
        console.log(
          'x:',
          event.offsetX,
          'y:',
          event.offsetY,
          'time:',
          new Date().getTime() - this.trackData.startSlidingTime.getTime()
        );
      },
      handleClick(event) {
        this.clickCount++;
        if (this.clickCount === 1) {
          this.trackData.startSlidingTime = new Date();
          // move 轨迹
          window.addEventListener('mousemove', this.move);
        }
        const startTime = this.trackData.startSlidingTime;
        // const trackArr = this.trackData.trackList;
        this.trackData.trackList.push({
          x: event.offsetX,
          y: event.offsetY,
          type: 'click',
          t: new Date().getTime() - startTime.getTime()
        });
        const left = event.offsetX - 10;
        const top = event.offsetY - 10;
        const clickSpan = document.createElement('span');
        clickSpan.classList.add('captcha-click-span');
        clickSpan.style.left = `${left}px`;
        clickSpan.style.top = `${top}px`;
        clickSpan.innerHTML = `${this.clickCount}`;
        document
          .querySelector('.captcha-click-bg-click-div')
          .appendChild(clickSpan);
        if (this.clickCount === 4) {
          // 校验
          this.trackData.endSlidingTime = new Date();
          window.removeEventListener('mousemove', this.move);
          this.check();
        }
      }
    }
  };
</script>

<style>
  .captcha-click {
    position: relative;
    z-index: 999;
    box-sizing: border-box;
    width: 278px;
    height: 230px;
    padding: 9px;
    background-color: #fff;
    border-radius: 6px;
    box-shadow: 0 0 11px 0 #999;
  }

  .captcha-click .captcha-click-content {
    position: relative;
    width: 100%;
    height: 159px;
  }

  .captcha-click-bg-img-div {
    position: absolute;
    z-index: 0;
    width: 100%;
    height: 100%;
    transform: translate(0, 0);
  }

  .captcha-click-bg-click-div {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
    width: 100%;
    height: 100%;
  }

  .captcha-click-bg-img-div img {
    width: 100%;
  }

  .captcha-click .captcha-click-move {
    position: relative;
    width: 100%;
    height: 40px;
  }

  .captcha-click .captcha-click-bottom {
    /* height: 19px; */
    width: 100%;
    margin-top: 10px;
  }

  .captcha-click-refresh-btn {
    background: url('/img/bg/captcha.png') no-repeat;
  }

  .captcha-click-refresh-btn:hover {
    cursor: pointer;
  }

  .captcha-click-refresh-btn {
    display: inline-block;
  }

  .captcha-click {
    user-select: none;
  }

  .captcha-click-bottom .captcha-click-refresh-btn {
    position: absolute;
    top: 10px;
    right: 5px;
    z-index: 2;
    width: 20px;
    height: 20px;
    background-position: 0 81.38425%;
  }

  .captcha-click-tip-img {
    position: absolute;
    right: 30px;
    width: 130px;
  }

  .captcha-click-move-span {
    display: inline-block;
    height: 40px;
    font-size: 18px;
    line-height: 40px;
  }

  .captcha-click-span {
    position: absolute;
    top: 0;
    left: 0;
    width: 20px;
    height: 20px;
    color: #fff;
    line-height: 20px;
    text-align: center;
    background-color: #409eff;
    border: 2px solid #fff;
    border-radius: 50px;
  }
</style>
