//表单序列化
export const serialize = (data) => {
  let list = [];
  Object.keys(data).forEach((ele) => {
    list.push(`${ele}=${data[ele]}`);
  });
  return list.join('&');
};
// 获取文档类型
export function handleDocType(fileType) {
  let docType = '';
  let fileTypesDoc = [
    'doc',
    'docm',
    'docx',
    'dot',
    'dotm',
    'dotx',
    'epub',
    'fodt',
    'htm',
    'html',
    'mht',
    'odt',
    'ott',
    'pdf',
    'rtf',
    'txt',
    'djvu',
    'xps'
  ];
  let fileTypesCsv = [
    'csv',
    'fods',
    'ods',
    'ots',
    'xls',
    'xlsm',
    'xlsx',
    'xlt',
    'xltm',
    'xltx'
  ];
  let fileTypesPPt = [
    'fodp',
    'odp',
    'otp',
    'pot',
    'potm',
    'potx',
    'pps',
    'ppsm',
    'ppsx',
    'ppt',
    'pptm',
    'pptx'
  ];
  if (fileTypesDoc.includes(fileType)) {
    docType = 'word';
  }
  if (fileTypesCsv.includes(fileType)) {
    docType = 'cell';
  }
  if (fileTypesPPt.includes(fileType)) {
    docType = 'slide';
  }
  return docType;
}
