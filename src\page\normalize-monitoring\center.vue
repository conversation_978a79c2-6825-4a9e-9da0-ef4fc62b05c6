<template>
  <div class="center-monitoring-container">
    <div class="summarizing">
      <div class="summarizing-item">
        <div class="summarizing-icon"></div>
        <div class="summarizing-wrap">
          <span>监控系统个数</span>
          <span>149</span>
        </div>
      </div>
      <div class="summarizing-item">
        <div class="summarizing-icon"></div>
        <div class="summarizing-wrap">
          <span>监控单位个数</span>
          <span>149</span>
        </div>
      </div>
      <div class="summarizing-item">
        <div class="summarizing-icon"></div>
        <div class="summarizing-wrap">
          <span>监控指标个数</span>
          <span>149</span>
        </div>
      </div>
    </div>
    <div class="abnormal-units">
      <div class="abnormal-units-title">异常单位个数（上月）</div>
      <div class="abnormal-units-content">
        <div class="abnormal-units-item">
          <div class="units-title">登录率不达标</div>
          <div class="units-number">
            <div class="number-item">
              <el-popover placement="top" width="230" trigger="hover">
                <div>asdfasdfasjkdfkja实打实大萨达付</div>
                <div slot="reference">4</div>
              </el-popover>
            </div>
            <div class="number-item">&lt; 80%</div>
          </div>
          <AbnormalFetrEchart></AbnormalFetrEchart>
        </div>
        <div class="abnormal-units-item">
          <div class="units-title">活跃率不达标</div>
          <div class="units-number">
            <div class="number-item">
              <el-popover placement="top" width="230" trigger="hover">
                <div>asdfasdfasjkdfkja实打实大萨达付</div>
                <div slot="reference">4</div>
              </el-popover>
            </div>
            <div class="number-item">&lt; 60%</div>
          </div>
          <AbnormalFetrEchart></AbnormalFetrEchart>
        </div>
        <div class="abnormal-units-item">
          <div class="units-title">任务办结完成率低</div>
          <div class="units-number">
            <div class="number-item">
              <el-popover placement="top" width="230" trigger="hover">
                <div>asdfasdfasjkdfkja实打实大萨达付</div>
                <div slot="reference">4</div>
              </el-popover>
            </div>
            <div class="number-item">&lt; 90%</div>
          </div>
          <AbnormalFetrEchart></AbnormalFetrEchart>
        </div>
        <div class="abnormal-units-item">
          <div class="units-title">任务退回率高</div>
          <div class="units-number">
            <div class="number-item">
              <el-popover placement="top" width="230" trigger="hover">
                <div>asdfasdfasjkdfkja实打实大萨达付</div>
                <div slot="reference">4</div>
              </el-popover>
            </div>
            <div class="number-item">&gt; 20%</div>
          </div>
          <AbnormalFetrEchart></AbnormalFetrEchart>
        </div>
        <div class="abnormal-units-item">
          <div class="units-title">项目申报通过率低（季度）</div>
          <div class="units-number">
            <div class="number-item">
              <el-popover placement="top" width="230" trigger="hover">
                <div>asdfasdfasjkdfkja实打实大萨达付</div>
                <div slot="reference">4</div>
              </el-popover>
            </div>
            <div class="number-item">&lt; 60%</div>
          </div>
          <AbnormalFetrEchart></AbnormalFetrEchart>
        </div>
        <div class="abnormal-units-item">
          <div class="units-title">被督办次数高</div>
          <div class="units-number">
            <div class="number-item">
              <el-popover placement="top" width="230" trigger="hover">
                <div>asdfasdfasjkdfkja实打实大萨达付</div>
                <div slot="reference">4</div>
              </el-popover>
            </div>
            <div class="number-item">&gt; 2</div>
          </div>
          <AbnormalFetrEchart></AbnormalFetrEchart>
        </div>
      </div>
    </div>
    <div class="service-indicators">
      <div class="service-indicators-title">
        业务监控指标（上月） 排名：<el-select
          v-model="form.year"
          filterable
          placeholder="请选择"
          style="width: 100px"
          clearable
        >
          <el-option
            v-for="dict in yearList"
            :key="dict.key"
            :label="dict.name"
            :value="dict.key"
          ></el-option>
        </el-select>
      </div>
      <div class="service-indicators-content">
        <div class="service-indicators-item">
          <div class="content-title">任务办结完成率</div>
          <div class="chart-wrap">
            <BarCrosswiseEchart></BarCrosswiseEchart>
          </div>
        </div>
        <div class="service-indicators-item">
          <div class="content-title">任务退回率</div>
          <div class="chart-wrap">
            <BarCrosswiseEchart></BarCrosswiseEchart>
          </div>
        </div>
        <div class="service-indicators-item">
          <div class="content-title">项目申报通过率（季度）</div>
          <div class="chart-wrap">
            <BarCrosswiseEchart></BarCrosswiseEchart>
          </div>
        </div>
        <div class="service-indicators-item">
          <div class="content-title">被督办次数</div>
          <div class="chart-wrap">
            <BarCrosswiseEchart></BarCrosswiseEchart>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
  import { AbnormalFetrEchart, BarCrosswiseEchart } from './components';
  export default {
    components: { AbnormalFetrEchart, BarCrosswiseEchart },
    name: 'normalize-monitoring',
    data() {
      return {
        form: {},
        yearList: []
      };
    },
    methods: {}
  };
</script>

<style scoped lang="scss">
  @import './css/center';
</style>
