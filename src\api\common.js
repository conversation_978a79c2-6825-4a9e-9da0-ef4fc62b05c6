import request from '@/router/axios';
// 获取业务字典数据列表
export const getDictList = (params) => {
  return request({
    url: `/api/szyk-system/dict-biz/dictionary`,
    method: 'get',
    params
  });
};

/**
 * 文件流返回
 * @param url 接口地址
 */
export const exportBlob = (url) => {
  return request({
    url: url,
    method: 'get',
    responseType: 'blob'
  });
};
// 获取全部菜单
export const getAllMenu = (params) => {
  return request({
    url: `/api/szyk-system/menu/lazy-list/4biz`,
    method: 'get',
    params
  });
};
// 下载文件
export const downloadFile = (attachId) => {
  return request({
    url: `/api/szyk-resource/oss/endpoint/download/${attachId}`,
    responseType: 'blob',
    method: 'get'
  });
};

// 获取列排序数据
export const getColumnSortData = (params) => {
  return request({
    url: `/api/szyk-system/customColumn/listColumn`,
    method: 'get',
    params
  });
};

// 获取列排序数据
export const saveColumnSortData = (data) => {
  return request({
    url: `/api/szyk-system/customColumn/save`,
    method: 'post',
    data
  });
};
