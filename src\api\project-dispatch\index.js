import request from '@/router/axios';
// 获取调度周期列表
export const getCycleList = (params) => {
  return request({
    url: `/api/dispatch-period/detail/list`,
    method: 'get',
    params
  });
};
// 获取调度周期 -分页
export const getCyclePage = (data) => {
  return request({
    url: `/api/dispatch-period/detail/page`,
    method: 'post',
    data
  });
};
// 列表
export const getPage = (data) => {
  return request({
    url: '/api/project_dispatch/page',
    method: 'post',
    data
  });
};
// 获取项目清单
export const getProjectList = (data) => {
  return request({
    url: '/api/szyk-zbusiness/plbase/list/construction/year',
    method: 'post',
    data
  });
};
// 获取下发机构
export const getDeliveryList = (params) => {
  return request({
    url: `/api/szyk-system/dept/now/dept`,
    method: 'get',
    params
  });
};
// 保存
export const addFetch = (data) => {
  return request({
    url: '/api/project_dispatch/save',
    method: 'post',
    data
  });
};
// 提报
export const submitFetch = (data) => {
  return request({
    url: '/api/project_dispatch/report',
    method: 'post',
    data
  });
};
// 下发
export const issueFetch = (data) => {
  return request({
    url: '/api/project_dispatch/submit',
    method: 'post',
    data
  });
};
// 批量删除
export const batchDelete = (data) => {
  return request({
    url: '/api/project_dispatch/delete',
    method: 'post',
    data
  });
};
// 批量下发
export const batchDelivery = (data) => {
  return request({
    url: '/api/project_dispatch/issue',
    method: 'post',
    data
  });
};
// 归档
export const pigeonhole = (params) => {
  return request({
    url: `/api/project_dispatch/file`,
    method: 'get',
    params
  });
};
// 获取调度记录
export const getSchedulingRecord = (params) => {
  return request({
    url: `/api/project_dispatch/log/list`,
    method: 'get',
    params
  });
};
// 获取详情
export const getDetail = (params) => {
  return request({
    url: `/api/project_dispatch/detail`,
    method: 'get',
    params
  });
};
// 获取项目阶段
export const getProjectPhase = (data) => {
  return request({
    url: '/api/dict-phase/list',
    method: 'post',
    data
  });
};
// 退回
export const setBack = (data) => {
  return request({
    url: '/api/project_dispatch/back',
    method: 'post',
    data
  });
};
// 导出
export const exportExcel = (data) => {
  return request({
    url: '/api/project_dispatch/export',
    method: 'post',
    responseType: 'blob',
    data
  });
};
// 根据ID获取项目数据
export const getDispatchDetail = (data) => {
  return request({
    url: '/api/project_dispatch/project/detail',
    method: 'post',
    data
  });
};

// 导出
export const detailExport = (data) => {
  return request({
    url: '/api/project_dispatch/detailExport',
    method: 'post',
    responseType: 'blob',
    data
  });
};

// 判断项目名称是否已经存在
export const checkProjectNameExist = (params) => {
  return request({
    url: `/api/project_base/checkProjectNameExist`,
    method: 'get',
    params
  });
};

// 获取下发机构
export const getIssueDeptParam = (data) => {
  return request({
    url: '/api/project_dispatch/getIssueDeptParam',
    method: 'post',
    data
  });
};
// 根据下发机构获取调度周期
export const listByDeptId = (params) => {
  return request({
    url: `/api/dispatch-period/detail/listByDeptId`,
    method: 'get',
    params
  });
};
