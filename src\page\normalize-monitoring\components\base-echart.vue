<template>
  <div
    :id="chartId"
    :class="className"
    :style="{ height: height, width: width }"
  ></div>
</template>

<script>
  import reszie from '../mixins/resize';
  export default {
    mixins: [reszie],
    props: {
      className: {
        type: String,
        default: 'base-echart'
      },
      id: {
        type: String,
        default: ''
      },
      width: {
        type: String,
        default: '600px'
      },
      height: {
        type: String,
        default: '300px'
      },
      option: {
        type: Object,
        required: true
      }
    },
    data() {
      return {
        chart: null,
        chartId: this.id || Math.random().toString(36).substring(2, 8)
      };
    },
    watch: {
      option: {
        handler() {
          this.initChart();
        },
        deep: true
      }
    },
    mounted() {
      this.initChart();
    },
    beforeDestroy() {
      if (!this.chart) {
        return;
      }
      this.chart.dispose();
      this.chart = null;
    },
    methods: {
      initChart() {
        this.chart = window.echarts.init(document.getElementById(this.chartId));

        this.chart.setOption(this.option);
      }
    }
  };
</script>

<style scoped></style>
