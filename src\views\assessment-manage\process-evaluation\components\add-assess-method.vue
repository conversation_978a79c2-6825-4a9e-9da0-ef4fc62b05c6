<template>
  <Dialog
    v-if="visible"
    :visible="visible"
    :title="title"
    width="80%"
    @closed="hide"
  >
    <el-form
      label-suffix=":"
      ref="queryParams"
      label-width="80px"
      :model="queryParams"
      inline
    >
      <el-form-item label="剩余分值不为0" label-width="150" prop="isNotZero">
        <el-checkbox
          @change="queryText"
          v-model="queryParams.isNotZero"
        ></el-checkbox>
      </el-form-item>
      <el-form-item label="指标分类" prop="classifyName">
        <el-input
          class="mb10"
          clearable
          v-model.trim="queryParams.classifyName"
          @keyup.enter.native.stop.prevent="queryText"
          type="text"
          placeholder="请输入"
        />
      </el-form-item>
      <el-form-item label="评价指标" prop="evaluateTarget">
        <el-input
          class="mb10"
          clearable
          v-model.trim="queryParams.evaluateTarget"
          @keyup.enter.native.stop.prevent="queryText"
          type="text"
          placeholder="请输入"
        />
      </el-form-item>
      <el-form-item label="评分标准" prop="scoreMethod">
        <el-input
          class="mb10"
          clearable
          v-model.trim="queryParams.scoreMethod"
          @keyup.enter.native.stop.prevent="queryText"
          type="text"
          placeholder="请输入"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="queryText"
          >搜索</el-button
        >
        <el-button icon="el-icon-delete" @click="reset">清空</el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="tableData"
      v-loading="loading"
      border
      stripe
      height="500"
      style="width: 100%"
      class="mt-10"
      row-key="id"
      :span-method="objectSpanMethod"
      :header-cell-style="{ backgroundColor: '#fafafa' }"
      @select-all="handleSelectionAll"
      @select="handleSelectionChange"
      ref="multipleTable"
    >
      <el-table-column
        type="selection"
        :selectable="selectable"
        width="30"
        align="center"
      >
      </el-table-column>
      <!-- {{(page - 1) * pageSize + scope.$index + 1}} -->
      <!-- <el-table-column type="index" width="60" align="center" label="序列" /> -->
      <el-table-column align="center" width="70" label="指标分类">
        <template slot-scope="{ row }">
          {{ row.classifyName || '--' }}
        </template>
      </el-table-column>
      <el-table-column header-align="center" label="评价指标">
        <template slot-scope="{ row }">
          {{ row.evaluateTarget || '--' }}
        </template>
      </el-table-column>
      <el-table-column align="center" width="100" label="剩余分值">
        <template slot-scope="{ row }">
          {{ row.leftScore || '--' }}
        </template>
      </el-table-column>
      <el-table-column header-align="center" label="评分标准">
        <template slot-scope="{ row }">
          {{ row.scoreMethod || '--' }}
        </template>
      </el-table-column>
      <el-table-column align="center" width="100" label="考核方式">
        <template slot-scope="{ row }">
          {{ row.rwTypeName || '--' }}
        </template>
      </el-table-column>
    </el-table>
    <!-- <yk-pagination
      small
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.current"
      :limit.sync="queryParams.size"
      @pagination="getList"
    /> -->
    <div slot="footer">
      <el-button type="primary" plain @click="hide">取消</el-button>
      <el-button
        type="primary"
        :disabled="!this.selectedList.length"
        :loading="loading"
        @click="onSubmit"
        >确定
      </el-button>
    </div>
  </Dialog>
</template>
<script>
  import Dialog from '@/components/basic-dialog';
  // import { getAllMaterialList } from "@/api/distribution-apply";
  import { calculate } from '@/util/util';
  import {
    getAssessMethod,
    getSortAssessMethod
  } from '@/api/assess-manage/process-examination';
  import { trimAll, deepClone } from '@/util/util';
  const queryParams = {
    isNotZero: true,
    classifyName: undefined,
    evaluateTarget: undefined,
    scoreMethod: undefined,
    size: 500,
    current: 1
  };
  export default {
    components: { Dialog },
    props: {
      deptTree: { type: Array, require: () => [] },
      categoryTree: { type: Array, require: () => [] },
      formInfo: {
        type: Object,
        default: () => {}
      }
    },
    data() {
      return {
        visible: false,
        loading: false,
        originTableData: [],
        tableData: [],
        total: 0,
        title: '选择评分标准',
        rules: {
          dictValue: [
            { required: true, message: '请输入存放地点名称', trigger: 'blur' }
          ]
        },
        // 查询条件
        queryParams: {
          ...queryParams
        },
        // ------------
        selectedList: [],
        editSelectedList: [] // 已选中编辑过的列表
      };
    },
    methods: {
      // 合并
      objectSpanMethod({ row, rowIndex, columnIndex }) {
        let classifyColumn = [1];
        if (classifyColumn.includes(columnIndex)) {
          let colspan = this.getDynamicColspan('classifyNumber', 'classifyId');
          // console.log('colspan classifyNumber', colspan);
          if (colspan.includes(rowIndex)) {
            return {
              rowspan: row.classifyNumber,
              colspan: 1
            };
          } else {
            return {
              rowspan: 0,
              colspan: 0
            };
          }
        }
        let mergeColumn = [2, 3];
        if (mergeColumn.includes(columnIndex)) {
          let colspan = this.getDynamicColspan(
            'evaluateNumber',
            'schemeEvaluateId'
          );
          // console.log('colspan evaluateNumber', colspan);
          if (colspan.includes(rowIndex)) {
            return {
              rowspan: row.evaluateNumber,
              colspan: 1
            };
          } else {
            return {
              rowspan: 0,
              colspan: 0
            };
          }
        }
      },
      // 获取动态开始合并行号 = 排序、数量
      getDynamicColspan(flag, key) {
        let list = this.tableData;
        // debugger;
        let uniqueArr = this.uniqueFunc(list, flag, key);
        // let arr = list.map((item) => item.columnIndicator);
        return uniqueArr;
      },
      // 计算开始合并的初始行号
      uniqueFunc(arr, flag, key) {
        // 去重
        const res = new Map();
        for (const item of arr) {
          res.set(item[key], item);
        }
        let uniqueArr = [...res.values()];

        let classifyId = uniqueArr.map((item) => item[flag]);
        // debugger;
        // 计算开始合并的初始行号
        let additionArr = [];
        classifyId.forEach((e, i) => {
          if (i === 0) {
            additionArr.push(0, e);
          } else {
            additionArr.push(additionArr[i] + e);
          }
        });
        return additionArr;
      },
      // 剩余得分
      selectable(row) {
        if (['0.00', null].includes(row.leftScore)) {
          return false;
        } else {
          return true;
        }
      },
      // 检索
      async queryText() {
        Object.assign(this.queryParams, {
          pageNum: 1,
          pageSize: 500
        });
        await this.request();
      },
      async getAllData() {
        try {
          this.loading = true;
          // leftScoreNoZero   剩余分值不为0     0否 1是
          let { assessedOrgId, rwPeriodDetailId } = this.formInfo;
          const params = {
            schemeId: this.formInfo.schemeId,
            assessedOrgId,
            rwPeriodDetailId,
            leftScoreNoZero: 0,
            current: 1,
            size: 500
          };
          const {
            data: { data }
          } = await getAssessMethod(params);
          // const { records = [], total = 0 } = data || {};
          // this.total = total;
          let arr = data || [];
          this.originTableData = arr;
        } catch (e) {
          console.error(e);
        } finally {
          this.loading = false;
        }
      },
      // 请求列表
      async request() {
        this.loading = true;
        // leftScoreNoZero   剩余分值不为0     0否 1是
        let { classifyName, evaluateTarget, scoreMethod, isNotZero } =
          this.queryParams;
        let { assessedOrgId, rwPeriodDetailId } = this.formInfo;
        const params = {
          schemeId: this.formInfo.schemeId,
          classifyName: trimAll(classifyName),
          evaluateTarget: trimAll(evaluateTarget),
          scoreMethod: trimAll(scoreMethod),
          assessedOrgId,
          rwPeriodDetailId,
          leftScoreNoZero: isNotZero ? 1 : 0,
          current: this.queryParams.pageNum,
          size: this.queryParams.pageSize
        };
        try {
          const {
            data: { data }
          } = await getAssessMethod(params);
          // const { records = [], total = 0 } = data || {};
          // this.total = total;
          let arr = data || [];
          // this.originTableData = arr;
          // 过滤剩余分值是否为0
          // let filterArr = arr.filter((item) => {
          //   return isNotZero ? !(item.leftScore === '0.00') : true;
          // });
          this.tableData = arr;
          this.resetSelected();
        } catch (e) {
          console.error(e);
        } finally {
          this.loading = false;
        }
      },
      // 分页查询
      getList({ page, limit }) {
        Object.assign(this.queryParams, {
          pageNum: page,
          pageSize: limit
        });
        this.request();
      },
      // -----------------
      // 全选
      handleSelectionAll(selection) {
        if (selection && selection.length >= this.tableData.length) {
          let complexArr = [...this.selectedList];
          let selectedIp = complexArr.map(
            (item) => item.scoreMethodId || item.id
          );
          this.tableData.forEach((item) => {
            if (!selectedIp.includes(item.id)) {
              // this.selectedList.push(item);
              let obj = this.editSelectedList.find(
                (edit) => (edit.scoreMethodId || edit.id) === item.id
              );
              if (obj) {
                this.selectedList.push(obj);
              } else {
                this.selectedList.push(item);
              }
            }
          });
        } else {
          let cancelIpArr = this.tableData.map((item) => item.id);
          this.selectedList = this.selectedList.filter(
            (item) => !cancelIpArr.includes(item.scoreMethodId || item.id)
          );
        }
      },
      // select 单选
      handleSelectionChange(selection, row) {
        let selected = this.selectedList.some(
          (item) => (item.scoreMethodId || item.id) === row.id
        );
        if (!selected) {
          let obj = this.editSelectedList.find(
            (item) => (item.scoreMethodId || item.id) === row.id
          );
          if (obj) {
            this.selectedList.push(obj);
          } else {
            this.selectedList.push(row);
          }
        } else {
          let index = this.selectedList.findIndex(
            (item) => (item.scoreMethodId || item.id) === row.id
          );
          this.selectedList.splice(index, 1);
        }
      },
      // 归类 key 合并数 剩余分值+扣/加分。加分
      arrGroupClassify(arr, fn) {
        const obj = {};
        arr.forEach((item) => {
          const key = JSON.stringify(fn(item));
          obj[key] = obj[key] || [];
          obj[key].push(item);
        });
        for (const key in obj) {
          let rwScore = obj[key].reduce(
            (accumulator, currentValue) =>
              accumulator.concat(currentValue.oldRwScore || 0),
            [obj[key][0].leftScore]
          );
          obj[key] = calculate('add', ...rwScore);
        }

        return obj;
      },
      // 提交
      async onSubmit() {
        if (!this.selectedList.length)
          return this.$message.warning('请选择评分标准');
        try {
          this.loading = true;
          // 获取排序好的数据
          let selectedArr = deepClone(this.selectedList);
          let { assessedOrgId, rwPeriodDetailId } = this.formInfo;
          // 查到列表中的历史数据
          let ids = [...selectedArr, ...this.editSelectedList].map(
            (item) => item.scoreMethodId || item.id
          );
          let originArr = [];
          selectedArr = this.originTableData.forEach((item) => {
            if (ids.includes(item.id)) {
              originArr.push({ ...item, assessedOrgId, rwPeriodDetailId });
            }
          });
          let {
            data: { data }
          } = await getSortAssessMethod(originArr);
          let arr = data || [];
          arr.forEach((item) => {
            let obj = this.editSelectedList.find(
              (s) => (s.scoreMethodId || s.id) === item.id
            );
            item.oldRwScore = obj ? obj.oldRwScore : 0;
            item.rwScore = obj ? obj.rwScore : undefined;
          });
          let groupClassify = this.arrGroupClassify(
            arr,
            (item) => item.schemeEvaluateId
          );

          let updateArr = [];

          arr.forEach((item) => {
            let obj = this.editSelectedList.find(
              (s) => (s.scoreMethodId || s.id) === item.id
            );

            if (obj) {
              obj.classifyNumber = item.classifyNumber;
              obj.evaluateNumber = item.evaluateNumber;
              obj.rwScore = item.rwScore;
              obj.evaluateTargetId =
                item.evaluateTargetId || item.schemeEvaluateId;
              let evaluateTargetId = JSON.stringify(obj.evaluateTargetId);
              obj.currentAssessScore =
                groupClassify[evaluateTargetId] > Number(item.score)
                  ? Number(item.score)
                  : groupClassify[evaluateTargetId];
              updateArr.push(obj);
            } else {
              item.rwScore = item.rwScore;
              item.evaluateTargetId = item.schemeEvaluateId;
              let evaluateTargetId = JSON.stringify(item.evaluateTargetId);
              item.currentAssessScore =
                groupClassify[evaluateTargetId] > Number(item.score)
                  ? Number(item.score)
                  : groupClassify[evaluateTargetId];
              updateArr.push(item);
            }
          });

          this.$emit('save-success', updateArr);
          this.$message.success('保存成功');
          this.loading = false;
          this.hide();
        } catch (error) {
          this.loading = false;
          this.$message.warning(error.msg);
          this.hide();
          console.log(error);
        }
        // 组合 相同类型、评价指标
        // let updateArr = this.localUpdate();

        // this.$emit('save-success', updateArr);
        // this.$message.success('保存成功');
        // this.hide();
      },
      async show(arr) {
        this.visible = true;
        this.editSelectedList = deepClone(arr);
        // this.selectedList = arr;
        // console.log('this.editSelectedList', this.editSelectedList);
        await this.getAllData();
        await this.queryText();
        this.initSelected(); // 替换 为了排序接口排序
        this.resetSelected();
      },
      // 归类
      localUpdate() {
        let arr = [...this.selectedList];
        const classifyArr = this.arrGroup(
          arr,
          'classifyNumber',
          (item) => item.classifyId
        );
        const evaluateArr = this.arrGroup(
          classifyArr,
          'evaluateNumber',
          (item) => item.evaluateTargetId
        );
        return deepClone(evaluateArr);
      },
      // 归类 key 合并数
      arrGroup(arr, key, fn) {
        const obj = {};
        arr.forEach((item) => {
          const key = JSON.stringify(fn(item));
          obj[key] = obj[key] || [];
          obj[key].push(item);
        });
        // debugger; 原始
        let result = [];
        Object.keys(obj).forEach((k) => {
          obj[k].forEach((o) => {
            o[key] = obj[k].length;
          });
          result = [...result, ...obj[k]];
        });
        return result;
      },
      // 把外面修改过的数据替换成列表中的数据
      initSelected() {
        let oldids = this.editSelectedList.map(
          (item) => item.scoreMethodId || item.id
        );
        // 是否有新的必填字段
        let newArr = this.tableData.filter((item) => {
          if (oldids.includes(item.id)) {
            return true;
          }
          return false;
        });
        // 去重
        this.selectedList = newArr;
      },
      // 重置table选中显示
      resetSelected() {
        // 赋值
        let arr = [...this.selectedList];
        // 判断
        let ids = arr.map((item) => item.scoreMethodId || item.id);
        let selectedRows = this.tableData.filter((item) =>
          ids.includes(item.id)
        );
        if (selectedRows) {
          this.$nextTick(() => {
            selectedRows.forEach((row) => {
              this.$refs['multipleTable'].toggleRowSelection(row, true);
            });
          });
        } else {
          this.$nextTick(() => {
            this.$refs['multipleTable'].clearSelection();
          });
        }
      },
      resetPage() {
        this.queryParams = { ...queryParams };
      },
      reset() {
        this.resetPage();
        this.queryText();
      },
      hide() {
        this.resetPage();
        this.selectedList = [];
        this.editSelectedList = [];
        this.visible = false;
      },
      // 相同类型数据在一起 分类
      classifyData(data, key = 'zhuanye') {
        let c = [];
        let d = {};
        data.forEach((element) => {
          if (!d[element[key]]) {
            c.push(element);
            d[element[key]] = element;
          } else {
            // 插入
            let lastIndex = c.findLastIndex(
              (item) => item[key] == element[key]
            );
            c.splice(lastIndex + 1, 0, element);
            // 排序
            let arr = c.filter((item) => item[key] == element[key]);
            arr.sort((a, b) => a.time > b.time);
            // 替换
            let Index = c.findIndex((item) => item[key] == element[key]);
            c.splice(Index, arr.length, ...arr);
          }
        });
        return c;
      },
      // 计算相同类的 数量
      calculateColumn(arr, key = 'zhuanye') {
        arr.forEach((item) => {
          let column = arr.filter((fitem) => fitem[key] == item[key]);
          item.columnIndicator = column.length;
        });
        return arr;
      }
    }
  };
</script>
