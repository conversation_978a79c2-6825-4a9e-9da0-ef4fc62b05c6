<template>
  <div class="table_wrapper">
    <el-table
      ref="multipleTable"
      :data="source"
      stripe
      border
      v-if="showTable"
      row-key="id"
      v-loading="loading"
      @select="handleSelectionChange"
      @select-all="handleSelectionAll"
      style="width: 100%"
    >
      <el-table-column
        align="center"
        type="selection"
        :selectable="selectable"
        width="30"
      >
      </el-table-column>
      <!-- <el-table-column
        align="center"
        type="index"
        label="序号"
      ></el-table-column> -->
      <!-- <el-table-column
        :key="1"
        align="center"
        show-overflow-tooltip
        label="流程单号"
      >
        <template slot-scope="{ row }">
          {{ row.flowNo || '--' }}
        </template>
      </el-table-column>
      <el-table-column
        :key="2"
        align="center"
        show-overflow-tooltip
        label="流程名称"
      >
        <template slot-scope="{ row }">
          {{ row.flowName || '--' }}
        </template>
      </el-table-column> -->
      <el-table-column
        :key="3"
        align="center"
        show-overflow-tooltip
        label="项目名称"
      >
        <template slot-scope="{ row }">
          <el-button
            type="text"
            @click.stop="$emit('dispatch', 'view', row)"
            class="text_ellipsis_title"
          >
            {{ row.description || '--' }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column
        :key="33"
        align="center"
        show-overflow-tooltip
        width="75"
        label="申报年份"
      >
        <template slot-scope="{ row }">
          {{ row.year || '--' }}
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        :key="4"
        v-if="activeName === '0'"
        show-overflow-tooltip
        label="当前节点"
      >
        <template slot-scope="{ row }">
          {{ row.curStepName || '--' }}
        </template>
      </el-table-column>
      <el-table-column
        :key="5"
        v-if="activeName === '0'"
        align="center"
        show-overflow-tooltip
        label="下个节点"
      >
        <template slot-scope="{ row }">
          {{ row.nextStepName || '--' }}
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        width="100"
        :key="8"
        show-overflow-tooltip
        label="发起人部门"
      >
        <template slot-scope="{ row }">
          {{ row.applyUserDeptName || '--' }}
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        :key="7"
        width="75"
        show-overflow-tooltip
        label="发起人"
      >
        <template slot-scope="{ row }">
          {{ row.applyUserName || '--' }}
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        :key="6"
        width="150"
        show-overflow-tooltip
        label="发起时间"
        ><template slot-scope="{ row }">
          {{ row.applyTime || '--' }}
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        :key="9"
        show-overflow-tooltip
        label="当前审核状态"
      >
        <template slot-scope="{ row }">
          <el-tag v-if="row.curStatus == 0">{{ row.curStatusName }}</el-tag>
          <el-tag type="success" v-if="row.curStatus == 1">{{
            row.curStatusName
          }}</el-tag>
          <el-tag type="danger" v-if="row.curStatus == 2">{{
            row.curStatusName
          }}</el-tag>
          <el-tag type="info" v-if="row.curStatus == 3">{{
            row.curStatusName
          }}</el-tag>
          <el-tag type="warning" v-if="row.curStatus == 4">{{
            row.curStatusName
          }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        v-if="activeName === '1'"
        :key="10"
        show-overflow-tooltip
        label="审核操作"
        ><template slot-scope="{ row }">
          <el-tag type="success" v-if="row.approveStatus == 1">{{
            row.approveStatusName
          }}</el-tag>
          <el-tag type="warning" v-if="row.approveStatus == 2">{{
            row.approveStatusName
          }}</el-tag>
          <el-tag type="danger" v-if="row.approveStatus == 3">{{
            row.approveStatusName
          }}</el-tag>
          <template v-if="[null].includes(row.approveStatus)">--</template>
        </template>
      </el-table-column>
      <el-table-column :key="11" align="center" label="操作" width="40">
        <template slot-scope="{ row }">
          <el-dropdown trigger="click" @command="(v) => handleCommand(v, row)">
            <i class="el-icon-more"></i>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                v-if="permission['approval-check-view']"
                command="view"
                >查看</el-dropdown-item
              >
              <el-dropdown-item
                v-if="activeName === '2' && permission['approval-check-recall']"
                command="recall"
                :disabled="!row.cancelFlag"
                >撤回</el-dropdown-item
              >
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
  // import { cloneDeep } from 'lodash';
  import { delLabel } from '@/api/project-submit';
  import { mapGetters } from 'vuex';

  export default {
    name: 'ProjectLibraryTableInfo',
    props: {
      loading: {
        type: Boolean,
        default: false
      },
      activeName: {
        type: String,
        default: ''
      },
      source: {
        type: Array,
        default() {
          return [];
        }
      }
    },
    // watch: {
    //   source: {
    //     handler(arr) {
    //       this.list = cloneDeep(arr);
    //     },
    //     deep: true
    //   }
    // },
    data() {
      return {
        list: [],
        visited: false,
        showTable: true
      };
    },
    methods: {
      handleCommand(command, row) {
        switch (command) {
          case 'view':
            this.$emit('dispatch', 'view', row);
            break;
          case 'recall':
            this.$emit('dispatch', 'commit', row);
            break;
          default:
            break;
        }
      },
      async handleClose(tag) {
        let params = {
          id: tag.id,
          labelKey: tag.dictKey
        };
        await delLabel(params);
        this.$message({
          type: 'success',
          message: '删除成功!'
        });
        this.$emit('dispatch', 'refresh');
      },
      // 可选的 退回 3 待提交 0 什么情况都可以打标签
      selectable() {
        if (this.activeName === '0') {
          return true;
        } else {
          return false;
        }
      },
      commit(row) {
        this.$emit('dispatch', 'commit', row);
      },
      // 项目删除
      del(row) {
        this.$emit('dispatch', 'delete', row);
      },
      // 多选框
      // no-unused-vars
      handleSelectionChange(selection, row) {
        console.log('selection', row);
        this.$emit('dispatch', 'selection', row);
      },
      // 全选
      handleSelectionAll(selection) {
        console.log('selectionAll', selection);
        this.$emit('dispatch', 'selectionAll', selection);
      }
    },
    computed: {
      ...mapGetters(['permission']),
      menuName() {
        let bool = this.$route.name.includes('schedule');
        return bool ? 'schedule' : 'check';
      },
      tableHeight() {
        let height = document.documentElement.clientHeight;
        let calcHeight = null;
        if (height > 800) {
          if (this.activeName === '0')
            calcHeight = (height - (65 + 40 + 10 + 10 + 60 + 190)) / 1.1;
          else calcHeight = (height - (65 + 40 + 10 + 10 + 60 + 190)) / 1;
        } else {
          if (this.activeName === '0')
            calcHeight = (height - (65 + 40 + 10 + 10 + 60 + 190)) / 1.61;
          else calcHeight = (height - (65 + 40 + 10 + 10 + 60 + 190)) / 1.21;
        }
        return `${calcHeight}px`;
      }
    }
  };
</script>

<style lang="scss" scoped>
  .project-label {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;

    // justify-content: space-between;
  }

  /deep/ .el-table .cell {
    font-size: 14px;
  }

  .show-center {
    justify-content: center;
  }
</style>
