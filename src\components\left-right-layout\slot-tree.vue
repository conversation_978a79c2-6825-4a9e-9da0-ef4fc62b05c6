<template>
  <div class="slot-tree" v-loading="lazyLoading">
    <el-input
      placeholder="请输入"
      style="width: 99%"
      v-model="filterText"
      @keyup.enter.native.stop.prevent="searchDept"
      clearable
      suffix-icon="el-icon-search"
    ></el-input>
    <div class="my-tree tree-container" :style="{ height: scrollHeight }">
      <el-tree
        ref="avueTree"
        v-if="loadingTree"
        :props="ifShowSearchDept ? searchProps : props"
        :default-expand-all="ifShowSearchDept"
        :expand-on-click-node="false"
        highlight-current
        :data="treeData"
        :load="treeLoad"
        node-key="id"
        :default-expanded-keys="expandKeys"
        :current-node-key="form.orgId"
        :lazy="!ifShowSearchDept"
        @node-click="treeChange"
      >
        <span
          class="el-tree-node__label"
          slot-scope="{ node }"
          @mouseenter="visibilityChange($event)"
        >
          <el-tooltip
            :disabled="!isShowTooltip"
            effect="dark"
            :content="node.label"
            placement="top-start"
          >
            <span class="label">{{ node.label }}</span>
          </el-tooltip>
        </span>
      </el-tree>
    </div>
  </div>
</template>

<script>
  import { getDeptLazyTree, deptTitleTree } from '@/api/system/dept';

  import { mapGetters } from 'vuex';
  export default {
    props: {
      userList: {
        type: Array,
        default: () => []
      },
      form: {
        type: Object,
        default: () => {}
      }
    },
    data() {
      return {
        filterText: '',
        treeList: [],
        editTreeList: [],
        defaultProps: {
          children: 'children',
          label: 'title',
          isLeaf: 'isLeaf'
        },
        loading: false,
        visible: false,
        loadingTree: true,
        dialogTitle: '',
        iconSize: 'mini',
        title: undefined,
        delList: [],
        node: null,
        resolveFunc: null,
        lazyLoading: false,
        defaultExpandList: [],
        isShowTooltip: false,
        expandKeys: ['1673217029692919810'],
        treeData: [],
        ifShowSearchDept: false,
        maps: new Map(),
        treeDeptId: '',
        searchProps: {
          label: 'deptName',
          children: 'children',
          isLeaf: (data) => !data.hasChildren
        },
        props: {
          label: 'title',
          children: 'children',
          isLeaf: (data) => !data.hasChildren
        }
      };
    },
    // watch: {
    //   filterText(val) {
    //     this.$refs.SlotMenuList.filter(val);
    //   }
    // },
    computed: {
      ...mapGetters(['permission']),
      scrollHeight() {
        let height = document.documentElement.clientHeight;
        let calcHeight = null;
        if (height > 700) {
          calcHeight = height - (65 + 40 + 10 + 10 + 150); // 顶部菜单、tab、margin、底部空隙
        } else {
          calcHeight = height - (65 + 40 + 10 + 10 + 130); // 顶部菜单、tab、margin、底部空隙
        }

        return `${calcHeight}px`;
      }
    },
    methods: {
      async searchDept() {
        this.searchLoading = true;
        this.refreshTree();
        this.treeData = [];
        if (this.filterText === '' || this.filterText === undefined) {
          this.ifShowSearchDept = false;
          this.updateTable();
        } else {
          this.ifShowSearchDept = true;
          this.lazyLoading = true;
          let params = { title: this.filterText };
          let res = await deptTitleTree(params);
          // this.searchDeptList = res.data.data;
          this.treeData = res.data.data || [];
          this.lazyLoading = false;
        }
        this.searchLoading = false;
      },
      //子节点刷新
      updateTable() {
        // 在删除或者添加操作成功之后，调用此函数
        let arr = [];
        this.lazyLoading = true;
        this.maps.forEach(async (item, key) => {
          key && arr.push(key);
          const { treeNode, resolve } = this.maps.get(key);
          await this.treeLoad(treeNode, resolve);
        });
        this.expandKeys = arr.length ? arr : ['1673217029692919810'];
        this.lazyLoading = false;
      },
      async treeLoad(treeNode, resolve) {
        const parentId = treeNode.level === 0 ? null : treeNode.data.id;
        this.maps.set(parentId, { treeNode, resolve });
        // this.searchLoading = true;
        this.lazyLoading = true;
        try {
          await getDeptLazyTree(parentId).then((res) => {
            let list = res.data.data || [];
            if (treeNode.level === 0) {
              this.treeData = list;
              // if (!this.ifShowSearchDept) {
              // let obj = list[0] || {};
              // this.expandKeys = [obj.id];
              // obj.id && this.treeChange(obj);
              // }
            } else {
              resolve(list);
            }

            this.$nextTick(() => {
              this.expandKeys = [this.treeData[0].id];
              this.$refs.avueTree.setCurrentKey(this.form.orgId);
            });
            // this.searchLoading = false;
            // setTimeout(() => {
            //   this.$refs.scrollbar.update();
            // }, 500);
          });
        } finally {
          this.lazyLoading = false;
        }
      },
      refreshTree() {
        //<el-tree>组件使用v-if重新加载
        this.loadingTree = false;
        this.$nextTick(() => {
          this.loadingTree = true;
        });
      },
      visibilityChange(event) {
        const ev = event.target;
        const ev_weight = ev.children[0].offsetWidth;
        const content_weight = ev.clientWidth;
        if (ev_weight > content_weight) {
          this.isShowTooltip = true;
        } else {
          this.isShowTooltip = false;
        }
      },
      orgclose(flag) {
        this.$refs[flag].dropDownVisible = false;
      },
      filterNode(value, data) {
        if (!value) return true;
        return data.classifyName.indexOf(value) !== -1;
      },
      treeChange(val) {
        this.form.orgName = val.ancestorName;
        this.form.orgId = val.id;
        this.$emit('getData', { queryOrg: val.id });
      },
      // 新增子节点
      NodeAdd(data, flag) {
        this.$refs.operate.show(data, flag);
      },
      // 设置
      NodeOptions(data) {
        this.$refs.content.show(data);
      },
      // 编辑
      async NodeEdit(data, flag) {
        this.$refs.operate.show(data, flag);
      },

      async operateRefresh(obj, newForm, isEdit) {
        // await this.lazySearch();
        if (isEdit) {
          this.$refs.SlotMenuList.$data.store.currentNode.data.title =
            newForm.classifyName;
        } else {
          this.$refs.SlotMenuList.$data.store.currentNode.isLeaf = false;
        }
        this.setTree(obj);
      },
      setTree(obj) {
        this.$nextTick(() => {
          this.$refs.avueTree.setCurrentKey(obj.id);
        });
      },
      handleClose() {
        this.visible = false;
      }
    },
    mounted() {
      // this.getCategoryTree(1);
      // this.$nextTick(() => {
      // 调用loadData方法展开第二层
      //   let nodedata = this.node.childNodes[0];
      //   debugger;
      //   nodedata.expanded = true;
      //   nodedata.loadData();
      // });
    }
  };
</script>

<style lang="scss" scoped>
  .el-select .el-input,
  .el-input {
    width: 100%;
  }

  .el-form-item .el-input-group {
    vertical-align: middle !important;
  }

  .slot-tree .slot-t-top {
    margin-bottom: 10px;
  }

  .tree-container {
    // float: left;
    // border: solid 1px #e4e7ed;
    width: 230px;
    height: 626px;
    overflow: auto;

    .el-scrollbar__wrap {
      overflow-x: hidden;
    }
  }

  .el-tree {
    background: #fff !important;
  }

  .el-button--mini.is-circle {
    padding: 3px;
  }

  .el-button + .el-button {
    margin-left: 3px;
  }

  .my-tree {
    width: 100%;
    margin-top: 15px;

    .el-tree-node__content {
      overflow: hidden;
    }

    .custom-tree-node {
      display: flex;
      flex-grow: 1;
      flex-shrink: 1;
      align-items: center;
      justify-content: space-between;
      overflow: hidden;
      font-size: 14px;

      .label {
        display: inline-block;
        overflow: hidden !important;

        // width: 100px;
        // font-weight: 600;
        white-space: nowrap;
        text-overflow: ellipsis !important;
      }

      .button {
        display: none;
        flex-grow: 0;
        flex-shrink: 0;
        margin-left: 10px;
      }
    }

    .custom-tree-node:hover .button {
      display: inline-block;
    }
  }
</style>
