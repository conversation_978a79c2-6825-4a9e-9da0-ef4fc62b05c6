const ieVersion = Number(document.documentMode);

const getStyle =
  ieVersion < 9
    ? function (element, styleName) {
        if (!element || !styleName) return null;
        if (styleName === 'float') {
          styleName = 'styleFloat';
        }
        try {
          switch (styleName) {
            case 'opacity':
              try {
                return element.filters.item('alpha').opacity / 100;
              } catch (e) {
                return 1.0;
              }
            default:
              return element.style[styleName] || element.currentStyle
                ? element.currentStyle[styleName]
                : null;
          }
        } catch (e) {
          return element.style[styleName];
        }
      }
    : function (element, styleName) {
        if (!element || !styleName) return null;
        if (styleName === 'float') {
          styleName = 'cssFloat';
        }
        try {
          var computed = document.defaultView.getComputedStyle(element, '');
          return element.style[styleName] || computed
            ? computed[styleName]
            : null;
        } catch (e) {
          return element.style[styleName];
        }
      };

export const tableCellEnter = (row, column, cell, event) => {
  const cellChild = event.target.querySelector('.cell');
  const range = document.createRange();
  range.setStart(cellChild, 0);
  range.setEnd(cellChild, cellChild.childNodes.length);
  // 判断水平方向是否溢出
  // const rangeWidth = range.getBoundingClientRect().width;
  // const horizonalPadding =
  //   (parseInt(getStyle(cellChild, 'paddingLeft'), 10) || 0) +
  //   (parseInt(getStyle(cellChild, 'paddingRight'), 10) || 0);
  // const widthFlag =
  //   rangeWidth + horizonalPadding > cellChild.offsetWidth ||
  //   cellChild.scrollWidth > cellChild.offsetWidth;
  // 判断垂直方向是否溢出
  const rangeHeight = range.getBoundingClientRect().height;
  const verticalPadding =
    (parseInt(getStyle(cellChild, 'paddingTop'), 10) || 0) +
    (parseInt(getStyle(cellChild, 'paddingBottom'), 10) || 0);
  const heightFlag =
    rangeHeight + verticalPadding > cellChild.offsetHeight ||
    cellChild.scrollHeight > cellChild.offsetHeight;
  if (
    // 如果里面的内容是html内容
    !cellChild.innerHTML.includes('</') &&
    !cellChild.getAttribute('title') &&
    column.label &&
    !column.label.includes('操作') &&
    heightFlag
  ) {
    cellChild.setAttribute('title', cellChild.innerHTML);
  }
};
