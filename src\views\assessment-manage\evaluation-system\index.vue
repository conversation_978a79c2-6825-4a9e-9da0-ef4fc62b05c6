<template>
  <basic-container autoHeight>
    <search @search="query" />
    <el-row>
      <el-col :span="24">
        <div class="tool-box">
          <el-button
            v-if="permission['check-system-add']"
            type="primary"
            icon="el-icon-plus"
            @click="handleAdd"
            >新增</el-button
          >
          <el-button
            type="danger"
            plain
            v-if="permission['check-system-delete']"
            icon="el-icon-delete"
            :disabled="!rows.length"
            @click="handleDel()"
            >批量删除
          </el-button>
        </div>
      </el-col>
    </el-row>
    <table-info
      :source="tableData"
      ref="tableInfo"
      :loading="loading"
      @dispatch="dispatch"
    />
    <yk-pagination
      small
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.current"
      :limit.sync="queryParams.size"
      @pagination="getList"
    />
    <!-- 批量操作结果 -->
    <ErrorDialog
      :dialogTitle="dialogTitle"
      :formVisible="formVisible"
      :list="batchList"
      :isInitiate="isInitiate"
      :loading="errorLoading"
      labelName="体系名称"
      @refresh="request"
      @close="handleDialogClose"
    />
    <InitiateEvaluation
      ref="initiate"
      @save-success="saveSuccess"
    ></InitiateEvaluation>
  </basic-container>
</template>

<script>
  import {
    Search,
    TableInfo,
    ErrorDialog,
    InitiateEvaluation
  } from './components';
  import {
    getPage,
    batchDelete,
    majorStatus,
    initiate
  } from '@/api/assess-manage';
  import { mapGetters } from 'vuex';
  import { trimAll } from '@/util/util';
  import 'nprogress/nprogress.css';
  import { getStore } from '@/util/store';

  export default {
    name: 'Project-progress',
    components: {
      Search,
      TableInfo,
      ErrorDialog,
      InitiateEvaluation
    },
    data() {
      return {
        // 查询条件
        queryParams: {
          size: 10,
          current: 1
        },
        // 表格加载中
        loading: false,
        errorLoading: false,
        // 列表条目总数量
        total: 0,
        // 列表数据
        tableData: [],
        // 是否显示
        formVisible: false,
        addVisible: false,
        // dialog标题
        dialogTitle: '',
        // 选中项
        rows: [],
        // 批量提报结果
        batchList: [],
        isInitiate: false
      };
    },
    computed: {
      ...mapGetters(['permission']),
      menuName() {
        let bool = this.$route.name.includes('submit');
        return bool ? 'schedule' : 'check';
      },
      // (1, "单位"),(2, "部门"),(3, "虚拟单位"),(4, "虚拟部门")
      deptCategory() {
        let org = getStore({ name: 'current-organization' });
        return org ? org['deptCategory'] : '';
      }
    },
    activated() {
      this.request();
    },
    methods: {
      async saveSuccess(form) {
        try {
          this.formVisible = true;
          this.isInitiate = true;
          this.errorLoading = true;
          this.batchList = [];
          const {
            data: { data }
          } = await initiate(form);
          this.batchList = data || [];
          this.dialogTitle = '提示';
          this.errorLoading = false;
        } catch (error) {
          console.log(error);
        } finally {
          this.errorLoading = false;
        }
      },
      // 批量删除、删除
      handleDel(row) {
        this.$confirm('确定要删除吗?', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(async () => {
            this.batchDel(row);
          })
          .catch(() => {});
      },
      async batchDel(row) {
        let complexArr = row ? [row] : [...this.rows];
        const filtered = complexArr.filter(Boolean).map((item) => item.id);
        try {
          this.loading = true;
          let params = {
            idList: filtered
          };
          const {
            data: { data }
          } = await batchDelete(params);
          this.batchList = data || [];
          this.setPage(row);
          // this.$message.success('提报成功');
          // this.request();
        } catch (e) {
          console.log(e);
        } finally {
          this.loading = false;
        }
        this.dialogTitle = row ? '删除结果' : '批量删除结果';
        this.formVisible = true;
      },
      setPage(row) {
        let resultList = this.batchList;
        let bool = resultList.every((item) => item.result);
        if (!bool) return;
        let length = this.tableData.length;

        if (row) {
          if (length === 1) {
            this.queryParams.current = 1;
          }
        } else {
          let rowsLength = this.rows.length;
          if (rowsLength >= length) {
            console.log('rowsLength', rowsLength);
            this.queryParams.current = 1;
          }
        }
      },
      // 查询
      query(params) {
        if (params.isResetQuery) {
          this.rows = [];
        }
        Object.assign(
          this.queryParams,
          {
            current: 1,
            size: 10
          },
          { ...params, isResetQuery: undefined }
        );
        for (const key in this.queryParams) {
          this.queryParams[key] = trimAll(this.queryParams[key]);
        }
        this.request();
      },
      // 分页查询
      getList({ page, limit }) {
        Object.assign(this.queryParams, {
          current: page,
          size: limit
        });
        this.request();
      },
      // 请求列表数据
      async request() {
        try {
          this.loading = true;
          const { data } = await getPage({ ...this.queryParams });
          const { total = 0, records = [] } = data.data ? data.data : {};
          this.total = total;
          this.tableData = records;
          this.setSelected();
        } catch (e) {
          console.error(e);
        } finally {
          this.loading = false;
        }
      },
      setSelected() {
        let ids = this.rows.map((item) => item.id);
        let selectedRows = this.tableData.filter((item) =>
          ids.includes(item.id)
        );
        if (selectedRows.length) {
          this.$nextTick(() => {
            selectedRows.forEach((row) => {
              this.$refs['tableInfo'].$refs['multipleTable'].toggleRowSelection(
                row,
                true
              );
            });
          });
        } else {
          this.$nextTick(() => {
            this.$refs['tableInfo'].$refs['multipleTable'].clearSelection();
          });
        }
      },
      // 列表操作
      dispatch(type, data) {
        switch (type) {
          case 'delete':
            return this.handleDel(data);
          case 'commit':
            return this.handleInitiate(data);
          case 'edit':
            return this.handleEdit(data);
          case 'view':
            return this.handleView(data);
          case 'onEnable':
            return this.handleEnable(data);
          case 'refresh':
            return this.request();
          case 'selection':
            return this.handleSelect(data);
          case 'selectionAll':
            return this.handleSelectAll(data);
          default:
            return false;
        }
      },
      // 发起专项考核
      handleInitiate(item) {
        if (![1, 3].includes(this.deptCategory)) {
          return this.$message.warning('需以单位登录，否则不能发起考核。');
        }
        this.$refs.initiate.show({ ...item, assessedOrgId: [] });
      },
      handleEnable(item) {
        this.$confirm(
          item.schemeStatus === '0'
            ? '确定 停用 此体系，是否继续？'
            : '确定 启用 此体系，是否继续？',
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        ).then(async () => {
          this.setEnable(item);
        });
      },
      async setEnable(item) {
        try {
          let params = {
            id: item.id,
            schemeStatus: item.schemeStatus === '0' ? '1' : '0'
          };
          await majorStatus(params);
          this.$message.success(
            item.schemeStatus === '0' ? '停用成功！' : '启用成功！'
          );
          this.request();
        } catch (e) {
          console.log(e);
        }
      },
      handleSelect(row) {
        let index = this.rows.findIndex((item) => item.id === row.id);
        if (index >= 0) {
          this.rows.splice(index, 1);
        } else {
          this.rows.push(row);
        }
      },
      uniqueFunc(arr, uniId) {
        let hash = {};
        return arr.reduce((accum, item) => {
          hash[item[uniId]]
            ? ''
            : (hash[item[uniId]] = true && accum.push(item));
          return accum;
        }, []);
      },
      handleSelectAll(rows) {
        if (!this.rows.length && rows.length)
          return (this.rows = [...this.rows, ...rows]);
        if (rows.length) {
          this.rows = this.uniqueFunc([...this.rows, ...rows], 'id');
        } else {
          let clearSelection = this.tableData.map((item) => item.id);
          this.rows = this.rows.filter(
            (item) => !clearSelection.includes(item.id)
          );
        }
      },
      handleAdd() {
        if (![1, 3].includes(this.deptCategory)) {
          return this.$message.warning('需以单位登录，否则不能新增考核体系。');
        }
        this.$router.push(`/assessment-manage/evaluation-system/add`);
      },
      handleView(row) {
        this.$router.push(
          `/assessment-manage/evaluation-system/view/${row.id}`
        );
      },
      handleEdit(row) {
        this.$router.push(
          `/assessment-manage/evaluation-system/edit/${row.id}`
        );
      },
      handleDialogClose() {
        this.rows = [];
        this.setSelected();
        this.formVisible = false;
        this.addVisible = false;
      }
    }
  };
</script>

<style lang="scss" scoped>
  .el-pagination {
    margin-top: 20px;
  }

  /deep/ .el-table .el-table__header .cell {
    color: #909399 !important;
  }

  /deep/ .el-button--small span {
    font-size: 13px !important;
  }

  /deep/ .el-button--mini,
  .el-button--small {
    font-size: 13px !important;
  }

  /deep/ .el-table .cell {
    font-size: 14px;
  }
</style>
