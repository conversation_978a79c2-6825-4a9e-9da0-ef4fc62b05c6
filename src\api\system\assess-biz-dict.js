import request from '@/router/axios';
// ------ 指标分类 -------
// 列表
export const getPhaseList = (data) => {
  return request({
    url: '/api/rw-target-classify/page',
    method: 'post',
    data
  });
};
// 新增、编辑
export const phaseSave = (data) => {
  return request({
    url: '/api/rw-target-classify/save',
    method: 'post',
    data
  });
};
// 启用、停用
export const phaseStatus = (data) => {
  return request({
    url: '/api/rw-target-classify/status',
    method: 'post',
    data
  });
};
// 是否加分项
export const phaseType = (data) => {
  return request({
    url: '/api/rw-target-classify/score',
    method: 'post',
    data
  });
};
// 删除
export const phaseDel = (data) => {
  return request({
    url: '/api/rw-target-classify/delete',
    method: 'post',
    data
  });
};
// 详情
export const phaseDetail = (params) => {
  return request({
    url: '/api/rw-target-classify/detail',
    method: 'get',
    params
  });
};
