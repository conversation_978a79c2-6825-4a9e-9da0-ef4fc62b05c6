<template>
  <span class="wrapper">
    <el-button type="primary" icon="el-icon-postcard" @click="visible = true">{{
      btnTitle
    }}</el-button>
    <!--  弹框  -->
    <el-dialog
      :title="modelTitle"
      :visible.sync="visible"
      :close-on-click-modal="false"
      :append-to-body="appendToBody"
      width="70%"
      custom-class="org_dialog"
      @close="cancel"
    >
      <div style="height: 500px" v-if="visible">
        <!--  检索  -->
        <div style="margin: 10px">
          <strong>快速检索</strong>
          <div class="input_wrapper">
            <el-input
              v-model.trim="queryDeptName"
              @keyup.enter.native.stop.prevent="textInput"
              type="text"
              placeholder="请输入部门名称"
            />
          </div>
          <el-button
            plain
            type="primary"
            icon="el-icon-search"
            @click="textInput"
            >查 询</el-button
          >
          <el-button type="primary" icon="el-icon-refresh-right" @click="reset"
            >重 置</el-button
          >
        </div>
        <el-row :gutter="15" class="content-wrapper">
          <tree-wrapper>
            <template v-slot:left>
              <!--  组织架构  -->
              <tree-org-dept
                v-if="resetStatus"
                :single="single"
                :node-list="nodeList"
                :expanded-arr="expandedArr"
                @setNodeSingle="setNodeSingle"
                @setNodePush="setNodeMorePush"
                @setNodeSplice="setNodeMoreSplice"
              />
              <tree-org-dept-sync
                v-else
                :single="single"
                :tree="treeAll"
                :query="queryDeptName"
                :expanded-arr="expandedArr"
                :expanded-query-arr="expandedQueryArr"
                :status="queryStatus"
                :loading="loading"
                :node-list="nodeList"
                @setNodeSingle="setNodeSingle"
                @setNodePush="setNodeMorePush"
                @setNodeSplice="setNodeMoreSplice"
              />
            </template>
            <template v-slot:mid>
              <!--  已选择部门  -->
              <select-dept :list="nodeList" @removeEmit="removeFn" />
            </template>
          </tree-wrapper>
        </el-row>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="ok">确 定</el-button>
      </span>
    </el-dialog>
  </span>
</template>

<script>
  import TreeOrgDept from './tree-org-dept';
  import TreeOrgDeptSync from './tree-org-dept-sync';
  import SelectDept from './select';
  import { allLowUnit, getDeptIdList } from '@/api/system/dept';
  // import { getCurrentChild } from '@/api/assess-manage';
  import { cloneDeep, concat, isEqual, throttle, uniqWith } from 'lodash';
  import TreeWrapper from './tree-wrapper';
  import website from '@/config/website';
  import { getStore } from '@/util/store';
  import { mapGetters } from 'vuex';
  export default {
    name: 'Department',
    components: {
      SelectDept,
      TreeOrgDept,
      TreeOrgDeptSync,
      TreeWrapper
    },
    model: {
      prop: 'sections',
      event: 'change'
    },
    props: {
      // 按钮标题
      btnTitle: {
        type: String,
        default: '部门选择'
      },
      modelTitle: {
        type: String,
        default: '部门选择'
      },
      // 已选部门数组
      sections: {
        type: Array,
        default: function () {
          return [];
        }
      },
      // 单选/多选 默认多选
      single: {
        type: Boolean,
        default: false
      },
      appendToBody: {
        type: Boolean,
        default: true
      }
    },
    watch: {
      async visible(val) {
        if (val) {
          await this.initDept();
          const arr = this.sections;
          const ids = arr.map((item) => item.id);
          // 回显展开
          if (ids.length) {
            const res = await getDeptIdList(ids.toString(), website.tenantName);
            // 设置返回展开ids
            this.expandedArr = res.data.data;
          } else {
            this.expandedArr = [];
          }
          // 单选处理
          if (this.single) {
            if (arr.length > 1) {
              arr.length = 1;
            }
          }
          this.nodeList = cloneDeep(arr);
        } else {
          this.reset();
        }
      }
    },
    data() {
      return {
        visible: false,
        queryDeptName: '',
        loading: false,
        nodeList: [],
        tree: [],
        treeAll: {},
        resetStatus: true,
        queryStatus: false,
        expandedArr: [],
        expandedQueryArr: []
      };
    },
    computed: {
      ...mapGetters(['userInfo']),
      unitId() {
        let org = getStore({ name: 'current-organization' });
        return org ? org['id'] : '';
      }
    },
    methods: {
      textInput: throttle(
        function () {
          if (this.queryDeptName.length) {
            this.loading = true;
            this.resetStatus = false;
            this.queryText();
            // 数据过大时，渲染慢
            setTimeout(() => {
              this.loading = false;
            }, 3 * 1000);
          }
        },
        2000,
        {
          trailing: false
        }
      ),
      // 取消按钮
      cancel() {
        this.visible = false;
        this.queryDeptName = '';
        this.nodeList = [];
      },
      // 确定按钮
      ok() {
        this.$emit('change', this.nodeList);
        this.dispatch('ElFormItem', 'el.form.change', this.nodeList);
        this.cancel();
      },
      // 删除部门
      removeFn(index) {
        this.nodeList.splice(index, 1);
      },
      // 所有组织
      async initDept() {
        try {
          let params = {
            tenantId: '000000',
            parentId: this.unitId,
            unitName: undefined
          };
          // const res = await getCurrentChild(params);
          const res = await allLowUnit(params);
          const data = res.data.data;
          const treeData = cloneDeep(data);
          treeData.push(website.deptRootSync);

          // treeData.push(website.deptRootSync);
          const treeMap = {};
          for (let d of treeData) {
            let parentId = d['parentId'];
            if (!treeMap[parentId]) {
              treeMap[parentId] = [d];
            } else {
              treeMap[parentId].push(d);
            }
          }
          this.treeAll = treeMap;
        } catch (e) {
          console.error(e);
        }
      },
      // 查询 API
      async queryText() {
        this.queryStatus = false;
        try {
          // const res = await getDeptNameList(
          //   this.queryDeptName,
          //   website.tenantName
          // );
          // let params = {
          //   tenantId: '000000',
          //   parentId: this.userInfo['dept_id']
          // };
          // const res = await getCurrentChild(params);
          // let arr = res.data.data || [];
          // let ids = arr.map((item) => item.parentId);
          // this.expandedQueryArr = [...ids];
          this.queryStatus = true;
        } catch (e) {
          console.error(e);
        }
      },
      // 重置
      reset() {
        this.resetStatus = true;
        this.queryDeptName = '';
        this.expandedQueryArr = [];
      },
      // 部门tree single emit 替换
      setNodeSingle(node) {
        this.nodeList = node;
      },
      // 部门tree more emit 添加
      setNodeMorePush(nodes) {
        const _temp = concat(this.nodeList, nodes);
        const _arr = _temp.map((item) => {
          return {
            id: item.id,
            deptName: item.deptName
          };
        });
        this.nodeList = uniqWith(_arr, isEqual);
      },
      // 删除部门
      setNodeMoreSplice(node) {
        delete node.leaf;
        delete node.parentId;
        const index = this.nodeList.findIndex((item) => item.id === node.id);
        this.nodeList.splice(index, 1);
      },
      // 触发校验
      dispatch(componentName, eventName, params) {
        let parent = this.$parent || this.$root;
        let name = parent.$options.componentName;

        while (parent && (!name || name !== componentName)) {
          parent = parent.$parent;

          if (parent) {
            name = parent.$options.componentName;
          }
        }
        if (parent) {
          parent.$emit.apply(parent, [eventName].concat(params));
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .input_wrapper {
    display: inline-block;
    width: 180px;
    margin: 0 10px;
  }

  .content-wrapper {
    height: calc(100% - 47px);
  }

  .h100 {
    height: 100%;
  }
</style>
