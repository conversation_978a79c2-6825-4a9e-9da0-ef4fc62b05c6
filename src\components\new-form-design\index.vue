<template>
  <div class="oaFormDesign">
    <form-left-list
      @click-cell="clickCell"
      :show-tab="showTab"
      :exclude="exclude"
    ></form-left-list>
    <section v-if="formType">
      <div class="phoneStyle">
        <form-center-type
          ref="formCenterType"
          @selectCell="selectCell"
          @setList="setList"
        ></form-center-type>
      </div>
    </section>
    <section v-else class="web-set">
      <div class="web-style">
        <div>
          <div class="web-title">默认字段(不可编辑)</div>
          <slot name="defaultForm"></slot>
        </div>
        <div class="web-title">自定义字段</div>
        <el-form label-width="160px">
          <form-center-type-web
            ref="formCenterType"
            @selectCell="selectCell"
            @setList="setList"
          >
          </form-center-type-web>
        </el-form>
      </div>
    </section>

    <form-right-data
      :list-obj="listObj"
      :data-list="dataList"
    ></form-right-data>
  </div>
</template>
<script>
  import formRightData from './form-right-data';
  import formLeftList from './form-left-list';
  import formCenterType from './form-center-type';
  import formCenterTypeWeb from './form-center-type-web';
  import { deepClone } from '@/util/util.js';
  import formDataList from './data/index.js';
  import ydFormDataList from './data/yd-data.js';
  import htFormDataList from './data/ht-data.js';
  import { message } from '@/components/message';

  export default {
    components: {
      formRightData,
      formLeftList,
      formCenterType,
      formCenterTypeWeb
    },
    props: {
      formDesignList: {
        type: Array,
        default: () => []
      },
      showTab: {
        type: String,
        default: ''
      },
      exclude: {
        type: Array,
        default: () => []
      },
      formType: {
        type: Boolean,
        default: true
      }
    },
    data() {
      return {
        listObj: {},
        dataList: [],
        length: 0,
        submitCloneDataList: []
      };
    },
    watch: {
      formDesignList: {
        handler() {
          let sourceData = [
            ...formDataList.component,
            ...formDataList.componentGroup
          ];
          // 云盾初始化数据
          if (this.$route.query.appCode === 'ydzk') {
            sourceData = sourceData.concat(ydFormDataList);
          }
          // 合同初始化数据
          sourceData = sourceData.concat(htFormDataList);
          sourceData = sourceData.reduce((result, item) => {
            return result.concat(item.children);
          }, []);

          const list = this.getList(this.formDesignList, sourceData);
          this.dataList = deepClone(list);
          this.$refs.formCenterType.list.push(...this.dataList);

          // 数据初始化的时候最后一项为表单时数据监听有问题，所以需要添加一项非表单的对象过渡
          this.$nextTick(() => {
            this.clickCell(sourceData[0]);
            this.$nextTick(() => {
              const index = this.$refs.formCenterType.list.length - 1;
              this.$refs.formCenterType.delectList(index);
            });
          });
        },
        deep: true
      }
    },
    methods: {
      getList(li, sourceData) {
        const list = [];
        li.forEach((f) => {
          sourceData.forEach((d) => {
            if (d.type === f.type) {
              const obj = deepClone(d);
              if (d.type === 'form') {
                obj.children = this.getList(f.children, sourceData);
              }
              obj.id = f.id;
              obj.valueJson = f.valueJson;
              list.push(obj);
            }
          });
        });
        return list;
      },
      selectCell(obj) {
        this.listObj = {};
        this.$nextTick(() => {
          this.listObj = obj || {};
        });
      },
      clickCell(c) {
        this.$refs.formCenterType.list.push(c);
      },
      setList(list) {
        this.dataList = list;
        this.$store.commit('SET_FORM_DESIGN_LIST', list);
        if (this.$store.state.oaSetUp.isValidate) {
          this.$nextTick(() => {
            this.$emit('update-error-number', true);
          });
        }
      },
      flatComputedData(obj, key, oldKey) {
        const newArr = [];
        if (!obj[key] || !obj[key].formulaFlat) {
          return [];
        }
        for (let f of obj[key].formulaFlat) {
          if (f.value === oldKey) {
            message({
              type: 'error',
              message: '计算公式存在相互引用！',
              duration: 1500
            });
            this.length++;
            return [];
          }
          if (f.value.startsWith('computed')) {
            newArr.push(...this.flatComputedData(obj, f.value, oldKey));
          } else {
            newArr.push(f);
          }
        }
        return newArr;
      },
      // 将计算公式的数据处理一下，方便App端用， 呵呵
      computedChange() {
        const obj = {};
        this.dataList.forEach((d) => {
          if (d.id.startsWith('computed')) {
            obj[d.id] = deepClone(d.valueJson);
            obj[d.id].formulaFlat = deepClone(d.valueJson.formula);
          } else if (d.type === 'form') {
            d.children.forEach((dc) => {
              obj[dc.id] = deepClone(dc.valueJson);
              obj[dc.id].formulaFlat = deepClone(dc.valueJson.formula);
            });
          }
        });

        // 递归找所有有关联的id
        for (let key in obj) {
          if (obj.hasOwnProperty(key)) {
            obj[key].formulaFlat = this.flatComputedData(obj, key, key);
          }
        }

        // 计算公式重新赋值
        this.submitCloneDataList = deepClone(this.dataList);
        this.submitCloneDataList.forEach((d) => {
          if (!this.length) {
            if (obj[d.id]) {
              d.valueJson = obj[d.id];
            } else if (d.type === 'form') {
              d.children.forEach((dc) => {
                obj[dc.id] && (dc.valueJson = obj[dc.id]);
              });
            }
          }
        });
      },
      submit() {
        const list = this.submitCloneDataList.map((l) => {
          const obj = {
            id: l.id,
            type: l.type,
            valueJson: l.valueJson
          };
          if (l.children) {
            // 主要针对控件或控件组精简数据
            obj.children = [];
            l.children.forEach((lc) => {
              const objChildren = {
                id: lc.id || '',
                type: lc.type,
                valueJson: lc.valueJson || {}
              };

              // 外出套件将最外层日期类型加到开始时间结束时间里面
              if (l.type === 'out' && objChildren.type === 'date') {
                objChildren.valueJson.dateType = l.valueJson.dateType;
              }

              // 出差套件数据项修改
              if (lc.children) {
                objChildren.children = lc.children;

                // 出差套件将最外层日期类型加到开始时间结束时间里面
                if (l.type === 'trip') {
                  objChildren.children.forEach((oc) => {
                    if (oc.type === 'daterange') {
                      oc.valueJson.dateType = l.valueJson.dateType;
                    }
                  });
                }
              }

              // 加班套件允许为他人提交去掉desc
              const isDesc =
                l.type === 'work' &&
                l.valueJson &&
                l.valueJson.instead &&
                lc.type === 'desc';

              // 出差套件允许同行人按钮关闭时去掉同行人
              const isPeople =
                l.type === 'trip' &&
                lc.type === 'people' &&
                l.valueJson &&
                !l.valueJson.peerPeople;

              if (!(isDesc || isPeople)) {
                obj.children.push(objChildren);
              }
            });
          }
          return obj;
        });
        return list;
      },
      validate() {
        this.length = Object.values(
          this.$store.state.oaSetUp.oaComponentsErrorList
        ).reduce((total, item) => {
          return total + item.length;
        }, 0);
        this.computedChange();
        return this.length;
      }
    }
  };
</script>
<style lang="scss">
  .oaFormDesign {
    display: flex;

    section {
      display: flex;
      flex: 1;
      margin-left: calc(50% - 542px);
      overflow: auto;

      &::-webkit-scrollbar-thumb {
        background-color: transparent;
      }

      &.web-set {
        margin-left: 0;
        padding: 0 40px;
      }

      .phoneStyle {
        display: flex;
        width: 332px;
        min-height: 480px;
        margin-top: 100px;
        margin-bottom: 100px;
        padding: 44px 16px;

        // box-sizing: border-box;
        background: #fff;
        border-radius: 42px;
      }

      .web-style {
        display: flex;
        flex-direction: column;
        box-sizing: border-box;
        width: 800px;
        min-width: 500px;
        height: calc(100% - 200px);
        margin: 100px auto;
        overflow-y: auto;

        // padding: 24px;
        background: #fff;
        border-radius: 4px;
        box-shadow: 0 12px 24px 0 rgba(0, 0, 0, 5%);

        .web-title {
          padding-left: 24px;
          font-size: 16px;
          line-height: 64px;
        }
      }
    }
  }
</style>
