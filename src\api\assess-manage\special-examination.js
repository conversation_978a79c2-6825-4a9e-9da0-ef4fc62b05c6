import request from '@/router/axios';
// 列表
export const getPage = (data) => {
  return request({
    url: '/api/rw-manage/score/page',
    method: 'post',
    data
  });
};
// 导出
export const exportExcel = (data) => {
  return request({
    url: '/api/rw-manage/exportMultiSheet',
    method: 'post',
    responseType: 'blob',
    data
  });
};
// 专项检查-保存
export const saveFetch = (data) => {
  return request({
    url: '/api/rw-manage/score/save',
    method: 'post',
    data
  });
};
// 专项检查-提交
export const submitFetch = (data) => {
  return request({
    url: '/api/rw-manage/score/submit',
    method: 'post',
    data
  });
};

// 根据ID获取数据
export const getDetail = (params) => {
  return request({
    url: `/api/rw-manage/detail`,
    method: 'get',
    params
  });
};
// 专项检查-删除 批量
export const batchDelete = (data) => {
  return request({
    url: '/api/rw-manage/score/delete',
    method: 'post',
    data
  });
};
// 归档
export const placeOnFile = (data) => {
  return request({
    url: '/api/rw-manage/placeOnFile',
    method: 'post',
    data
  });
};
// 专项检查-取消材料锁定
export const unLockFile = (data) => {
  return request({
    url: '/api/rw-manage/unLockFile',
    method: 'post',
    data
  });
};
// 专项检查-材料锁定
export const lockFile = (data) => {
  return request({
    url: '/api/rw-manage/lockFile',
    method: 'post',
    data
  });
};
