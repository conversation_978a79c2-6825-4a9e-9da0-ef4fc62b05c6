<template>
  <base-echart
    class="bar-crosswise-echart"
    width="100%"
    height="100%"
    :option="option"
  />
</template>

<script>
  import BaseEchart from './base-echart.vue';

  export default {
    components: {
      BaseEchart
    },
    data() {
      return {
        option: {}
      };
    },
    mounted() {
      setTimeout(() => {
        this.option = {
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            }
          },
          legend: { show: false },
          grid: {
            top: 20,
            left: 120,
            right: 20,
            bottom: 30,
            containLabel: false
          },
          xAxis: {
            type: 'value',
            axisLabel: {
              fontSize: 10
            },
            axisTick: {
              show: true
            },
            minInterval: 1,
            axisLine: {
              show: true,
              lineStyle: {
                color: '#8890a1'
              }
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: ['#8890a1'],
                width: 1,
                type: 'solid'
              }
            }
          },
          yAxis: [
            {
              type: 'category',
              axisLabel: {
                fontSize: 14,
                lineHeight: 14,
                interval: 0, //横轴信息全部显示
                rotate: -30, //-30度角倾斜显示
                textStyle: {
                  align: 'right'
                },
                margin: 5 //刻度标签与轴线之间的距离。
              },
              nameTextStyle: {
                color: ['#44c2fe']
              },
              axisLine: {
                lineStyle: {
                  color: '#44c2fe',
                  width: 1 //这里是为了突出显示加上的
                }
              },
              // axisLine: {
              //   lineStyle: {
              //     color: 'rgb(58,88,120)'
              //   }
              // },
              // axisTick: {
              //   show: false
              // },
              data: [
                '兖矿能源',
                '兖矿能源',
                '信息技术中心办公室',
                '信息技术中心',
                'e类',
                'f类',
                'g类',
                'h类',
                'i类',
                'j类',
                'k类',
                'l类'
              ]
            }
          ],
          series: [
            {
              name: '2011',
              type: 'bar',
              barWidth: 12,
              itemStyle: {
                color: '#44c2fe'
              },
              data: [15, 20, 25, 56, 25, 25, 37, 27, 47, 18, 26, 44]
            }
          ],
          dataZoom: [
            {
              type: 'slider',
              show: false, //隐藏或显示（true）组件
              backgroundColor: 'rgb(19, 63, 100)', // 组件的背景颜色。
              fillerColor: 'rgb(16, 171, 198)', // 选中范围的填充颜色。
              borderColor: 'rgb(19, 63, 100)', // 边框颜色
              showDetail: false, //是否显示detail，即拖拽时候显示详细数值信息
              startValue: 0, // 数据窗口范围的起始数值
              endValue: 5, // 数据窗口范围的结束数值（一页显示多少条数据）
              yAxisIndex: [0, 1], //控制哪个轴，如果是 number 表示控制一个轴，如果是 Array 表示控制多个轴。此处控制第二根轴
              filterMode: 'empty',
              width: 8, //滚动条高度
              height: '80%', //滚动条显示位置
              right: 3, // 距离右边
              handleSize: 0, //控制手柄的尺寸
              zoomLoxk: true, // 是否锁定选择区域（或叫做数据窗口）的大小
              top: 'middle'
            },
            {
              //没有下面这块的话，只能拖动滚动条，鼠标滚轮在区域内不能控制外部滚动条
              type: 'inside',
              yAxisIndex: [0, 1], //控制哪个轴，如果是 number 表示控制一个轴，如果是 Array 表示控制多个轴。此处控制第二根轴
              zoomOnMouseWheel: false, //滚轮是否触发缩放
              moveOnMouseMove: true, //鼠标移动能否触发平移
              moveOnMouseWheel: true //鼠标滚轮能否触发平移
            }
          ]
        };

        setInterval(() => {
          // 每次向后滚动一个，最后一个从头开始。
          if (this.option.dataZoom[0].endValue == 11) {
            this.option.dataZoom[0].endValue = 5;
            this.option.dataZoom[0].startValue = 0;
          } else {
            this.option.dataZoom[0].endValue =
              this.option.dataZoom[0].endValue + 1;
            this.option.dataZoom[0].startValue =
              this.option.dataZoom[0].startValue + 1;
          }
        }, 2000);
      }, 3000);
    }
  };
</script>

<style scoped></style>
