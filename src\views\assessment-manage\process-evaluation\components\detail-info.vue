<template>
  <div>
    <el-descriptions v-if="detail">
      <el-descriptions-item
        label="考核单号"
        label-class-name="my-label"
        content-class-name="my-content"
        >{{ detail.rwNo || '--' }}</el-descriptions-item
      >
      <el-descriptions-item
        label="发起组织"
        label-class-name="my-label"
        content-class-name="my-content"
        >{{ detail.initiateOrgName || '--' }}</el-descriptions-item
      >
      <el-descriptions-item
        label="考核周期"
        label-class-name="my-label"
        content-class-name="my-content"
        >{{ detail.periodName || '--' }}</el-descriptions-item
      >
      <el-descriptions-item
        label="被考核机构"
        label-class-name="my-label"
        content-class-name="my-content"
        >{{ detail.assessedOrgName || '--' }}</el-descriptions-item
      >
      <el-descriptions-item
        label="考核日期"
        label-class-name="my-label"
        content-class-name="my-content"
        >{{ detail.rwDate || '--' }}</el-descriptions-item
      >
      <el-descriptions-item
        label="体系名称"
        label-class-name="my-label"
        content-class-name="my-content"
        >{{ detail.schemeName || '--' }}</el-descriptions-item
      >

      <el-descriptions-item
        label="来源单号"
        :span="3"
        label-class-name="my-label"
        content-class-name="my-content"
        >{{ detail.sourceName || '/' }}
      </el-descriptions-item>
      <el-descriptions-item
        label="备注"
        :span="3"
        label-class-name="my-label"
        content-class-name="my-content"
        >{{ detail.remark || '--' }}</el-descriptions-item
      >
      <el-descriptions-item
        label="附件"
        :span="3"
        label-class-name="my-label"
        content-class-name="my-content"
      >
        <h-upload
          ref="upload"
          v-model="detail.processFileList"
          showFileList
          show-loading
          :disabled="true"
          multiple
        >
          <el-button type="primary" icon="el-icon-upload">上传文件 </el-button>
        </h-upload>
      </el-descriptions-item>
      <el-descriptions-item
        label="创建人"
        label-class-name="my-label"
        content-class-name="my-content"
        >{{ detail.createUserName || '--' }}</el-descriptions-item
      >
      <el-descriptions-item
        label="创建日期"
        label-class-name="my-label"
        content-class-name="my-content"
        >{{ detail.createTime || '--' }}</el-descriptions-item
      >
    </el-descriptions>
  </div>
</template>
<script>
  import { dateFormat } from '@/util/date';
  export default {
    name: 'ProjectLibraryBasicInfo',
    filters: {
      dateFormat(value) {
        if (!value) return '--';
        let val = new Date(value);
        let date = dateFormat(val, 'yyyy-MM-dd');
        return date || '--';
      }
    },
    props: {
      detail: {
        type: Object,
        default: () => {}
      }
    },
    data() {
      return {};
    },
    methods: {}
  };
</script>
<style lang="scss">
  .my-label {
    width: 130px;
    text-align: right;
  }

  .my-content {
    display: flex;
    gap: 5px;
    max-width: 430px;
  }

  .project-label {
    display: flex;
    flex-wrap: wrap;
  }

  .el-descriptions :not(.is-bordered) .el-descriptions-item__cell {
    vertical-align: baseline;
  }
</style>
