.left-monitoring-container {
  height: 100%;

  .left-search{
    display: grid;
    grid-template-columns: 180px auto;
    margin-bottom: 10px;

    .search-item{
      display: grid;
      grid-template-columns: auto auto;
    }
  }

  .left-content{
    display: grid;
    grid-template-rows: 1fr 1fr 1fr 1fr;
    gap: 10px;
    height: 94%;

    &-item{
      display: grid;
      grid-template-rows: 30px auto;
      text-align: center;
      background-image: url('./imgs/border.png');

      // background-size: cover;
      background-size: 100% 100%;

      .content-title{
        width: 100%;
        height: 30px;

        // padding: 5px 20px;
        color: #fff;
        font-size: 16px;
        line-height: 30px;
        background: linear-gradient(90deg, transparent 0%, #0057b0 51%, transparent 100%);
      }

      .chart-wrap{
        height: 100%;

        // background-color: aquamarine;
      }
    }
  }
}
