<template>
  <Dialog
    v-if="visible"
    :visible="visible"
    :title="title"
    width="90%"
    @closed="hide"
  >
    <el-button
      @click="exportResult"
      type="warning"
      style="margin: 0 0 10px"
      :loading="exportLoading"
      icon="el-icon-download"
      plain
      >{{ exportLoading ? '导出中' : '导出' }}</el-button
    >
    <el-table
      :data="tableData"
      v-loading="loading"
      border
      stripe
      v-if="showTable"
      :span-method="objectSpanMethod"
      :header-cell-style="{ backgroundColor: '#fafafa' }"
      style="width: 100%"
      class="mt-10"
      row-key="id"
      ref="multipleTable"
    >
      <el-table-column type="index" width="40" align="center" label="序列" />
      <el-table-column width="75" align="center" label="考核项目">
        <template slot-scope="{ row }">
          {{
            [null, undefined, ''].includes(row.classifyName)
              ? '--'
              : row.classifyName
          }}
        </template>
      </el-table-column>
      <el-table-column header-align="center" label="指标">
        <template slot-scope="{ row }">
          {{
            [null, undefined, ''].includes(row.evaluateTarget)
              ? '--'
              : row.evaluateTarget
          }}
        </template>
      </el-table-column>
      <el-table-column prop="score" align="center" width="75" label="分值">
        <template slot-scope="{ row }">
          {{ [null, undefined, ''].includes(row.score) ? '--' : row.score }}
        </template>
      </el-table-column>
      <el-table-column header-align="center" label="评分标准">
        <template slot-scope="{ row }">
          {{
            [null, undefined, ''].includes(row.scoreMethod)
              ? '--'
              : row.scoreMethod
          }}
        </template>
      </el-table-column>
      <el-table-column align="center" width="100" label="考核方式">
        <template slot-scope="{ row }">
          {{
            [null, undefined, ''].includes(row.rwTypeName)
              ? '--'
              : row.rwTypeName
          }}
        </template>
      </el-table-column>
      <el-table-column width="75" align="center" label="扣/加分">
        <template slot-scope="{ row }">
          <!-- {{ [null, undefined, ''].includes(row.rwScore) ? '--' : row.rwScore }} -->
          {{ !row.rwScore ? '' : row.addScore === '0' ? '-' : '+'
          }}{{ row.rwScore || '0' }}
        </template>
      </el-table-column>
      <el-table-column
        header-align="center"
        width="150"
        prop="rwCause"
        label="扣/加分原因"
      >
        <template slot-scope="{ row }">
          <div style="white-space: pre">
            <pre
              style="
                margin: 0;
                font-family: none;
                white-space: pre-wrap||pre-line;
              "
              >{{ row.rwCause ? row.rwCause : '无' }}</pre
            >
          </div>
        </template>
      </el-table-column>
    </el-table>
    <yk-pagination
      small
      v-show="total > 0"
      :total="total"
      :page.sync="queryForm.current"
      :limit.sync="queryForm.size"
      @pagination="getList"
    />
    <div slot="footer">
      <el-button type="primary" plain @click="hide">取消</el-button>
      <!-- <el-button type="primary"  @click="onSubmit">确定 </el-button> -->
    </div>
  </Dialog>
</template>
<script>
  import Dialog from '@/components/basic-dialog';
  // import { getAllMaterialList } from "@/api/distribution-apply";
  import { getDetail, exportDetail } from '@/api/assess-manage/examine-result';
  import { trimAll, deepClone, downloadXls } from '@/util/util';
  import { dateNow } from '@/util/date';
  import NProgress from 'nprogress';
  import 'nprogress/nprogress.css';
  const queryForm = {
    periodType: undefined,
    periodName: undefined,
    size: 10,
    current: 1
  };
  export default {
    // serviceDicts: ['period_type'],
    components: { Dialog },
    props: {
      deptTree: { type: Array, require: () => [] },
      categoryTree: { type: Array, require: () => [] }
    },
    data() {
      return {
        visible: false,
        loading: false,
        showTable: false,
        tableData: [],
        total: 0,
        tableRadio: undefined,
        title: '考核明细',
        // ------------
        editSelected: {}, // 已选中编辑过的列表
        queryForm: { ...queryForm },
        cycleOptions: [],
        exportLoading: false
      };
    },
    methods: {
      exportResult() {
        // this.$confirm('是否导出考核结果明细数据?', '提示', {
        //   confirmButtonText: '确定',
        //   cancelButtonText: '取消',
        //   type: 'warning'
        // }).then(() => {
        NProgress.start();
        this.exportLoading = true;
        let { assessedOrgId, initiateOrgId, rwPeriodDetailId, schemeId } =
          this.editSelected;
        const params = {
          assessedOrgId: trimAll(assessedOrgId),
          initiateOrgId: trimAll(initiateOrgId),
          rwPeriodDetailId: trimAll(rwPeriodDetailId),
          schemeId: trimAll(schemeId)
        };
        try {
          exportDetail(Object.assign(params))
            .then((res) => {
              downloadXls(res.data, `考核结果明细数据表${dateNow()}.xlsx`);
              NProgress.done();
              this.exportLoading = false;
            })
            .catch(() => {
              this.exportLoading = false;
            });
        } catch (error) {
          this.exportLoading = false;
        }
        // });
      },
      // 合并
      objectSpanMethod({ row, rowIndex, columnIndex }) {
        let classifyColumn = [1];
        if (classifyColumn.includes(columnIndex)) {
          let colspan = this.getDynamicColspan('classifyNumber', 'classifyId');
          // console.log('colspan classifyNumber', colspan);
          if (colspan.includes(rowIndex)) {
            return {
              rowspan: row.classifyNumber,
              colspan: 1
            };
          } else {
            return {
              rowspan: 0,
              colspan: 0
            };
          }
        }
        let mergeColumn = [2, 3, 5];
        if (mergeColumn.includes(columnIndex)) {
          let colspan = this.getDynamicColspan(
            'evaluateNumber',
            'evaluateTargetId'
          );
          // console.log('colspan evaluateNumber', colspan);
          if (colspan.includes(rowIndex)) {
            return {
              rowspan: row.evaluateNumber,
              colspan: 1
            };
          } else {
            return {
              rowspan: 0,
              colspan: 0
            };
          }
        }
      },
      // 获取动态开始合并行号 = 排序、数量
      getDynamicColspan(flag, key) {
        let list = this.tableData;
        // debugger;
        let uniqueArr = this.uniqueFunc(list, flag, key);
        // let arr = list.map((item) => item.columnIndicator);
        return uniqueArr;
      },
      // 计算开始合并的初始行号
      uniqueFunc(arr, flag, key) {
        // 去重
        const res = new Map();
        for (const item of arr) {
          res.set(item[key], item);
        }
        let uniqueArr = [...res.values()];

        let classifyId = uniqueArr.map((item) => item[flag]);
        // debugger;
        // 计算开始合并的初始行号
        let additionArr = [];
        classifyId.forEach((e, i) => {
          if (i === 0) {
            additionArr.push(0, e);
          } else {
            additionArr.push(additionArr[i] + e);
          }
        });
        return additionArr;
      },
      getCurrentRow(row) {
        this.editSelected = row;
      },
      // 检索
      async queryText() {
        Object.assign(this.queryForm, {
          current: 1,
          size: 10
        });
        await this.request();
      },
      // 请求列表
      async request() {
        this.loading = true;
        this.showTable = false;
        let { assessedOrgId, initiateOrgId, rwPeriodDetailId, schemeId } =
          this.editSelected;
        const params = {
          assessedOrgId: trimAll(assessedOrgId),
          initiateOrgId: trimAll(initiateOrgId),
          rwPeriodDetailId: trimAll(rwPeriodDetailId),
          schemeId: trimAll(schemeId)
        };
        try {
          const {
            data: { data }
          } = await getDetail(params);
          // const { records = [], total = 0 } = data || {};
          // this.total = total;
          this.tableData = data || [];
          // this.resetSelected();
        } catch (e) {
          console.error(e);
        } finally {
          this.loading = false;
          this.showTable = true;
        }
      },
      // 归类
      localUpdate(arr) {
        const classifyArr = this.arrGroup(
          arr,
          'classifyNumber',
          (item) => item.classifyId
        );
        const evaluateArr = this.arrGroup(
          classifyArr,
          'evaluateNumber',
          (item) => item.evaluateTargetId
        );
        return deepClone(evaluateArr);
      },
      // 归类 key 合并数
      arrGroup(arr, key, fn) {
        const obj = {};
        arr.forEach((item) => {
          const key = JSON.stringify(fn(item));
          obj[key] = obj[key] || [];
          obj[key].push(item);
        });
        // debugger; 原始
        let result = [];
        Object.keys(obj).forEach((k) => {
          obj[k].forEach((o) => {
            o[key] = obj[k].length;
          });
          result = [...result, ...obj[k]];
        });
        return result;
      },
      // 分页查询
      getList({ page, limit }) {
        Object.assign(this.queryForm, {
          current: page,
          size: limit
        });
        this.request();
      },
      // 提交
      onSubmit() {
        // this.$message.success('保存成功');
        this.$emit('save-success', this.editSelected);
        this.hide();
      },
      async show(row = {}) {
        this.visible = true;
        this.editSelected = row;
        await this.queryText();
      },
      // 重置table选中显示
      resetSelected() {
        let keys = Object.keys(this.editSelected);
        if (!keys) return;
        let obj = this.tableData.find(
          (item) => item.id == this.editSelected.id
        );
        this.tableRadio = obj || undefined;
      },
      resetPage() {
        this.queryForm = { ...queryForm };
      },
      reset() {
        this.resetPage();
        this.queryText();
      },
      hide() {
        this.tableRadio = undefined;
        this.editSelected = {};
        this.resetPage();
        this.visible = false;
      }
    }
  };
</script>
<style scoped lang="scss">
  ::v-deep .el-dialog__body {
    padding: 15px 20px;
  }

  /deep/ .el-table .el-table__header .cell {
    color: #909399 !important;
  }

  /deep/ .el-button--small span {
    font-size: 13px !important;
  }

  /deep/ .el-table .cell {
    font-size: 14px;
  }
</style>
