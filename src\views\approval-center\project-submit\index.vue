<template>
  <basic-container autoHeight v-loading="loading">
    <search @search="query" ref="search" :activeName="activeName" />
    <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
      <el-tab-pane label="待办理" name="0"></el-tab-pane>
      <el-tab-pane
        v-if="
          currentBusinessScope['parentId'] === '0' &&
          (currentBusinessScope['contactPerson'] || '').includes('PROJECT')
        "
        label="办理中"
        name="2"
      ></el-tab-pane>
      <el-tab-pane label="已办理" name="1"></el-tab-pane>
      <!-- <el-tab-pane label="已发起" name="2"></el-tab-pane> -->
    </el-tabs>
    <transition name="el-fade-in-linear">
      <table-info
        :source="tableData"
        ref="tableInfo"
        :loading="loading"
        :activeName="activeName"
        :showReviewBtn="showReviewBtn"
        @dispatch="dispatch"
      >
        <div slot="operateBtnContent" style="margin-bottom: 6px">
          <el-button
            :disabled="!rows.length"
            type="primary"
            v-if="
              permission['approval-submit-transact'] &&
              ['0', '2'].includes(activeName)
            "
            icon="el-icon-check"
            @click="handleAdd"
            >办理</el-button
          >
          <el-button
            type="primary"
            v-if="
              permission['approval-submit-all-transact'] &&
              ['0', '2'].includes(activeName)
            "
            icon="el-icon-check"
            @click="handleAllAdd"
            >全部办理</el-button
          >
          <el-button
            @click="buildReport()"
            type="warning"
            v-if="
              permission['approval-submit-report'] &&
              ['0', '2'].includes(activeName)
            "
            icon="el-icon-download"
            plain
            >生成报表</el-button
          >
          <el-button
            type="success"
            v-if="
              permission['approval-submit-update'] &&
              hasContactBtn &&
              ['0', '2'].includes(activeName)
            "
            icon="el-icon-refresh"
            plain
            @click="updateProjectContacts"
            >更新项目联络人</el-button
          >
          <el-button
            type="success"
            :disabled="!rows.length"
            v-if="
              hasContactBtn &&
              currentBusinessScope['parentId'] === '0' &&
              ['0', '2'].includes(activeName)
            "
            icon="el-icon-d-arrow-right"
            plain
            @click="handleForwardExpertReview()"
            >一键转发专家评审</el-button
          >
          <el-button
            type="warning"
            plain
            :loading="exportLoading"
            icon="el-icon-download"
            @click="handleExport"
            >{{ exportLoading ? '下载附件中' : '批量下载附件' }}
          </el-button>
          <el-button
            type="primary"
            v-if="['2'].includes(activeName)"
            icon="el-icon-check"
            @click="generateProjectNumber"
            >生成项目编号</el-button
          >
          <!-- <el-button
        v-if="showReviewBtn && permission['approval-submit-opinion']"
        :disabled="!rows.length"
        type="primary"
        icon="el-icon-check"

        @click="handleReview"
        >审查意见</el-button
      > -->
        </div>
      </table-info>
    </transition>

    <yk-pagination
      small
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.current"
      :limit.sync="queryParams.size"
      @pagination="getList"
    />
    <!-- 批量操作结果 -->
    <Approval
      ref="approval"
      :showReviewBtn="showReviewBtn"
      @save-success="approvalSuccess"
    ></Approval>
    <ReviewComment
      ref="reviewComment"
      @close="handleDialogClose"
      :showReviewBtn="showReviewBtn"
      @save-success="reviewSuccess"
    ></ReviewComment>
    <ValidDialog
      v-if="validateVisible"
      :dialogTitle="dialogTitle"
      :formVisible="validateVisible"
      :list="batchList"
      :showProjectName="true"
      labelName="提交单位"
      @close="handleDialogClose"
    ></ValidDialog>
    <ExpertList
      ref="people"
      v-model="currentExpertList"
      @save-success="expertSuccess"
    ></ExpertList>
    <ErrorDialog
      :dialogTitle="dialogTitle"
      :formVisible="errorVisible"
      :list="batchList"
      labelName="项目名称"
      @refresh="request"
      @close="handleDialogClose"
    />
  </basic-container>
</template>

<script>
  import {
    Search,
    TableInfo,
    Approval,
    ReviewComment,
    ExpertList,
    ErrorDialog
  } from './components';
  import {
    approvePage,
    approvedVia,
    returnFlow,
    approveCancel,
    getReviewBtn,
    reviewSave,
    instanceCheckSubmit,
    userContact,
    flushContractPerson,
    exportExcel,
    updateContactApprovalStatus
  } from '@/api/workflow';
  import ValidDialog from '@/components/yk-validate-dialog';
  import {
    oneProjectSend,
    manyProjectSend
  } from '@/api/business-applications/expert-database';
  import { createBatchNo } from '@/api/system/declaration-settings';
  import { getStore } from '@/util/store';
  import { mapGetters } from 'vuex';
  import { trimAll, getJointUrl, downloadZip } from '@/util/util';
  import { env } from '@/config/env';
  import { dateNow } from '@/util/date';
  import 'nprogress/nprogress.css';

  export default {
    name: 'Project-progress',
    components: {
      Search,
      TableInfo,
      Approval,
      ReviewComment,
      ValidDialog,
      ExpertList,
      ErrorDialog
    },
    data() {
      return {
        // 查询条件
        queryParams: {
          size: 20,
          current: 1
        },
        // 表格加载中
        loading: false,
        // 列表条目总数量
        total: 0,
        // 列表数据
        tableData: [],
        // 是否显示
        formVisible: false,
        errorVisible: false,
        addVisible: false,
        validateVisible: false,
        // dialog标题
        dialogTitle: '',
        // 选中项
        rows: [],
        // 批量提报结果
        batchList: [],
        currentExpertList: [],
        activeName: '0',
        showReviewBtn: 1,
        hasContactBtn: false, // 是否有联络人按钮
        isSingleProject: false,
        currentRows: [],
        exportLoading: false
      };
    },
    computed: {
      ...mapGetters(['permission', 'userInfo']),
      currentBusinessScope() {
        let org = getStore({ name: 'current-organization' });
        return org || {};
      }
    },
    activated() {
      this.getUpdateProjectContacts();
      // if (!this.$route.params.remain) {
      //   this.activeName = '0';
      //   return this.$refs.search.resetQuery();
      // }
      this.rows = [];
      if (this.$route.query.home === '1') {
        this.activeName = '0';
        this.rows = [];
        this.$refs.search.resetQuery();
        return this.query({});
      }
      this.request();
      // this.getReviewBtn();
    },
    watch: {
      activeName(val) {
        if (val === '1') {
          return (this.showReviewBtn = 0);
        } else {
          return (this.showReviewBtn = 1);
        }
        // this.getReviewBtn();
      }
    },
    methods: {
      async generateProjectNumber() {
        await this.confirm('系统将生成项目编号，确认生成？');
        try {
          await createBatchNo();
          this.request();
          this.$message.success('生成成功');
        } catch (error) {
          console.log(error);
        }
      },
      handleExport() {
        this.exportLoading = true;
        let rows = this.rows;
        let ids = rows.map((item) => item.id);
        // let isSnLogin = this.currentBusinessScope['parentId'] === '0' ? 1 : 0;
        let act = this.activeName;
        const query = {
          ...this.queryParams,
          ids,
          // isSnLogin,
          approveType: ['0', '2'].includes(act) ? '0' : '1',
          contactApprovalStatus: act === '0' ? '0' : act === '2' ? '1' : null,
          businessType: '1'
        };
        exportExcel({ ...query })
          .then((res) => {
            downloadZip(res.data, `项目申报办理${dateNow()}.zip`);
            this.handleDialogClose();
            this.request();
            this.exportLoading = false;
          })
          .catch(() => {
            this.exportLoading = false;
            this.request();
          });
      },
      async expertSuccess(arr) {
        let experts = arr || [];
        let expertList = experts.map((item) => ({
          expertId: item.id || item.expertId,
          orgId: item.orgId,
          userId: item.userId,
          userName: item.userName,
          onlineStatus: item.onlineStatus
        }));
        let projectList = this.currentRows.map((item) => ({
          projectId: item.businessId,
          projectName: item.description
        }));
        let params = {
          expertList,
          projectList
        };
        try {
          const Func = this.isSingleProject ? oneProjectSend : manyProjectSend;
          let {
            data: { data }
          } = await Func(params);
          // if (this.isSingleProject) {
          //   this.handleDialogClose();
          //   this.$message.success('一键转发专家评审成功');
          //   this.request();
          //   return;
          // }
          this.batchList = data || [];
          this.errorVisible = true;
          this.dialogTitle = '一键转发专家评审结果';
        } catch (error) {
          this.handleDialogClose();
          console.log(error);
        }
      },
      handleForwardExpertReview(row, flag) {
        this.isSingleProject = !!flag || this.rows.length === 1;
        this.currentExpertList = flag
          ? row.expertReviewList
          : this.rows.length === 1
          ? this.rows[0].expertReviewList
          : [];
        this.currentRows = flag ? [row] : this.rows;
        this.$refs.people.visible = true;
      },
      async getUpdateProjectContacts() {
        try {
          let {
            data: { data }
          } = await userContact();
          let arr = data || [];
          this.hasContactBtn = arr.includes('PROJECT');
        } catch (error) {
          this.hasContactBtn = false;
        }
      },
      // 更新项目联络人
      async updateProjectContacts() {
        try {
          this.loading = true;
          await flushContractPerson();
          this.handleDialogClose();
          this.request();
        } catch (error) {
          this.loading = false;
          // this.hasContactBtn = false;
        }
      },
      buildReport() {
        let rows = this.rows;
        let ids = rows.map((item) => item.id).join();
        let isSnLogin = this.currentBusinessScope['parentId'] === '0' ? 1 : 0;
        let url =
          env === 'production'
            ? 'https://fr.ykjt.cc:8756/webroot/decision/view/report?viewlet=%25E6%2595%25B0%25E6%2599%25BA%25E4%25BA%2591%25E7%25A7%2591%252F%25E9%25A1%25B9%25E7%259B%25AE%25E7%2594%25B3%25E6%258A%25A5%25E5%258A%259E%25E7%2590%2586.cpt&ref_t=design&ref_c=2f4c0ae8-0f04-4784-94d9-49c25a741561'
            : 'https://frdev.ykjt.cc:8756/webroot/decision/v10/entry/access/af23a88f-64ec-403a-b439-297662d9238f?preview=true';
        // window.open(
        //   `${url}&approveType=${this.activeName || ''}&year=${
        //     this.queryParams.year || ''
        //   }&businessType=1&projectName=${
        //     this.queryParams.projectName || ''
        //   }&curStatus=${this.queryParams.curStatus || ''}&applyStartTime=${
        //     this.queryParams.applyStartTime || ''
        //   }&applyEndTime=${this.queryParams.applyEndTime || ''}&applyUserName=${
        //     this.queryParams.applyUserName || ''
        //   }&deptId=${this.queryParams.deptId || ''}&nextStepName=${
        //     this.queryParams.nextStepName || ''
        //   }&ids=${ids}&curDeptId=${this.userInfo['dept_id']}&curUserId=${
        //     this.userInfo['user_id']
        //   }&docReview=${this.queryParams.docReview || ''}`
        // );
        // &__filename__=${encodeURIComponent('项目申报办理')}${new Date()
        // .toLocaleString('zh-CN')
        // .replace(/[\\/,\s,:]+/g, '')}
        let act = this.activeName;
        const query = {
          ...this.queryParams,
          size: undefined,
          current: undefined,
          ids,
          isSnLogin,
          businessType: '1',
          curUserId: this.userInfo['user_id'],
          curDeptId: this.userInfo['dept_id'],
          userCodeParam: this.userInfo['account'],
          contactApprovalStatus: act === '0' ? '0' : act === '2' ? '1' : '',
          approveType: this.activeName
        };
        window.open(getJointUrl(url, query));
      },
      // 0没有1集团2二级
      async getReviewBtn() {
        try {
          // this.loading = true;
          let params = {
            businessType: '1'
          };
          const {
            data: { data }
          } = await getReviewBtn(params);
          this.showReviewBtn = data;
        } catch (e) {
          console.log(e);
        } finally {
          // this.loading = false;
        }
      },
      async reviewSuccess(obj) {
        try {
          const Func = reviewSave;
          await Func(obj);
          this.rows = [];
          this.$message.success('保存成功');
          this.request();
        } catch (error) {
          console.log(error);
        }
      },
      async approvalSuccess(obj) {
        try {
          this.loading = true;
          const Func = obj.approval === '1' ? approvedVia : returnFlow;
          await Func(obj);
          this.$message.success('操作成功');
          console.log('obj.approval', obj.approval);
          this.setPage();
          this.handleDialogClose();
          this.request();
        } catch (error) {
          console.log(error);
          this.request();
        }
      },
      handleClick() {
        this.rows = [];
        this.$refs.search.resetQuery();
        // let cancelProp = [];
        // if (this.activeName === '0') {
        //   cancelProp = ['approveStatus'];
        // } else {
        //   cancelProp = [
        //     'curStepName',
        //     'nextStepName',
        //     'docReview',
        //     'expertReviewList',
        //     'curReceive'
        //   ];
        // }
        // this.$refs.tableInfo.$refs.multipleTable.filterProp(cancelProp);
      },
      // 撤回
      async withdrawal(row) {
        try {
          await this.confirm('确定要撤回吗？');
          this.loading = true;
          let params = {
            instanceId: row.id
          };
          const {
            data: { data }
          } = await approveCancel(params);
          this.batchList = data || [];
          this.$message.success('操作成功');
          this.request();
        } catch (e) {
          console.log(e);
        } finally {
          this.loading = false;
        }
      },
      handleAddTab() {
        this.dialogTitle = '添加标签';
        this.addVisible = true;
      },
      setPage() {
        // let resultList = this.batchList;
        // let bool = resultList.every((item) => item.result);
        // if (!bool) return;
        let length = this.tableData.length;
        // if (row) {
        //   if (length === 1) {
        //     this.queryParams.current = 1;
        //   }
        // } else {
        let rowsLength = this.rows.length;
        if (rowsLength >= length) {
          this.queryParams.current = 1;
        }
        // }
      },
      // 查询
      query(params) {
        if (params.isResetQuery) {
          this.rows = [];
          this.currentRows = [];
        }
        Object.assign(
          this.queryParams,
          {
            current: 1,
            size: 20
          },
          { ...params, isResetQuery: undefined }
        );
        for (const key in this.queryParams) {
          this.queryParams[key] = trimAll(this.queryParams[key]);
        }
        this.request();
        // this.getReviewBtn();
      },
      // 分页查询
      getList({ page, limit }) {
        Object.assign(this.queryParams, {
          current: page,
          size: limit
        });
        this.request();
      },
      // 请求列表数据
      async request() {
        try {
          this.loading = true;
          let act = this.activeName;
          const { data } = await approvePage({
            ...this.queryParams,
            approveType: ['0', '2'].includes(act) ? '0' : '1',
            contactApprovalStatus: act === '0' ? '0' : act === '2' ? '1' : null,
            businessType: '1'
          });
          const { total = 0, records = [] } = data.data ? data.data : {};
          this.total = total;
          this.tableData = records;
          this.setSelected();
        } catch (e) {
          console.error(e);
        } finally {
          setTimeout(() => {
            this.loading = false;
          }, 1500);
        }
      },
      setSelected(flag) {
        let ids = this.rows.map((item) => item.id);
        let selectedRows = this.tableData.filter((item) =>
          ids.includes(item.id)
        );
        if (selectedRows.length) {
          this.$nextTick(() => {
            selectedRows.forEach((row) => {
              this.$refs['tableInfo'].$refs['multipleTable'] &&
                this.$refs['tableInfo'].$refs[
                  'multipleTable'
                ].toggleRowSelection(row, true);
            });
          });
        } else {
          this.$nextTick(() => {
            this.$refs['tableInfo'].$refs['multipleTable'] &&
              this.$refs['tableInfo'].$refs['multipleTable'].clearSelection();
          });
        }
        if (!flag) return;
        this.$nextTick(() => {
          this.$refs.tableInfo.showTable = false;
          setTimeout(() => {
            this.$refs.tableInfo.showTable = true;
            let ids = this.rows.map((item) => item.id);
            let selectedRows = this.tableData.filter((item) =>
              ids.includes(item.id)
            );
            if (selectedRows.length) {
              this.$nextTick(() => {
                selectedRows.forEach((row) => {
                  this.$refs['tableInfo'].$refs['multipleTable'] &&
                    this.$refs['tableInfo'].$refs[
                      'multipleTable'
                    ].toggleRowSelection(row, true);
                });
              });
            } else {
              this.$nextTick(() => {
                this.$refs['tableInfo'].$refs['multipleTable'] &&
                  this.$refs['tableInfo'].$refs[
                    'multipleTable'
                  ].clearSelection();
              });
            }
          }, 500);
        });
      },
      // 列表操作
      dispatch(type, data) {
        switch (type) {
          case 'commit':
            return this.withdrawal(data);
          case 'expertReview':
            return this.handleForwardExpertReview(data, true);
          case 'edit':
            return this.handleEdit(data);
          case 'check':
            return this.handleCheck(data);
          case 'move':
            return this.handleMove(data);
          case 'view':
            return this.handleView(data);
          case 'refresh':
            return this.request();
          case 'selection':
            return this.handleSelect(data);
          case 'selectionAll':
            return this.handleSelectAll(data);
          default:
            return false;
        }
      },
      async handleMove(row) {
        try {
          let { contactApprovalStatus, processId } = row;
          await this.confirm(
            contactApprovalStatus === '0'
              ? '确定要移到办理中吗？'
              : '确定要移到待办理吗？'
          );
          let params = {
            contactApprovalStatus: contactApprovalStatus === '0' ? '1' : '0',
            processId
          };
          await updateContactApprovalStatus(params);
          this.$message.success('操作成功');
          this.request();
        } catch ({ msg }) {
          msg && this.error(msg);
        }
      },
      handleCheck(row) {
        let rows = [row];
        let params = {
          show: 'detail',
          rows,
          businessType: '1'
        };
        this.$refs.reviewComment.show(params);
      },
      handleSelect(row) {
        let index = this.rows.findIndex((item) => item.id === row.id);
        if (index >= 0) {
          this.rows.splice(index, 1);
        } else {
          this.rows.push(row);
        }
      },
      uniqueFunc(arr, uniId) {
        let hash = {};
        return arr.reduce((accum, item) => {
          hash[item[uniId]]
            ? ''
            : (hash[item[uniId]] = true && accum.push(item));
          return accum;
        }, []);
      },
      handleSelectAll(rows) {
        if (!this.rows.length && rows.length)
          return (this.rows = [...this.rows, ...rows]);
        if (rows.length) {
          this.rows = this.uniqueFunc([...this.rows, ...rows], 'id');
        } else {
          // let clearSelection = this.tableData.map((item) => item.id);
          // this.rows = this.rows.filter(
          //   (item) => !clearSelection.includes(item.id)
          // );
          this.rows = [];
        }
      },
      validSomeCurrFlowSort() {
        let complexArr = [...this.rows];
        let first = complexArr[0].currFlowSort;
        let bool = complexArr.every((item) => item.currFlowSort === first);
        return bool;
      },
      validSomeStatus() {
        let complexArr = [...this.rows];
        let first = complexArr[0].curStatus;
        let bool = complexArr.every((item) => item.curStatus === first);
        return bool;
      },
      validSameNextNode() {
        let complexArr = [...this.rows];
        let first = complexArr[0].nextStepName;
        let bool = complexArr.every((item) => item.nextStepName === first);
        return bool;
      },
      // 办理所有
      async handleAllAdd() {
        try {
          this.loading = true;
          let act = this.activeName;
          const { data } = await approvePage({
            size: 500,
            current: 1,
            approveType: ['0', '2'].includes(act) ? '0' : '1',
            contactApprovalStatus: act === '0' ? '0' : act === '2' ? '1' : null,
            businessType: '1'
          });
          const { records = [] } = data.data ? data.data : {};
          this.$refs.search.resetQuery();
          setTimeout(() => {
            this.rows = records;
            this.handleAdd();
          }, 2000);
        } catch (e) {
          console.error(e);
        }
      },
      // 办理 节点
      async handleAdd() {
        // this.loading = true;
        // const { data } = await approvePage({
        //   size: 500,
        //   current: 1,
        //   approveType: this.activeName,
        //   businessType: '1'
        // });
        // this.loading = false;
        // const { total = 0 } = data.data ? data.data : {};
        // *** 去掉选中的所有数据currFlowSort都相等可以办理，原因：之前流程模板变更过导致，变更前和变更过后相同节点的flowSort不再一致，所以根据currFlowSort进行校验不再准确。另外根据当前流程状态和下个节点的校验已经完全避免了不同审批节点的流程批量提交的问题。去掉：请不要选择既是已退回状态又是不同层级的审核数据。这个校验已经没什么必要了，目前流程支持不同层级发起的审批流一起退回
        // let bool = this.validSomeCurrFlowSort();
        // if (!bool) {
        //   this.handleDialogClose();
        //   return this.warning('不同流程节点的业务不能同时办理');
        // }
        let nextbool = this.validSameNextNode();
        if (!nextbool) {
          this.handleDialogClose();
          return this.warning('不同下个节点的流程不能同时办理');
        }
        let boolStatus = this.validSomeStatus();
        if (!boolStatus) {
          this.handleDialogClose();
          return this.warning('不同状态的流程不能同时办理');
        }
        let complexArr = [...this.rows];
        // 有退回
        let hasBack = complexArr.some((item) => item.curStatus === 2);
        // backLogic：0退回后不可提交 1退回后可提交
        let backLogic0 = complexArr.some((item) => !item.backLogic);
        // 当前是节点是 分管领导
        let isLeaders = complexArr.every((item) =>
          (item.curStepName || '').includes('分管领导')
        );
        // row.docReview == 1
        let reviewPass = complexArr.filter(
          (item) => item.docReview == 1
        ).length;
        let reviewNoPass = complexArr.filter(
          (item) => item.docReview == 2
        ).length;
        let reviewPending = complexArr.filter(
          (item) => item.docReview == 0
        ).length;

        // 有不同层级
        let hasDifferent = false;
        if (complexArr.length >= 2) {
          let iniLevel = complexArr.map((item) => item.iniLevel);
          hasDifferent = complexArr.length === new Set(iniLevel).size;
        }

        // if (hasBack && hasDifferent) {
        //   this.handleDialogClose();
        //   return this.$message.warning(
        //     '请不要选择既是已退回状态又是不同层级的审核数据'
        //   );
        // }
        let checkObj = await this.instanceCheckSubmit(complexArr);
        let { dataList = [] } = checkObj;
        if (dataList.length) {
          this.batchList = dataList;
          this.dialogTitle = '验证结果';
          this.validateVisible = true;
          return;
        }
        const instanceIds = complexArr.map((item) => item.id);
        let params = {
          hasBack,
          isLeaders,
          hasDifferent,
          total: 0,
          businessType: '1',
          reviewPass,
          reviewNoPass,
          reviewPending,
          instanceIds,
          backLogic0
        };
        this.$refs.approval.show(params);
      },
      // 校验提报数据
      async instanceCheckSubmit(rows) {
        try {
          this.loading = true;
          let params = rows.map((item) => ({
            id: item.id,
            currFlowSort: item.currFlowSort
          }));
          const {
            data: { data }
          } = await instanceCheckSubmit(params);
          return data || false;
        } catch (e) {
          return false;
        } finally {
          this.loading = false;
        }
      },
      handleReview() {
        let bool = this.validSomeCurrFlowSort();
        if (!bool)
          return this.warning('不同流程节点的业务不能同时操作审查意见');
        let boolStatus = this.validSomeStatus();
        if (!boolStatus)
          return this.warning('不同状态的流程不能同时操作审查意见');
        let rows = [...this.rows];
        // let projectName = complexArr.map((item) => item.description).join('，');
        let params = {
          rows,
          businessType: '1'
        };
        this.$refs.reviewComment.show(params);
      },
      handleView(row) {
        this.$router.push({
          path: `/project-manage/project-submit/submit-view/${row.businessId}`,
          query: {
            flag: 'center',
            audit: this.activeName !== '1' ? 'audit' : '',
            instanceId: row.id
          }
        });
      },
      handleEdit(row) {
        this.$router.push({
          path: `/project-manage/project-submit/submit-edit/${row.businessId}`,
          query: {
            flag: 'center',
            groupContact: 'contact', // 集团联络人
            instanceId: row.id
          }
        });
      },
      handleDialogClose() {
        this.rows = [];
        this.currentRows = [];
        this.setSelected();
        this.validateVisible = false;
        this.formVisible = false;
        this.errorVisible = false;
        this.addVisible = false;
      }
    }
  };
</script>

<style lang="scss" scoped>
  .el-pagination {
    margin-top: 20px;
  }
</style>
