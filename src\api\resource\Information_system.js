import request from '@/router/axios';

export const getList = (data) => {
  return request({
    url: '/api/system/page',
    method: 'post',
    data
  });
};

export const deleteById = (data) => {
  return request({
    url: '/api/system/delete',
    method: 'post',
    data
  });
};

export const detailById = (id) => {
  return request({
    url: `/api/system/detail?id=${id}`,
    method: 'get'
  });
};

export const save = (data) => {
  return request({
    url: '/api/system/save',
    method: 'post',
    data
  });
};

export const submit = (data) => {
  return request({
    url: '/api/system/submit',
    method: 'post',
    data
  });
};

export const setStatus = (data) => {
  return request({
    url: `/api/system/status`,
    method: 'post',
    data
  });
};

export const serverRoomExport = (data) => {
  return request({
    url: `/api/system/export`,
    method: 'post',
    responseType: 'blob',
    data
  });
};

// 下载模板
export const getExportTemplate = () => {
  return request({
    url: `/api/system/exportTemplate`,
    method: 'get',
    responseType: 'blob'
  });
};

// 根据部门ID查询二级部门 - 公司名称
export const getCompanyName = (deptId) => {
  return request({
    url: `/api/szyk-system/dept/two-dept`,
    method: 'get',
    params: { deptId }
  });
};

// 根据部门ID查询项目
export const getProjectBydeptId = (deptId) => {
  return request({
    url: `/api/szyk-zbusiness/plbase/list/dept`,
    method: 'get',
    params: { deptId }
  });
};

// 获取供应商列表
export const getSupplierList = () => {
  return request({
    url: `/api/supplier/list`,
    method: 'post'
  });
};

// 获取供应商列表
export const batchSubmit = (data) => {
  return request({
    url: `/api/system/batchSubmit`,
    method: 'post',
    data
  });
};

// 获取数据中心机房列表
export const getServerRoomList = (roomStatus) => {
  return request({
    url: `/api/server-room/list`,
    method: 'get',
    params: { roomStatus }
  });
};
