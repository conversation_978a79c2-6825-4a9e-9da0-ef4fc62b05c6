import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/supplier/page',
    method: 'post',
    data: {
      ...params,
      current,
      size
    }
  });
};

export const deleteById = (data) => {
  return request({
    url: '/api/supplier/delete',
    method: 'post',
    data
  });
};

export const detailById = (id) => {
  return request({
    url: `/api/supplier/detail?id=${id}`,
    method: 'get'
  });
};

export const save = (data) => {
  return request({
    url: '/api/supplier/save',
    method: 'post',
    data
  });
};

// 列表
export const getListNoPage = () => {
  return request({
    url: `/api/supplier/list`,
    method: 'post'
  });
};
