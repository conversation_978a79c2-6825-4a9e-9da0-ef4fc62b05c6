<template>
  <div style="width: 100%; height: 100%">
    <div class="department">
      <h3 class="dep_txt">已选部门名单（{{ list.length || 0 }}）</h3>
    </div>
    <div class="tag-wrapper" v-if="list.length > 0">
      <el-tag
        class="tag-style"
        v-for="tag in list"
        :key="tag.id"
        :disable-transitions="false"
        closable
        @close="handleClose(tag)"
      >
        {{ tag.deptName }}
      </el-tag>
    </div>
    <el-empty v-else description="暂无部门" :image-size="80" />
  </div>
</template>

<script>
  export default {
    props: {
      list: {
        type: Array,
        default: function () {
          return [];
        }
      }
    },
    methods: {
      // 删除人员
      handleClose(tag) {
        this.$emit('removeEmit', this.list.indexOf(tag), tag.id);
      }
    }
  };
</script>

<style lang="scss" scoped>
  .department {
    box-sizing: border-box;
    height: 32px;
    margin: 0;
    background: #f8f8f9;

    h3 {
      margin: 0;
      font-weight: 600;
    }

    .dep_txt {
      padding-left: 15px;
      overflow: hidden;
      color: #515a6e;
      font-size: 13px;
      line-height: 30px;
      white-space: normal;
      text-overflow: ellipsis;
      word-break: break-all;
    }
  }

  .tag-style {
    margin: 10px 0 0 10px;
  }

  .tag-wrapper {
    height: calc(100% - 40px);
    overflow: auto;
  }
</style>
