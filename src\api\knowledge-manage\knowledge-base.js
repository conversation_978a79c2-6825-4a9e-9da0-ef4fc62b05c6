import request from '@/router/axios';
// ------ 知识库 -------
// 知识分类树
export const getClassifyTreeList = (data) => {
  return request({
    url: '/api/zbusiness-dict/classify/tree/list',
    method: 'post',
    data
  });
};
// 分类下列表
export const getknowledgeList = (data) => {
  return request({
    url: '/api/zbusiness-knowledge/base/page',
    method: 'post',
    data
  });
};
// 新增、编辑
export const knowledgeSave = (data) => {
  return request({
    url: '/api/zbusiness-knowledge/base/save',
    method: 'post',
    data
  });
};
// 删除
export const knowledgeDel = (data) => {
  return request({
    url: '/api/zbusiness-knowledge/base/deleteByIds',
    method: 'delete',
    data
  });
};
// 详情
export const knowledgeDetail = (id) => {
  return request({
    url: `/api/zbusiness-knowledge/base/fetchById/${id}`,
    method: 'get'
  });
};
// 查询知识分类列表(传入collectType获取手动、自动的列表)
export const getClassifyList = (data) => {
  return request({
    url: `/api/zbusiness-dict/classify/list`,
    method: 'post',
    data
  });
};
// 查询所有部门
export const getAllOrgTreeList = () => {
  return request({
    url: `/api/szyk-system/dept/lazy-list`,
    method: 'get'
  });
};

// 增加点击次数
export const addClickCount = (id) => {
  return request({
    url: `/api/zbusiness-knowledge/base/addClickCount/${id}`,
    method: 'get'
  });
};
// 增加下载次数
export const addDownloadCount = (id) => {
  return request({
    url: `/api/zbusiness-knowledge/base/addDownloadCount/${id}`,
    method: 'get'
  });
};
// 增加阅读次数
export const addReadCount = (id) => {
  return request({
    url: `/api/zbusiness-knowledge/base/addReadCount/${id}`,
    method: 'get'
  });
};
