import request from '@/router/axios';

// 获取上级单位对本单位的锁定状态
export const getSuperiorLockStatus = (targetResource, params) => {
  return request({
    url: `/api/zbusiness-resource/lock/getLockParentView/${targetResource}`,
    method: 'get',
    params
  });
};

// 获取下属组织列表
export const getListData = (params) => {
  return request({
    url: '/api/zbusiness-resource/lock/list',
    method: 'get',
    params
  });
};

// 锁定
export const putLock = (targetResource, data) => {
  return request({
    url: `/api/zbusiness-resource/lock/lock/${targetResource}`,
    method: 'put',
    data
  });
};

// 解锁
export const putUnlock = (targetResource, data) => {
  return request({
    url: `/api/zbusiness-resource/lock/unlock/${targetResource}`,
    method: 'put',
    data
  });
};

// 申请解锁 - 台账
export const putStandingBookApplyUnlock = (targetResource, data) => {
  return request({
    url: `/api/zbusiness-resource/lock/applyUnlockFromStandingBook/${targetResource}`,
    method: 'put',
    data
  });
};

// 申请解锁 - 锁定/解锁页面
export const putApplyUnlock = (targetResource, data) => {
  return request({
    url: `/api/zbusiness-resource/lock/applyUnlock/${targetResource}`,
    method: 'put',
    data
  });
};

// 拒绝解锁
export const putRejectUnlock = (id) => {
  return request({
    url: `/api/zbusiness-resource/lock/refuseUnlockApply/${id}`,
    method: 'put'
  });
};
