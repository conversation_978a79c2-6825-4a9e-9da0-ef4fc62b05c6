import request from '@/router/axios';
// 测试通过
export const testPass = (data) => {
  return request({
    url: `/api/project_progress/audit`,
    method: 'get',
    params: data
  });
};
// 根据建设单位id查询项目基础信息
export const getProjectList = (constructionUnitId) => {
  return request({
    url: `/api/zbusiness-project/plExternal/getPlBase/${constructionUnitId}`,
    method: 'get'
  });
};
// 根据建设单位id查询项目基础信息
export const listByDeptScope = () => {
  return request({
    url: `/api/szyk-zbusiness/plbase/listByDeptScope`,
    method: 'get'
  });
};
// 根据项目id查询项目库信息
export const getProjectInfo = (projectId) => {
  return request({
    url: `/api/zbusiness-project/plExternal/getPlInfo/${projectId}`,
    method: 'get'
  });
};
// 根据部门ID查询二级部门 - 公司名称
export const getCompanyName = (deptId) => {
  return request({
    url: `/api/szyk-system/dept/two-dept`,
    method: 'get',
    params: { deptId }
  });
};
// 列表
export const getPage = (data) => {
  return request({
    url: '/api/project_progress/page',
    method: 'post',
    data
  });
};
// 进度列表
export const getProcessPage = (data) => {
  return request({
    url: '/api/project_progress_new/page',
    method: 'post',
    data
  });
};
// 保存
export const addFetch = (data) => {
  return request({
    url: '/api/project_progress/save',
    method: 'post',
    data
  });
};
// 保存
export const addProcessFetch = (data) => {
  return request({
    url: '/api/project_progress_new/save',
    method: 'post',
    data
  });
};
// 提交
export const submitFetch = (data) => {
  return request({
    url: '/api/project_progress/submit',
    method: 'post',
    data
  });
};
// 提交
export const submitProcessFetch = (data) => {
  return request({
    url: '/api/project_progress_new/submit',
    method: 'post',
    data
  });
};
// 批量提报
export const batchReport = (data) => {
  return request({
    url: '/api/project_progress/report',
    method: 'post',
    data
  });
};
// 批量提报
export const batchProcessReport = (data) => {
  return request({
    url: '/api/project_progress_new/report',
    method: 'post',
    data
  });
};
// 批量提报
export const batchDelete = (data) => {
  return request({
    url: '/api/project_progress/delete',
    method: 'post',
    data
  });
};
// 批量提报
export const batchProcessDelete = (data) => {
  return request({
    url: '/api/project_progress_new/delete',
    method: 'post',
    data
  });
};
// 获取编辑详情
export const getEditDetail = (params) => {
  return request({
    url: `/api/project_progress/detail/edit`,
    method: 'get',
    params
  });
};
// 获取编辑详情
export const getEditProcessDetail = (params) => {
  return request({
    url: `/api/project_progress_new/detail/edit`,
    method: 'get',
    params
  });
};
// 获取详情
export const getDetail = (params) => {
  return request({
    url: `/api/project_progress/detail`,
    method: 'get',
    params
  });
};
// 获取详情
export const getProcessDetail = (params) => {
  return request({
    url: `/api/project_progress_new/detail`,
    method: 'get',
    params
  });
};
// -----------------------

// 项目库导出
export const exportExcel = (params) => {
  return request({
    url: '/api/szyk-zbusiness/plbase/plExport',
    method: 'get',
    responseType: 'blob',
    params
  });
};
// 项目标签分页
export const labelList = (data) => {
  return request({
    url: '/api/dict-label/page',
    method: 'post',
    data
  });
};
// 项目标签分类
export const getLabelClassification = () => {
  return request({
    url: `/api/dict-label-classify/tree/list`,
    method: 'get'
  });
};
// 新增项目标签
export const addLabel = (data) => {
  return request({
    url: '/api/dict-label/save',
    method: 'post',
    data
  });
};

// 为项目新增项目标签-删除标签
export const delProjectLabel = (data) => {
  return request({
    url: `/api/szyk-zbusiness/plbase/deleteLabel/${data.id}`,
    method: 'delete',
    params: data
  });
};
// 上传补充材料
export const uploadSupplement = (data) => {
  return request({
    url: '/api/project_progress/uploadCheckResult',
    method: 'post',
    data
  });
};
// 获取补充材料列表
export const supplementList = (params) => {
  return request({
    url: `/api/project_progress/checkResultList`,
    method: 'get',
    params
  });
};
// 补充材料归档
export const placeOnFile = (params) => {
  return request({
    url: `/api/project_progress/placeOnFile`,
    method: 'get',
    params
  });
};
