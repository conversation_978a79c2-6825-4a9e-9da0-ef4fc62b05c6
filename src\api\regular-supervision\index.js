import request from '@/router/axios';

// 指标库- 列表
export const getList = (params) => {
  return request({
    url: '/api/zbusiness-stat/indicatorlibrary/page',
    method: 'get',
    params
  });
};

// 指标库- 详情
export const getDetail = (params) => {
  return request({
    url: '/api/zbusiness-stat/indicatorlibrary/detail',
    method: 'get',
    params
  });
};

// 批量删除
export const batchDelete = (data) => {
  return request({
    url: '/api/zbusiness-stat/indicatorlibrary/delete',
    method: 'post',
    data
  });
};

// 指标库- 详情
export const indexSystem = (params) => {
  return request({
    url: '/api/zbusiness-stat/indicatorlibrary/systemList',
    method: 'get',
    params
  });
};

// 指标库- 保存编辑
export const indexSave = (data) => {
  return request({
    url: '/api/zbusiness-stat/indicatorlibrary/save',
    method: 'post',
    data
  });
};

// 单位排名- 列表
export const unitRankingList = (params) => {
  return request({
    url: '/api/zbusiness-stat/indicatorreportorg/orgRankPage',
    method: 'get',
    params
  });
};

// 单位排名- 单位的平均分
export const unitRankingAvg = (params) => {
  return request({
    url: '/api/zbusiness-stat/indicatorreportorg/orgAvgRank',
    method: 'get',
    params
  });
};

// 单位排名- 单位的历史得分趋势
export const unitTrend = (params) => {
  return request({
    url: '/api/zbusiness-stat/indicatorreportorg/orgHistoryScore',
    method: 'get',
    params
  });
};

// 单位排名- 系统明细
export const systemDetail = (params) => {
  return request({
    url: '/api/zbusiness-stat/indicatorreportorg/systemDetailList',
    method: 'get',
    params
  });
};

// 系统排名- 列表
export const systemRankingList = (params) => {
  return request({
    url: '/api/zbusiness-stat/indicatorreportsystem/systemRankPage',
    method: 'get',
    params
  });
};

// 系统排名- 平均分
export const systemRankingAvg = (params) => {
  return request({
    url: '/api/zbusiness-stat/indicatorreportsystem/systemAvgRank',
    method: 'get',
    params
  });
};

// 系统排名- 单位的历史得分趋势
export const systemTrend = (params) => {
  return request({
    url: '/api/zbusiness-stat/indicatorreportsystem/systemHistoryScore',
    method: 'get',
    params
  });
};

// 系统排名- 系统明细
export const systemRankingDetail = (params) => {
  return request({
    url: '/api/zbusiness-stat/indicatorreportsystem/orgDetailList',
    method: 'get',
    params
  });
};

// 指标明细
export const indicatorDetailList = (params) => {
  return request({
    url: '/api/zbusiness-stat/indicatordataset/indicatorDetailList',
    method: 'get',
    params
  });
};

// 指标明细-单位
export const unitIndicatorDetailList = (params) => {
  return request({
    url: '/api/zbusiness-stat/indicatordataset/orgRankIndicatorDetailList',
    method: 'get',
    params
  });
};

// 指标系统树形查询
export const systemTree = (params) => {
  return request({
    url: '/api/zbusiness-stat/indicatorlibrary/systemTree',
    method: 'get',
    params
  });
};

// 根据系统编号查询模块列表
export const indexModule = (params) => {
  return request({
    url: '/api/zbusiness-stat/indicatorlibrary/listModuleBySystemCode',
    method: 'get',
    params
  });
};

// 获取所有的 二级单位 [指标库使用]
export const secondaryUnit = (params) => {
  return request({
    url: '/api/szyk-system/dept/getAllSecondDeptList',
    method: 'get',
    params
  });
};

// -----------------------------分割--------
// 设备台账分类
export const getLedgerList = (params) => {
  return request({
    url: '/api/equipment-classify/tree/list',
    method: 'get',
    params
  });
};
// 设备台账分类- 保存编辑
export const ledgerSave = (data) => {
  return request({
    url: '/api/equipment-classify/save',
    method: 'post',
    data
  });
};
// 设备台账分类- 删除
export const ledgerDel = (data) => {
  return request({
    url: '/api/equipment-classify/delete',
    method: 'post',
    data
  });
};
// 设备台账分类 扩展属性
export const getExpandList = (params) => {
  return request({
    url: '/api/equipment-classify/attr/detail',
    method: 'get',
    params
  });
};
// 设备台账自定义属性- 保存编辑
export const ledgerExpandSave = (data) => {
  return request({
    url: '/api/equipment-classify/attr/save',
    method: 'post',
    data
  });
};
// 根据属性ID和待选值判断是否被选中(拓展属性)
export const attributeCheck = (params) => {
  return request({
    url: '/api/equipment-classify/attr/check',
    method: 'get',
    params
  });
};
// end

export const getComputerList = (params) => {
  return request({
    url: '/api/server-room/list',
    method: 'get',
    params
  });
};
// 供应商
export const getSupplierList = (data) => {
  return request({
    url: '/api/supplier/list',
    method: 'post',
    data
  });
};
export const getProjectList = (params) => {
  return request({
    url: '/api/szyk-zbusiness/plbase/list/dept',
    method: 'get',
    params
  });
};
export const equipmentSave = (row) => {
  return request({
    url: '/api/equipment/save',
    method: 'post',
    data: row
  });
};

// 停用-启用
export const setStatus = (data) => {
  return request({
    url: '/api/equipment/status',
    method: 'post',
    data
  });
};
// 提交
export const submitFetch = (data) => {
  return request({
    url: '/api/equipment/submit',
    method: 'post',
    data
  });
};

// 编辑设备台账分类-下级分类接口
export const getNextClassify = (params) => {
  return request({
    url: '/api/equipment-classify/tree/list/code',
    method: 'get',
    params
  });
};

// 导出
export const exportExcel = (data) => {
  return request({
    url: '/api/equipment/export',
    method: 'post',
    responseType: 'blob',
    data
  });
};
// 设备台账 导出模板
export const exportTemplateExcel = (data) => {
  return request({
    url: '/api/equipment/exportTemplate',
    method: 'post',
    responseType: 'blob',
    data
  });
};
// 导入设备台账
export const ImportExcel = (data) => {
  return request({
    url: '/api/equipment/import-equipment',
    method: 'post',
    data
  });
};
// 设备台账分类-懒加载
export const getRecordClassify = (params) => {
  return request({
    url: '/api/equipment-classify/lazy-tree',
    method: 'get',
    params
  });
};
// 批量提交设备台账
export const batchSubmit = (data) => {
  return request({
    url: '/api/equipment/batchSubmit',
    method: 'post',
    data
  });
};
// 设备台账分类-子级的懒加载树形结构
export const getSubDeviceLazyList = (params) => {
  return request({
    url: '/api/equipment/childrenLazyTree',
    method: 'get',
    params
  });
};
// 设备台账分类-子级的树形结构
export const getSubDeviceList = (params) => {
  return request({
    url: '/api/equipment/childrenTree',
    method: 'get',
    params
  });
};

// 日志查询
export const getLogList = (data) => {
  return request({
    url: '/api/zbusiness-stat/indicatorlog/loginLog',
    method: 'post',
    data
  });
};
