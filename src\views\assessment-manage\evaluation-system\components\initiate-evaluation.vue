<template>
  <Dialog
    v-if="visible"
    :visible="visible"
    :title="title"
    width="1100px"
    @closed="hide"
  >
    <el-form
      label-suffix=":"
      ref="form"
      :rules="rules"
      label-width="120px"
      :model="form"
    >
      <el-row>
        <!-- <el-col :span="12">
          <el-form-item label="体系编号">{{
            editSelected.schemeNo || '--'
          }}</el-form-item>
        </el-col> -->
        <el-col :span="12">
          <el-form-item label="体系名称">{{
            editSelected.schemeName || '--'
          }}</el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="所属组织">{{
            editSelected.initiateOrgName || '--'
          }}</el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="被考核组织" prop="assessedOrgId">
            <!-- {{ editSelected.assessedOrgName || '--' }} -->
            <!-- <el-select
              v-model="form.assessedOrgId"
              filterable
              multiple
              ref="assessedOrg"
              placeholder="请选择，多选"
              style="width: 100%"
            >
              <el-option
                v-for="dict in assessedList"
                :key="dict.id"
                :label="dict.deptName"
                :value="dict.id"
              ></el-option>
            </el-select> -->
            <InputTree
              v-model="form.assessedOrgId"
              lazy
              :form="form"
              multiple
              :samePort="true"
              :dic="deptData"
              style="width: 100%"
              :props="{
                label: 'title',
                value: 'id',
                isLeaf: 'isLeaf',
                formLabel: 'assessedOrgName',
                formValue: 'assessedOrgId'
              }"
              :load="lazyLoad"
              :lazyLoading="lazyLoading"
              @search="lazySearch"
            ></InputTree>
            <!-- <department
              v-model="form.assessedOrgId"
              btnTitle="组织选择"
              modelTitle="组织选择"
            />
            <div>
              <el-tag
                v-for="(tag, index) in form.assessedOrgId"
                :key="tag.id"
                closable
                style="margin-top: 10px; margin-right: 10px"
                @close="closeDept(index)"
              >
                {{ tag.deptName }}
              </el-tag>
            </div> -->
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="考核周期" prop="periodName">
            <el-input
              placeholder="请选择考核周期"
              v-model="form.periodName"
              @focus="dispatchHandle"
              suffix-icon="el-icon-arrow-down"
            >
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="自评截止日期" prop="selfAssessmentDeadline">
            <el-date-picker
              style="width: 100%"
              value-format="yyyy-MM-dd"
              v-model="form.selfAssessmentDeadline"
              type="date"
              :clearable="false"
              :picker-options="pickerOptions"
              placeholder="请选择"
            >
            </el-date-picker
          ></el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="考核日期" prop="rwDate">
            <el-date-picker
              style="width: 100%"
              value-format="yyyy-MM-dd"
              v-model="form.rwDate"
              type="date"
              :clearable="false"
              placeholder="请选择"
            >
            </el-date-picker
          ></el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="考核小组人员" prop="teamMembers">
            <el-input
              v-model="form.teamMembers"
              placeholder="请输入"
              :maxlength="100"
              type="textarea"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="现场考核人员" prop="sceneMembers">
            <el-input
              v-model="form.sceneMembers"
              placeholder="请输入"
              :maxlength="100"
              type="textarea"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="备注" prop="remark">
            <el-input
              v-model="form.remark"
              placeholder="请输入200字以内备注，如没有可填写无"
              :maxlength="200"
              type="textarea"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="" prop="workPlanFileIds">
            <h-upload
              ref="upload"
              v-model="form.workPlanFileIds"
              showFileList
              show-loading
              multiple
            >
              <el-button type="primary" icon="el-icon-upload"
                >上传工作方案
              </el-button>
            </h-upload>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <div slot="footer">
      <el-button type="primary" plain @click="hide">取消</el-button>
      <el-button type="primary" @click="onSubmit">确定 </el-button>
    </div>
    <AddAssessmentCycle
      ref="cycle"
      @saveSuccess="saveSuccess"
    ></AddAssessmentCycle>
  </Dialog>
</template>
<script>
  import Dialog from '@/components/basic-dialog';
  import AddAssessmentCycle from './add-assessment-cycle.vue';
  import Department from './department';

  import {
    getCyclePage,
    getBelowDepartment,
    getCurrentChild
  } from '@/api/assess-manage';
  import { InputTree } from '@/components/yk-select-tree';
  import { mapGetters } from 'vuex';
  import { getStore } from '@/util/store';
  const form = {
    selfAssessmentDeadline: undefined,
    rwDate: undefined,
    remark: undefined,
    rwPeriodDetailId: undefined,
    periodName: undefined,
    sceneMembers: undefined,
    teamMembers: undefined,
    assessedOrgId: [],
    workPlanFileIds: []
  };
  export default {
    serviceDicts: ['period_type'],
    components: { Dialog, AddAssessmentCycle, InputTree, Department },
    props: {
      deptTree: { type: Array, require: () => [] },
      categoryTree: { type: Array, require: () => [] }
    },
    data() {
      return {
        visible: false,
        loading: false,
        title: '发起考核',
        // ------------
        editSelected: {}, // 已选中编辑过的列表
        form: { ...form },
        cycleOptions: [],
        assessedList: [],
        rules: {
          assessedOrgId: [
            {
              required: true,
              type: 'array',
              message: '请选择被考核组织',
              trigger: ['change']
            }
          ],
          periodName: [
            {
              required: true,
              message: '请选择考核周期',
              trigger: ['change']
            }
          ],
          selfAssessmentDeadline: [
            {
              required: true,
              message: '请选择自评截止日期',
              trigger: ['change']
            }
          ],
          rwDate: [
            {
              required: true,
              message: '请选择考核日期',
              trigger: ['change']
            }
          ]
        },
        deptData: [],
        node: null,
        resolveFunc: null,
        lazyLoading: false
      };
    },
    computed: {
      ...mapGetters(['userInfo']),
      pickerOptions() {
        return {
          disabledDate(time) {
            return time.getTime() < Date.now() - 24 * 60 * 60 * 1000;
          }
        };
      },
      currentBusinessScope() {
        let org = getStore({ name: 'current-organization' });
        return org || {};
      }
    },
    methods: {
      closeDept(idx) {
        this.form.assessedOrgId.splice(idx, 1);
      },
      async lazySearch(title) {
        if (!title) {
          this.node.childNodes = [];
          this.lazyLoad(this.node, this.resolveFunc);
          return;
        }
        this.lazyLoad(this.node, this.resolveFunc, title);
      },
      async lazyLoad(node, resolve, title) {
        const { data } = node;
        const { id } = data || {};
        if (node.level === 0) {
          this.node = node;
          this.resolveFunc = resolve;
        }
        let parentId = this.currentBusinessScope['id'];
        let params = {
          tenantId: '000000',
          parentId: title ? parentId : id || parentId,
          title: title || undefined
        };
        this.lazyLoading = true;
        try {
          const {
            data: { data: list }
          } = await getCurrentChild(params);
          // 通过调用resolve将子节点数据返回，通知组件数据加载完成
          let arr = list || [];
          arr.forEach((item) => (item.isLeaf = !item.hasChildren));
          if (title) {
            this.deptData = arr;
          } else {
            resolve(arr);
          }
        } catch (error) {
          console.log(error);
        } finally {
          this.lazyLoading = false;
        }
      },
      // 弹窗确定
      saveSuccess(row) {
        this.form.rwPeriodDetailId = row.id;
        this.form.periodName = row.periodName;
      },
      dispatchHandle() {
        let { initiateOrgId } = this.editSelected;
        this.$refs.cycle.show({ ...this.form, initiateOrgId });
      },
      validForm() {
        let bool = false;
        this.$refs.form.validate((val) => {
          bool = val;
        });
        return bool;
      },
      // 提交
      onSubmit() {
        let bool = this.validForm();
        if (!bool) return;
        let { id } = this.editSelected;
        let assessedOrgId = this.form.assessedOrgId.map((item) => item).join();
        let workPlanFileIds = this.form.workPlanFileIds.map(
          (item) => item.attachId || item.id
        );
        this.$emit('save-success', {
          ...this.form,
          id,
          assessedOrgId,
          workPlanFileIds
        });
        this.hide();
      },
      async show(row = {}) {
        this.visible = true;
        this.editSelected = row;
        this.requestFirstPeriod();
        // this.requestAssessedOrganization();
      },
      // 被考核组织organization
      async requestAssessedOrganization() {
        this.loading = true;
        const params = {
          deptId: this.editSelected.initiateOrgId
        };
        try {
          const {
            data: { data }
          } = await getBelowDepartment(params);
          let arr = data || [];
          this.assessedList = arr;
        } catch (e) {
          console.error(e);
        } finally {
          this.loading = false;
        }
      },
      async requestFirstPeriod() {
        this.loading = true;
        const params = {
          // periodType: '4',
          deptId: this.editSelected.initiateOrgId,
          periodDetailStatus: '0',
          current: 1,
          size: 1
        };
        try {
          const {
            data: { data }
          } = await getCyclePage(params);
          const { records = [] } = data || {};
          let arr = records || [];
          this.form.rwPeriodDetailId = arr.length ? arr[0].id : undefined;
          this.form.periodName = arr.length ? arr[0].periodName : undefined;
        } catch (e) {
          console.error(e);
        } finally {
          this.loading = false;
        }
      },
      hide() {
        this.editSelected = {};
        this.form = { ...form };
        this.visible = false;
      }
    }
  };
</script>
