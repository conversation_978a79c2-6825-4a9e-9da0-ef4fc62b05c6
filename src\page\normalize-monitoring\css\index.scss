.monitoring-container {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  height: 100%;
  color: #04edf9;
  background-image: url('./imgs/bg.png');
  background-size: cover;

  .monitoring-top {
    display: grid;
    grid-template-columns: 1fr 2fr 1fr;
    width: 100%;
    height: 7%;
    font-weight: 700;
    font-size: 1.2rem;
    font-style: normal;

    // background-color: #004c99;
    background: url("./imgs/top-2.png") no-repeat center center;

    span{
      display: grid;
      align-items: center;
      justify-content: center;
    }

    span:first-child {
      // background-color: #004c99;
    }

    span:last-child {
      // background-color: #004c99;
    }

    span:nth-child(2) {
      color: #FFF;
      font-weight: 700;
      font-size: 1.5rem;
      font-style: normal;

      // background: #082247;
      // background-size: 10px 10px;
      // transform: scaleY(1.1) perspective(1.5em) rotateX(2deg);
      // transform-origin: bottom;
    }
  }

  .normalize-content{
    display: grid;
    grid-template-columns: 1fr 2fr 1fr;
    width: 100%;
    height: 93%;
padding: 20px;
padding-top: 5px;

    &-left{
      // background-color: antiquewhite;
    }

    &-center{
      // background-color: antiquewhite;
    }

    &-right{
      // background-color: antiquewhite;
    }
  }
}
