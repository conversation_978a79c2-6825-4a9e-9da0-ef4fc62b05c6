import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/zbusiness-person/personalbase/dataPage',
    method: 'get',
    params: {
      ...params,
      current,
      size
    }
  });
};
// 批量 添加分页查询
export const getBatchList = (params) => {
  return request({
    url: '/api/zbusiness-person/personalbase/batchAddPage',
    method: 'get',
    params
  });
};
// 获取当前用户所在单位的部门树
export const getNowDeptTree = (params) => {
  return request({
    url: '/api/szyk-system/dept/getNowDeptTree',
    method: 'get',
    params
  });
};
// 获取当前用户所在单位的部门树
export const getUnitDeptTree = (params) => {
  return request({
    url: '/api/szyk-system/dept/getUnitDeptTree',
    method: 'get',
    params
  });
};
// 获取当前用户所在单位的部门列表
export const getNowDept = (params) => {
  return request({
    url: '/api/szyk-system/dept/getNowDept',
    method: 'get',
    params
  });
};

export const deleteById = (idList) => {
  return request({
    url: '/api/zbusiness-person/personalbase/deleteById',
    method: 'put',
    params: {
      idList
    }
  });
};

export const detailById = (id) => {
  return request({
    url: `/api/zbusiness-person/personalbase/fetchById/${id}`,
    method: 'get'
  });
};

export const save = (data) => {
  return request({
    url: '/api/zbusiness-person/personalbase/save',
    method: 'post',
    data
  });
};
// 批量保存
export const batchAdd = (data, manageOrgId) => {
  return request({
    url: `/api/zbusiness-person/personalbase/batchAdd?manageOrgId=${manageOrgId}`,
    method: 'post',
    data
  });
};

export const getSuperiorDept = (deptId) => {
  return request({
    url: `/api/zbusiness-person/personalbase/getSuperiorDept/${deptId}`,
    method: 'get'
  });
};

export const personExport = (data) => {
  return request({
    url: `/api/zbusiness-person/personalbase/personExport`,
    method: 'post',
    responseType: 'blob',
    data
  });
};

export const treeIncludePersonCount = (params) => {
  return request({
    url: `/api/zbusiness-person/personalbase/treeIncludePersonCount`,
    method: 'GET',
    params
  });
};

// export const getHtmlData = () => {
//   return request({
//     url: `/getHtml/onlinePreview?url=aHR0cDovLzEyMy41Ny4yNDEuMTYxOjEzMTAxL2ltYWdlcy8yMDIyMTEvMjAvNWViMjk1MjgtMjY0OC00YjI5LThhZGUtYTNkZmQ2NzBiMzRjLmRvYw%3D%3D`,
//     method: 'get'
//   });
// };

// 根据部门ID查询二级部门 - 公司名称
export const getCompanyName = (deptId) => {
  return request({
    url: `/api/szyk-system/dept/two-dept`,
    method: 'get',
    params: { deptId }
  });
};
