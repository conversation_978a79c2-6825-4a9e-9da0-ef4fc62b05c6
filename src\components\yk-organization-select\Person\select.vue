<template>
  <div class="tree_box">
    <div class="department">
      <h3 class="dep_txt">已选人员名单（{{ list.length || 0 }}）</h3>
    </div>
    <div v-loading="loading">
      <div class="tag-wrapper" v-if="list.length > 0">
        <div v-for="tag in list" :key="tag.id">
          <el-tooltip
            effect="dark"
            placement="top-start"
            :content="tag.ancestorName + '-' + tag.deptName || ''"
          >
            <el-tag
              class="tag-style"
              closable
              :disable-transitions="false"
              @close="handleClose(tag)"
            >
              {{ tag.realName }} 【{{ tag.deptName }}】
            </el-tag>
          </el-tooltip>
        </div>
      </div>
      <el-empty v-else description="暂无人员" :image-size="80" />
    </div>
  </div>
</template>

<script>
  export default {
    name: 'SelectPeople',
    props: {
      list: {
        type: Array,
        default: function () {
          return [];
        }
      },
      loading: {
        type: Boolean,
        default: false
      }
    },
    methods: {
      // 删除人员
      handleClose(tag) {
        this.$emit('removeEmit', this.list.indexOf(tag), tag.id, tag.deptId);
      }
    }
  };
</script>

<style lang="scss" scoped>
  .tree_box {
    width: 100%;
    height: 420px !important;
  }

  .department {
    box-sizing: border-box;
    height: 32px;
    margin: 0;
    background: #f8f8f9;
    border-bottom: 1px solid #dfe6ec;

    h3 {
      margin: 0;
      font-weight: 600;
    }

    .dep_txt {
      padding-left: 15px;
      overflow: hidden;
      color: #515a6e;
      font-size: 13px;
      line-height: 30px;
      white-space: normal;
      text-overflow: ellipsis;
      word-break: break-all;
    }
  }

  .tag-style {
    margin: 10px 0 0 10px;
  }

  .tag-wrapper {
    height: 410px;
    overflow: auto;
  }
</style>
