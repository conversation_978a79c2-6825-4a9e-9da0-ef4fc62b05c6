<template>
  <div :id="id" class="chart" :style="{ height: height, width: width }" />
</template>

<script>
  import tdTheme from './theme.json'; // 引入默认主题
  import resizeMixins from '@/util/resizeMixins';
  import { mapGetters } from 'vuex';

  export default {
    name: 'echart',
    mixins: [resizeMixins],
    props: {
      id: {
        type: String,
        default: 'chart'
      },
      width: {
        type: String,
        default: '100%'
      },
      height: {
        type: String,
        default: '100%'
      },
      options: {
        type: Object,
        default: () => ({})
      }
    },
    data() {
      return {
        chart: null
      };
    },
    watch: {
      options: {
        handler(options) {
          // 设置true清空echart缓存
          this.chart.setOption(options, true);
        },
        deep: true
      }
    },
    mounted() {
      window.echarts.registerTheme('tdTheme', tdTheme); // 覆盖默认主题
      this.initChart();
    },
    methods: {
      initChart() {
        // 初始化echart
        this.chart = window.echarts.init(this.$el, 'tdTheme');
        this.chart.setOption(this.options, true);
        this.$store.commit('ADD_COCKPIT_CHART', this.chart);
      }
    },
    computed: {
      ...mapGetters(['cockpitChartsList'])
    }
  };
</script>

<style lang="scss" scoped></style>
