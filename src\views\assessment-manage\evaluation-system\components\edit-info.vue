<template>
  <div>
    <!-- v-if="formInfo.constructionUnitId" v-if="(isEdit && !id) || formInfo.schemeNo" -->
    <el-form
      ref="form"
      :rules="rules"
      :model="formInfo"
      label-width="120px"
      label-position="right"
      label-suffix="："
    >
      <el-row>
        <el-col>
          <el-form-item label="体系名称" prop="schemeName">
            <el-input
              v-model="formInfo.schemeName"
              placeholder="请输入，50字符以内"
              clearable
              :maxlength="50"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <!-- <el-col :span="8"
          ><el-form-item label="体系编号" prop="schemeNo">
            {{ formInfo.schemeNo || '--' }}</el-form-item
          ></el-col
        >
        <el-col :span="8"></el-col> -->
        <el-col :span="8"
          ><el-form-item label="发起组织" prop="initiateOrgId">
            <el-select
              v-model="formInfo.initiateOrgId"
              filterable
              :disabled="!!(isEdit && id)"
              ref="company"
              placeholder="根据用户默认，可修改"
              style="width: 100%"
              @change="handleConstructe"
            >
              <el-option
                v-for="dict in deliveryList"
                :key="dict.id"
                :label="dict.deptName"
                :value="dict.id"
              ></el-option>
            </el-select> </el-form-item
        ></el-col>
        <!-- </el-row>
      <el-row> -->
        <!-- <el-col :span="8"
          ><el-form-item label="被考核组织" prop="assessedOrgId"> -->
        <!-- <el-select
              v-model="formInfo.assessedOrgId"
              filterable
              multiple
              ref="assessedOrg"
              placeholder="请选择，多选"
              style="width: 100%"
            >
              <el-option
                v-for="dict in lowerDepartment"
                :key="dict.id"
                :label="dict.deptName"
                :value="dict.id"
              ></el-option>
            </el-select> -->
        <!-- <InputTree
              v-model="formInfo.assessedOrgId"
              lazy
              multiple
              :form="formInfo"
              ref="inputTree"
              :dic="deptData"
              clearable
              style="width: 100%"
              :props="{
                label: 'title',
                value: 'id',
                isLeaf: 'isLeaf',
                formLabel: 'assessedOrgName',
                formValue: 'assessedOrgId'
              }"
              :load="lazyLoad"
              :lazyLoading="lazyLoading"
              @search="lazySearch"
            ></InputTree> </el-form-item
        ></el-col> -->
        <el-col :span="8"
          ><el-form-item label="状态" prop="schemeStatus">
            <!-- <el-checkbox
              :disabled="!!(isEdit && id)"
              true-label="1"
              false-label="0"
              v-model="formInfo.schemeStatus"
            ></el-checkbox> -->
            <el-switch
              v-model="formInfo.schemeStatus"
              active-value="0"
              inactive-value="1"
              active-text="启用"
              inactive-text="停用"
            >
            </el-switch> </el-form-item
        ></el-col>
        <el-col :span="8"
          ><el-form-item label="总分" prop="totalScore">
            {{
              [undefined, '', null].includes(formInfo.totalScore)
                ? '--'
                : formInfo.totalScore
            }}</el-form-item
          ></el-col
        >

        <!-- <el-col :span="8"
          ><el-form-item label="创建人">
            {{ formInfo.createUserName || '--' }}
          </el-form-item></el-col
        >
      </el-row>
      <el-row>
        <el-col :span="8"
          ><el-form-item label="创建时间">
            {{ formInfo.createTime || '--' }}</el-form-item
          ></el-col
        >
      </el-row>
      <el-row>-->
        <el-col :span="24"
          ><el-form-item label="备注" prop="remark">
            <el-input
              v-model="formInfo.remark"
              :disabled="!isEdit"
              placeholder="请输入，如没有可填写无"
              type="textarea"
              :maxlength="200"
              clearable
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>
<script>
  import { formatTreeData } from '@/util/util';
  import { getDeptTree, getLazyDeptTree_3 } from '@/api/system/dept';
  import { getDeliveryList } from '@/api/project-dispatch';
  import { mapGetters } from 'vuex';
  import { InputTree } from '@/components/yk-select-tree';
  export default {
    components: {
      InputTree
    },
    name: 'editInfo',
    props: {
      isEdit: { type: Boolean, default: false },
      id: { type: String, default: '' },
      formInfo: {
        type: Object,
        default: () => {}
      },
      lowerDepartment: {
        type: Array,
        default: () => []
      },
      formMember: {
        type: Object,
        default: () => {}
      }
    },
    data() {
      return {
        rules: {
          schemeName: [
            {
              required: true,
              message: '请输入体系名称',
              trigger: ['blur']
            }
          ],
          initiateOrgId: [
            {
              required: true,
              message: '请选择发起组织',
              trigger: ['change']
            }
          ]
          // assessedOrgId: [
          //   {
          //     required: true,
          //     message: '请选择被考核组织',
          //     trigger: ['change']
          //   }
          // ]
        },
        constructionList: [],
        deliveryList: [],
        deptData: [],
        node: null,
        resolveFunc: null,
        lazyLoading: false
      };
    },
    computed: {
      ...mapGetters(['permission', 'userInfo'])
    },
    mounted() {
      this.getDeliveryList();
    },
    methods: {
      async lazySearch(title) {
        if (!title) {
          this.node.childNodes = [];
          let val = this.formInfo.initiateOrgId;
          let node = { data: { id: val } };
          this.lazyLoad(node, this.resolveFunc);
          return;
        }
        this.lazyLoad(this.node, this.resolveFunc, title);
      },
      changelazyLoad(node) {
        this.node.childNodes = [];
        this.lazyLoad(node, this.resolveFunc);
      },
      async lazyLoad(node, resolve, title) {
        const { data } = node;
        const { id } = data || {};
        if (node.level === 0) {
          this.node = node;
          this.resolveFunc = resolve;
        }
        // if (!id) return;
        let params = {
          tenantId: '000000',
          parentId: title ? undefined : id || 0,
          title: title || undefined
        };
        this.lazyLoading = true;
        try {
          const {
            data: { data: list }
          } = await getLazyDeptTree_3(params);
          // 通过调用resolve将子节点数据返回，通知组件数据加载完成
          if (title) {
            this.deptData = list || [];
          } else {
            resolve(list || []);
            // this.$refs.inputTree._initData(this.formInfo.assessedOrgId);
          }
        } catch (error) {
          console.log(error);
        } finally {
          this.lazyLoading = false;
        }
      },
      async getDeliveryList() {
        try {
          const Func = getDeliveryList;
          const {
            data: { data }
          } = await Func({ userId: this.userInfo['user_id'] });
          // sendCompanyName
          this.deliveryList = data || [];
          // 初始化当前用户所属组织
          if (!this.id && this.isEdit) {
            this.formInfo.initiateOrgId = this.deliveryList[0].id;
            this.formInfo.initiateOrgName = this.deliveryList[0].deptName;
            this.formInfo.oldinitiateOrgId = this.deliveryList[0].id;
            this.formInfo.oldinitiateOrgName = this.deliveryList[0].deptName;
          }
          // this.handleConstructe();
          // this.isEdit && !this.id && this.$emit('setsolution');
        } catch (e) {
          console.log(e);
        }
      },
      validForm() {
        let bool = false;
        this.$refs.form.validate((valid) => {
          bool = valid;
        });
        return bool;
      },
      setcompanyName() {
        let id = this.formInfo.initiateOrgId;
        let obj = this.deliveryList.find((item) => item.id === id);
        this.formInfo.initiateOrgName = obj ? obj.deptName : '';
        this.formInfo.oldinitiateOrgName = obj ? obj.deptName : '';
        this.formInfo.oldinitiateOrgId = id;
        // 重置
        this.formInfo.assessedOrgId = [];
      },
      setChangeOrg() {
        this.setcompanyName();
        this.$emit('setsolution');
      },
      // 建设单位 选择
      async handleConstructe() {
        if (!this.formMember.stageList.length) {
          this.setcompanyName();
          return;
        }
        // debugger;
        this.$confirm('此操作将删除指标配置, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.setChangeOrg();
            this.$refs.company.blur();
          })
          .catch(() => {
            let { oldinitiateOrgId, oldinitiateOrgName } = this.formInfo;
            this.formInfo.initiateOrgId = oldinitiateOrgId;
            this.formInfo.oldinitiateOrgName = oldinitiateOrgName;
            this.$refs.company.blur();
          });
      },
      async getDept() {
        try {
          const {
            data: { data }
          } = await getDeptTree();
          this.constructionList = formatTreeData(data, {
            label: 'title',
            value: 'id'
          });
          this.initGetConstrutionName();
        } catch (e) {
          console.error(e);
        }
      },
      initGetConstrutionName() {
        if (!this.constructionList.length) return;
        let id = this.formInfo.initiateOrgId;
        const row = this.treeFind(
          this.constructionList,
          (row) => row.value === id
        );
        this.formInfo.initiateOrgName = row ? row.label : '';
        this.formInfo.oldinitiateOrgName = row ? row.label : '';
      },
      treeFind(tree, func) {
        for (const data of tree) {
          if (func(data)) return data;
          if (data.children) {
            const res = this.treeFind(data.children, func);
            if (res) return res;
          }
        }
        return null;
      }
    }
  };
</script>
<style lang=""></style>
