import request from '@/router/axios';
// 统谈分签 采购需求 列表
export const getRequirementList = (data) => {
  return request({
    url: '/api/unifiedRequirement/page',
    method: 'post',
    data
  });
};
// 统谈分签 采购需求 删除
export const requirementDel = (data) => {
  return request({
    url: '/api/unifiedRequirement/delete',
    method: 'post',
    data
  });
};
// 历史需求记录
export const getHistorical = (data) => {
  return request({
    url: `/api/unifiedRequirement/historyPage`,
    method: 'post',
    data
  });
};
// 需求登记
export const registry = (data) => {
  return request({
    url: '/api/unifiedRequirement/registry',
    method: 'post',
    data
  });
};

// 获取数据status
export const getStatusList = (params) => {
  return request({
    url: `/api/unifiedCatalog/getCategoryDict`,
    method: 'get',
    params
  });
};
// 根据ID获取数据 需求登记
export const getRegistry = (params) => {
  return request({
    url: `/api/unifiedRequirement/detail`,
    method: 'get',
    params
  });
};
// 采购需求 提交
export const submit = (data) => {
  return request({
    url: '/api/unifiedRequirement/batchSubmit',
    method: 'post',
    data
  });
};
// 采购需求 提交
export const lock = (data) => {
  return request({
    url: '/api/unifiedRequirement/batchLock',
    method: 'post',
    data
  });
};
// 采购需求 完成
export const complete = (data) => {
  return request({
    url: '/api/unifiedRequirement/complete',
    method: 'post',
    data
  });
};
// 采购需求 撤回
export const recall = (data) => {
  return request({
    url: '/api/unifiedRequirement/recall',
    method: 'post',
    data
  });
};
// 统谈分签 分页
export const getSignList = (params) => {
  return request({
    url: '/api/unifiedCatalog/page',
    method: 'get',
    params
  });
};
// 统谈分签 保存
export const save = (data) => {
  return request({
    url: '/api/unifiedCatalog/save',
    method: 'post',
    data
  });
};
// 统谈分签 根据ID获取数据
export const detailById = (params) => {
  return request({
    url: '/api/unifiedCatalog/fetchById',
    method: 'get',
    params
  });
};
// 统谈分签
export const deleteById = (data) => {
  return request({
    url: `/api/unifiedCatalog/deleteByIds`,
    method: 'post',
    data
  });
};
// 统谈分签 修改状态
export const setStatus = (data) => {
  return request({
    url: `/api/unifiedCatalog/changeStatus`,
    method: 'post',
    data
  });
};
// 校验目录名称
export const checkCatalogName = (params) => {
  return request({
    url: '/api/unifiedCatalog/checkCatalogName',
    method: 'get',
    params
  });
};
// 校验明细名称
export const checkDetailName = (params) => {
  return request({
    url: '/api/unifiedCatalog/checkDetailName',
    method: 'get',
    params
  });
};
// 目录明细 导出模板
export const exportTemplateExcel = (data) => {
  return request({
    url: '/api/unifiedCatalog/exportTemplate',
    method: 'post',
    responseType: 'blob',
    data
  });
};
// 导入目录明细
export const ImportExcel = (data) => {
  return request({
    url: '/api/unifiedCatalog/importDetail',
    method: 'post',
    data
  });
};
// 目录明细 导出 打包 zip
export const exportTemplatePackageZip = (data) => {
  return request({
    url: '/api/unifiedCatalog/exportDetailZip',
    method: 'post',
    responseType: 'blob',
    data
  });
};
// 目录明细 导出 单个 zip
export const exportTemplateZip = (data) => {
  return request({
    url: '/api/unifiedCatalog/exportZip',
    method: 'post',
    responseType: 'blob',
    data
  });
};
