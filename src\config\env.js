// 阿里矢量图标库配置
// let iconfontVersion = ['567566_pwc3oottzol'];
let iconfontVersion = [
  '567566_pwc3oottzol',
  '1066523_6bvkeuqao36',
  '2059706_ppk64uqnyme'
];
let iconfontUrl = `//at.alicdn.com/t/font_$key.css`;

let baseUrl = '';
let imgUrl = '';
let codeUrl = `${baseUrl}/code`;
let wsUrl = '';
let wsBacklogUrl = ''; // 首页待办websocket
// 文件预览
let preUrl = '';
let httpPreviewUrl = '';
let httpsPreviewUrl = '';
let loginBgUrl = 'none'; // 登录背景
const env = process.env.VUE_APP_ENV;
if (env === 'development') {
  baseUrl = ``; // 开发环境地址
  imgUrl = ``;
  wsUrl = 'ws://101.201.140.144:30831/websocket/';
  preUrl = 'http://59.110.212.35:8077/onlinePreview?url=';
  // previewUrl = 'http://10.115.132.254:8701/';
  httpPreviewUrl = 'http://59.110.212.35:9696/';
  httpsPreviewUrl = 'http://59.110.212.35:9696/';
  wsBacklogUrl = 'ws://59.110.225.56:30766/api/websocket/';
  loginBgUrl = '/img/bg/bg-sub.png';
} else if (env === 'production') {
  baseUrl = ``; //生产环境地址
  imgUrl = ``;
  wsUrl = 'ws://101.201.140.144:30831/websocket/';
  preUrl = 'https://xxh.shandong-energy.com/preview/onlinePreview?url=';
  httpPreviewUrl = 'http://172.27.36.179:8701/';
  httpsPreviewUrl = 'https://xxh.shandong-energy.com:8701/';
  wsBacklogUrl = 'ws://xxh.shandong-energy.com/api/websocket/';
  loginBgUrl = '/img/bg/bg-sub2.png';
} else if (env === 'test') {
  baseUrl = ``; //测试环境地址
  imgUrl = ``;
  wsUrl = 'ws://101.201.140.144:30831/websocket/';
  preUrl = 'http://59.110.212.35:8077/onlinePreview?url=';
  // previewUrl = 'http://10.115.132.254:8701/';
  httpPreviewUrl = 'http://59.110.212.35:9696/';
  httpsPreviewUrl = 'http://59.110.212.35:9696/';
  // previewUrl = 'http://59.110.225.56:32529/preview/';
  wsBacklogUrl = 'ws://59.110.225.56:32529/api/websocket/';
  loginBgUrl = '/img/bg/bg-sub.png';
} else if (env === 'show') {
  baseUrl = ``; // 演示环境地址
  imgUrl = ``;
  wsUrl = 'ws://101.201.140.144:30831/websocket/';
  preUrl = 'http://172.31.31.108/preview/onlinePreview?url=';
  httpPreviewUrl = 'http://10.115.132.254:8701/preview/';
  httpsPreviewUrl = 'http://10.115.132.254:8701/preview/';
  wsBacklogUrl = 'ws://172.31.31.108/api/websocket/';
  loginBgUrl = '/img/bg/bg-sub2.png';
}
export {
  baseUrl,
  imgUrl,
  iconfontUrl,
  iconfontVersion,
  codeUrl,
  httpPreviewUrl,
  httpsPreviewUrl,
  wsUrl,
  preUrl,
  wsBacklogUrl,
  loginBgUrl,
  env
};
