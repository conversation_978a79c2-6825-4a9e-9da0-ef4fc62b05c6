<template>
  <Dialog
    v-if="visible"
    :visible="visible"
    :title="title"
    width="1100px"
    @closed="hide"
  >
    <el-form
      label-suffix=":"
      ref="queryForm"
      label-width="100px"
      :model="queryForm"
      :rules="rules"
      inline
    >
      <el-form-item label="指标分类" prop="classifyId">
        <el-select
          v-model="queryForm.classifyId"
          filterable
          ref="classifyId"
          placeholder="请选择"
          style="width: 100%"
          @change="changeIndicator"
        >
          <el-option
            v-for="dict in indicatorList"
            :key="dict.id"
            :label="dict.classifyName"
            :value="dict.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="分类分值">
        {{ queryForm.classifyScore || '--' }}
      </el-form-item>
    </el-form>
    <el-form
      ref="form"
      :rules="rules"
      :model="formInfo"
      label-width="200px"
      label-position="right"
      label-suffix="："
    >
      <el-table
        :data="formInfo.memberList"
        border
        stripe
        v-loading="isLoading"
        :header-cell-style="{ backgroundColor: '#fafafa' }"
        style="width: 100%"
      >
        <el-table-column label="序号" type="index" align="center" width="50">
        </el-table-column>
        <el-table-column prop="evaluateTarget" header-align="center">
          <template slot="header">
            <span style="color: #f56c6c">*</span> 评价指标
          </template>
          <template slot-scope="scope">
            <el-form-item
              label-width="0"
              :prop="'memberList.' + scope.$index + '.evaluateTarget'"
              :rules="rules.evaluateTarget"
            >
              <!--  @paste.native.capture.prevent="(e) => handlePaste(e, scope.row)" -->
              <avue-input
                :maxlength="200"
                :minRows="2"
                type="textarea"
                v-model="scope.row.evaluateTarget"
                placeholder="请输入评价指标"
              ></avue-input>
            </el-form-item>
          </template>
        </el-table-column>
        <!-- <el-table-column prop="targetExplain" align="center">
          <template slot="header"> 指标解释 </template>
          <template slot-scope="scope">
            <el-form-item
              label-width="0"
              :prop="'memberList.' + scope.$index + '.targetExplain'"
              :rules="rules.targetExplain"
            >
              <avue-input
                :maxlength="200"
                :minRows="2"
                type="textarea"

                v-model="scope.row.targetExplain"
                placeholder="请输入指标解释"
              ></avue-input>
            </el-form-item>
          </template>
        </el-table-column> -->
        <el-table-column prop="score" width="180" align="center">
          <template slot="header">
            <span style="color: #f56c6c">*</span> 分值
          </template>
          <template slot-scope="scope">
            <el-form-item
              label-width="0"
              :prop="'memberList.' + scope.$index + '.score'"
              :rules="rules.score"
            >
              <avue-input
                v-model="scope.row.score"
                placeholder="请输入分值"
                :readonly="scope.row.isReadonly"
                @focus="scoreFocus(scope.row)"
                @blur="calculateScore"
              ></avue-input>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column prop="rwType" width="180" align="center">
          <template slot="header">
            <span style="color: #f56c6c">*</span>
            考核方式
          </template>
          <template slot-scope="scope">
            <el-form-item
              label-width="0"
              :prop="'memberList.' + scope.$index + '.rwType'"
              :rules="rules.rwType"
            >
              <el-select
                v-model="scope.row.rwType"
                filterable
                placeholder="请选择"
                style="width: 100%"
                @change="(val) => handleRwTypeName(val, scope.row)"
              >
                <el-option
                  v-for="dict in serviceDicts.type['rw_type']"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="100">
          <template slot-scope="scope">
            <el-button
              type="text"
              @click="handleDelete(scope.$index, scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <el-button
        :disabled="!queryForm.classifyId"
        class="dynamic-add-btn"
        @click="handleAdd"
        >新 增</el-button
      >
    </el-form>
    <div slot="footer">
      <el-button type="primary" plain @click="hide">取消</el-button>
      <el-button type="primary" @click="onSubmit">保存 </el-button>
    </div>
  </Dialog>
</template>
<script>
  import Dialog from '@/components/basic-dialog';
  import { validEvaluateRemove } from '@/api/assess-manage';
  import reg from '@/util/regexp';
  import { calculate } from '@/util/util';
  const form = {
    memberList: []
  };
  const queryForm = {
    classifyId: undefined,
    classifyName: '',
    oldclassifyId: undefined,
    oldclassifyName: '',
    classifyScore: '',
    addScore: '' // 0 否
  };
  export default {
    serviceDicts: ['rw_type'],
    components: { Dialog },
    props: {
      indicatorList: { type: Array, require: () => [] }
    },
    data() {
      return {
        visible: false,
        loading: false,
        tableData: [],
        total: 0,
        title: '添加评价指标',
        formInfo: { ...form },
        rules: {
          classifyId: [{ required: true, message: '请选择指标分类' }],
          evaluateTarget: [{ required: true, message: '请输入评价指标' }],
          // targetExplain: [{ required: true, message: '请输入指标解释' }],
          score: [
            { required: true, message: '请输入分值' },
            {
              pattern: reg.regTwoNumber,
              message: '可输入10位正整数或2位小数',
              trigger: 'blur'
            }
          ],
          rwType: [{ required: true, message: '请选择考核方式' }]
        },
        // ------------
        evaluationMode: [],
        selectedList: [],
        editSelectedList: [], // 已选中编辑过的列表
        queryForm: { ...queryForm },
        isLoading: false,
        pages: {
          pageNum: 1,
          pageSize: 10
        }
      };
    },
    methods: {
      async scoreFocus(row) {
        if (row.id) {
          let bool = await this.hasReferenced(row);
          if (!bool) {
            row.isReadonly = true;
            this.$set(row, 'isReadonly', true);
            return this.$message.warning(
              '该评价指标中的评分标准已被引用，无法修改分值！'
            );
          }
        }
        this.$set(row, 'isReadonly', false);
      },
      handlePaste(e, row) {
        var clip = e.clipboardData.getData('Text');
        row.evaluateTarget = clip
          .replace(/\r/g, '')
          .replace(/\n/g, '')
          .replace(/&lt;/g, '<')
          .replace(/&gt;/g, '>')
          .replace(/&amp;/g, '&')
          .replace(/&quot;/g, '"')
          .replace(/&apos;/g, "'");
      },
      handleRwTypeName(val, row) {
        let id = val;
        if (!id) return;
        let obj = this.serviceDicts.type['rw_type'].find(
          (item) => item.value === id
        );
        row.rwTypeName = obj.label;
      },
      // 计算总分
      calculateScore() {
        let list = this.formInfo.memberList;
        let total = 0;
        list.forEach((item) => {
          total = calculate('add', total, item.score);
        });
        console.log(total);
        this.$set(this.queryForm, 'classifyScore', total);
      },
      setIndicator(val, currentRow) {
        if (currentRow) {
          let { classifyId, classifyName, addScore } = currentRow;
          this.queryForm.oldclassifyId = classifyId;
          this.queryForm.classifyName = classifyName;
          this.queryForm.oldclassifyName = classifyName;
          this.queryForm.addScore = addScore;
        } else {
          let obj = this.indicatorList.find((item) => item.id === val);
          this.queryForm.oldclassifyId = obj.id;
          this.queryForm.classifyName = obj.classifyName;
          this.queryForm.oldclassifyName = obj.classifyName;
          this.queryForm.addScore = obj.addScore;
        }
        this.formInfo.memberList = [];
        this.setHistoryList();
        this.calculateScore();
      },
      // 指标分类切换
      async changeIndicator(val) {
        let memberList = this.formInfo.memberList;
        if (!memberList.length) return this.setIndicator(val);

        await this.confirm('切换指标分类将清除未保存数据')
          .then(() => {
            this.setIndicator(val);
            this.$refs.classifyId.blur();
          })
          .catch(() => {
            this.queryForm.classifyId = this.queryForm.oldclassifyId;
            this.$refs.classifyId.blur();
          });
      },
      // 指标分类切换后 添加以前的数据以便修改
      setHistoryList() {
        let currentV = this.queryForm.classifyId;
        let list = this.editSelectedList;
        let historyList = list.filter((item) => item.classifyId == currentV);
        this.formInfo.memberList = historyList;
      },
      // 是否被引用
      async hasReferenced(row) {
        try {
          // true 可以删除
          // false 不能删除
          this.isLoading = true;
          let {
            data: { data }
          } = await validEvaluateRemove({
            evaluateTargetId: row.id
          });
          this.isLoading = false;
          return data || false;
        } catch (error) {
          this.isLoading = false;
          console.log(error);
          return false;
        }
      },
      async handleDelete(index, row) {
        if (row.id) {
          let bool = await this.hasReferenced(row);
          if (!bool)
            return this.$message.warning(
              '该评价指标中的评分标准已被引用，无法删除！'
            );
        }
        this.formInfo.memberList.splice(index, 1);
        this.calculateScore();
      },
      handleAdd() {
        let { classifyId, classifyName } = this.queryForm;
        let row = {
          classifyId,
          classifyName,
          evaluateTarget: '',
          // targetExplain: '',
          score: '',
          isReadonly: false,
          rwType: '',
          rwTypeName: '',
          list: []
        };
        let arr = this.formInfo.memberList;
        arr.push(row);
      },
      validForm(flag) {
        let bool = false;
        this.$refs[flag].validate((valid) => {
          bool = valid;
        });
        return bool;
      },
      // 提交
      onSubmit() {
        let bool = this.validForm('queryForm');
        let bool2 = this.validForm('form');
        if (!(bool && bool2)) return;
        let length = this.formInfo.memberList.length;
        if (!length) return this.$message.warning('请添加评价指标');
        this.$message.success('保存成功');

        let currentList = this.formInfo.memberList;
        let currentV = this.queryForm.classifyId;
        let arr = [];
        // 历史数据与当前数据一样就替换， 没有就添加到前面
        let index = this.editSelectedList.findIndex(
          (item) => item.classifyId === currentV
        );
        let historyList = this.editSelectedList.filter(
          (item) => item.classifyId !== currentV
        );
        if (index !== -1) {
          historyList.splice(index, 0, ...currentList);
          arr = [...historyList];
        } else {
          arr = [...historyList, ...currentList];
        }

        // 计算总分
        this.calculateScore();
        let { classifyId, addScore, classifyScore } = this.queryForm;

        arr.forEach((item) => {
          item.addScore = item.addScore || addScore;
          item.classifyScore =
            item.classifyId === classifyId ? classifyScore : item.classifyScore;
          let columnIndicator = this.getNum(arr, item.classifyId);
          item.columnIndicator = columnIndicator || length;
        });
        this.$emit('save-success', arr);
        this.hide();
      },
      // 获取要合并多少行
      getNum(arr, indicator) {
        let filterList = arr.filter((item) => item.classifyId === indicator);
        console.log(arr, filterList.length);
        // debugger;
        return filterList.length;
      },
      async show(arr, currentRow) {
        await this.$emit('getIndicatorList');
        this.visible = true;
        this.editSelectedList = arr;
        if (currentRow) {
          this.queryForm.classifyId = currentRow.classifyId;
          this.setIndicator(currentRow.classifyId, currentRow);
        }
      },
      resetPage() {
        this.queryForm = { ...queryForm };
        this.formInfo.memberList = [];
        this.editSelectedList = [];
      },
      reset() {
        this.resetPage();
      },
      hide() {
        this.resetPage();
        this.visible = false;
      }
    }
  };
</script>
