<template>
  <div>
    <el-form ref="form" :model="queryParams" :inline="true" label-suffix="：">
      <!-- <el-form-item label="考核单号" prop="rwNo">
        <el-input
          v-model="queryParams.rwNo"
          placeholder="请输入"
          clearable
          :maxlength="20"

        />
      </el-form-item> -->
      <el-form-item label="被考核机构" prop="assessedOrgId">
        <!-- <el-input
          v-model="queryParams.assessedOrgName"
          placeholder="请输入"
          clearable
          :maxlength="20"

        /> -->
        <InputTree
          v-model="queryParams.assessedOrgId"
          lazy
          clearable
          :form="queryParams"
          :samePort="true"
          :dic="deptData"
          style="width: 100%"
          :props="{
            label: 'title',
            value: 'id',
            isLeaf: 'isLeaf',
            formLabel: 'assessedOrgName',
            formValue: 'assessedOrgId'
          }"
          :load="lazyLoad"
          :lazyLoading="lazyLoading"
          @search="lazySearch"
        ></InputTree>
      </el-form-item>
      <el-form-item label="考核周期" prop="periodName">
        <el-input
          clearable
          placeholder="请选择"
          v-model="queryParams.periodName"
          @focus="dispatchHandle"
          @clear="clearPeriod"
          suffix-icon="el-icon-arrow-down"
        >
        </el-input>
      </el-form-item>
      <el-form-item label="考核日期" prop="rwDate">
        <!--  HH:mm:ss :default-time="['00:00:00', '23:59:59']" -->
        <el-date-picker
          v-model="queryParams.rwDate"
          type="daterange"
          style="width: 220px"
          align="right"
          range-separator="至"
          value-format="yyyy-MM-dd"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-delete" @click="resetQuery">清空</el-button>
      </el-form-item>
    </el-form>
    <AddAssessmentCycle
      ref="cycle"
      @saveSuccess="saveSuccess"
    ></AddAssessmentCycle>
  </div>
</template>
<script>
  import { getCurrentChild } from '@/api/assess-manage';
  import { InputTree } from '@/components/yk-select-tree';
  import { mapGetters } from 'vuex';
  import { getStore } from '@/util/store';
  import AddAssessmentCycle from '@/views/assessment-manage/evaluation-system/components/add-assessment-cycle.vue';
  const query = {
    rwNo: '',
    periodName: '',
    rwPeriodDetailId: '',
    assessedOrgId: '',
    assessedOrgName: '',
    rwDate: []
  };
  export default {
    components: {
      InputTree,
      AddAssessmentCycle
    },
    data() {
      return {
        queryParams: { ...query },
        unfold: false,
        loading: false,
        checkList: [],
        deptData: [],
        node: null,
        resolveFunc: null,
        lazyLoading: false
      };
    },
    computed: {
      ...mapGetters(['userInfo']),
      currentBusinessScope() {
        let org = getStore({ name: 'current-organization' });
        return org || {};
      }
    },
    mounted() {
      // this.handleQuery();
    },
    methods: {
      clearPeriod() {
        this.queryParams.rwPeriodDetailId = '';
        this.queryParams.periodName = '';
      },
      // 弹窗确定
      saveSuccess(row) {
        let { id, periodName } = row;
        this.queryParams.rwPeriodDetailId = id;
        this.queryParams.periodName = periodName;
      },
      dispatchHandle() {
        this.$refs.cycle.show({
          ...this.queryParams,
          initiateOrgId: this.currentBusinessScope['id']
        });
      },
      async lazySearch(title) {
        if (!title) {
          this.node.childNodes = [];
          this.lazyLoad(this.node, this.resolveFunc);
          return;
        }
        this.lazyLoad(this.node, this.resolveFunc, title);
      },
      async lazyLoad(node, resolve, title) {
        const { data } = node;
        const { id } = data || {};
        if (node.level === 0) {
          this.node = node;
          this.resolveFunc = resolve;
        }
        let parentId = this.currentBusinessScope['id'];
        let params = {
          tenantId: '000000',
          parentId: title ? parentId : id || parentId,
          title: title || undefined
        };
        this.lazyLoading = true;
        try {
          const {
            data: { data: list }
          } = await getCurrentChild(params);
          // 通过调用resolve将子节点数据返回，通知组件数据加载完成
          let arr = list || [];
          arr.forEach((item) => (item.isLeaf = !item.hasChildren));
          if (title) {
            this.deptData = arr;
          } else {
            resolve(arr);
          }
        } catch (error) {
          console.log(error);
        } finally {
          this.lazyLoading = false;
        }
      },
      // 重置
      resetQuery() {
        this.$refs['form'].resetFields();
        this.queryParams.assessedOrgId = '';
        this.queryParams.assessedOrgName = '';
        this.queryParams.rwPeriodDetailId = '';
        this.queryParams.periodName = '';
        this.handleQuery(1);
      },
      // 查询
      handleQuery(isResetQuery) {
        let { rwDate } = this.queryParams;
        let startRwDate = undefined;
        let endRwDate = undefined;
        if (Array.isArray(rwDate) && rwDate.length) {
          startRwDate = rwDate[0];
          endRwDate = rwDate[1];
        }
        let query = {
          ...this.queryParams,
          startRwDate,
          endRwDate,
          isResetQuery
        };
        this.$emit('search', query);
      }
    }
  };
</script>
<style lang="scss"></style>
