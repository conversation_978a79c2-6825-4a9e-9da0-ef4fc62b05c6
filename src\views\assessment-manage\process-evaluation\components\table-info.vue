<template>
  <div class="table_wrapper">
    <el-table
      ref="multipleTable"
      :data="source"
      stripe
      border
      :header-cell-style="{ backgroundColor: '#fafafa' }"
      v-loading="loading"
      :show-summary="activeName === '2'"
      :summary-method="getSummaries"
      @select="handleSelectionChange"
      @select-all="handleSelectionAll"
      style="width: 100%"
    >
      <el-table-column
        align="center"
        type="selection"
        :selectable="selectable"
        width="30"
      >
      </el-table-column>
      <!-- <el-table-column
        align="center"
        type="index"
        label="序号"
        width="50"
      ></el-table-column> -->
      <el-table-column
        width="110"
        align="center"
        label="发起组织"
        show-overflow-tooltip
      >
        <template slot-scope="{ row }">
          {{ row.initiateOrgName || '--' }}
        </template>
      </el-table-column>
      <el-table-column
        min-width="170"
        align="center"
        label="体系名称"
        show-overflow-tooltip
      >
        <template slot-scope="{ row }">
          {{ row.schemeName || '--' }}
        </template>
      </el-table-column>
      <el-table-column
        width="90"
        align="center"
        label="被考核机构"
        show-overflow-tooltip
      >
        <template slot-scope="{ row }">
          {{ row.assessedOrgName || '--' }}
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        width="100"
        label="考核周期"
        show-overflow-tooltip
      >
        <template slot-scope="{ row }">
          {{ row.periodName || '--' }}
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        width="96"
        label="考核日期"
        show-overflow-tooltip
      >
        <template slot-scope="{ row }">
          {{ row.rwDate || '--' }}
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        width="90"
        prop="rwTotalScore"
        label="扣/加分合计"
        show-overflow-tooltip
      >
        <template slot-scope="{ row }">
          {{
            [null, undefined, ''].includes(row.rwTotalScore)
              ? '0'
              : Number(row.rwTotalScore) > 0
              ? `+${row.rwTotalScore}`
              : row.rwTotalScore
          }}
        </template>
      </el-table-column>
      <el-table-column width="120" align="center" label="督办业务">
        <template slot-scope="{ row }">
          {{ row.superviseStatus ? '已下发督办单' : '/' }}
        </template>
      </el-table-column>
      <!-- <el-table-column
        min-width="75"
        align="center"
        label="考核单号"
        show-overflow-tooltip
      >
        <template slot-scope="{ row }">
          {{ row.rwNo || '--' }}
        </template>
      </el-table-column> -->
      <el-table-column
        align="center"
        :class-name="activeName === '1' ? 'all-table-operation' : ''"
        label="操作"
        :min-width="activeName === '1' ? 110 : 50"
      >
        <template slot-scope="{ row }">
          <el-button
            type="text"
            v-if="permission['process_evaluation_view']"
            @click="$emit('dispatch', 'view', row)"
            >查看
          </el-button>
          <el-button
            v-if="activeName === '1' && permission['process_evaluation_edit']"
            type="text"
            @click="$emit('dispatch', 'edit', row)"
            >编辑
          </el-button>
          <el-button
            v-if="
              activeName === '1' && permission['process_evaluation_initiate']
            "
            type="text"
            @click="$emit('dispatch', 'supervise', row)"
            >发起督办
          </el-button>
          <el-button
            type="text"
            v-if="activeName === '1' && permission['process_evaluation_delete']"
            @click="del(row)"
            >删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
  // import { cloneDeep } from 'lodash';
  import { mapGetters } from 'vuex';
  import { calculate } from '@/util/util';

  export default {
    name: 'ProjectLibraryTableInfo',
    props: {
      loading: {
        type: Boolean,
        default: false
      },
      source: {
        type: Array,
        default() {
          return [];
        }
      },
      activeName: {
        type: String,
        default: '1'
      }
    },
    // watch: {
    //   source: {
    //     handler(arr) {
    //       this.list = cloneDeep(arr);
    //     },
    //     deep: true
    //   }
    // },
    data() {
      return {
        list: [],
        visited: false
      };
    },
    methods: {
      getSummaries(param) {
        const { columns, data } = param;
        const sums = [];
        columns.forEach((column, index) => {
          if (index === 0) {
            sums[index] = '合计';
            return;
          }
          const values = data.map((item) => item[column.property]);
          if (!values.every((value) => isNaN(value))) {
            sums[index] = calculate('add', ...values).toFixed(2);
            // sums[index] += ' 元';
          } else {
            sums[index] = ' ';
          }
        });
        return sums;
      },
      // 可选的 退回 3 待提交 0
      selectable(row) {
        return !!row;
        // if (['3', '0'].includes(row.reviewStatus)) {
        //   return true;
        // } else {
        //   return false;
        // }
      },
      commit(row) {
        this.$emit('dispatch', 'commit', row);
      },
      // 项目删除
      del(row) {
        this.$emit('dispatch', 'delete', row);
      },
      // 多选框
      // no-unused-vars
      handleSelectionChange(selection, row) {
        console.log('selection', row);
        this.$emit('dispatch', 'selection', row);
      },
      // 全选
      handleSelectionAll(selection) {
        console.log('selectionAll', selection);
        this.$emit('dispatch', 'selectionAll', selection);
      }
    },
    computed: {
      ...mapGetters(['permission']),
      menuName() {
        let bool = this.$route.name.includes('schedule');
        return bool ? 'schedule' : 'check';
      },
      tableHeight() {
        let height = document.documentElement.clientHeight;
        let calcHeight = null;
        if (height > 800) {
          if (this.activeName === '1')
            calcHeight = (height - (65 + 40 + 10 + 10 + 60 + 190)) / 1.1;
          else calcHeight = (height - (65 + 40 + 10 + 10 + 60 + 190)) / 1;
        } else {
          if (this.activeName === '1')
            calcHeight = (height - (65 + 40 + 10 + 10 + 60 + 190)) / 1.2;
          else calcHeight = (height - (65 + 40 + 10 + 10 + 60 + 190)) / 1;
        }
        return `${calcHeight}px`;
      }
    }
  };
</script>

<style lang="scss" scoped>
  .project-label {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;

    // justify-content: space-between;
  }

  .show-center {
    justify-content: center;
  }
</style>
