<template>
  <basic-container autoHeight v-loading="loading">
    <search @search="query" :activeName="activeName" ref="search" />
    <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
      <el-tab-pane label="待审核" name="0"></el-tab-pane>
      <el-tab-pane label="已审核" name="1"></el-tab-pane>
      <!-- <el-tab-pane label="已发起" name="2"></el-tab-pane> -->
    </el-tabs>
    <div style="margin-bottom: 15px" v-if="activeName === '0'">
      <el-button
        :disabled="!rows.length"
        type="primary"
        v-if="permission['approval-check-approve']"
        icon="el-icon-check"
        @click="handleAdd"
        >审核</el-button
      >
      <!-- <el-button
        v-if="activeName === '0' && permission['approval-check-opinion']"
        :disabled="!rows.length"
        type="primary"
        icon="el-icon-check"

        @click="handleReview"
        >审查意见</el-button
      > -->
    </div>
    <transition name="el-fade-in-linear">
      <table-info
        :source="tableData"
        ref="tableInfo"
        :loading="loading"
        :activeName="activeName"
        @dispatch="dispatch"
      />
    </transition>

    <yk-pagination
      small
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.current"
      :limit.sync="queryParams.size"
      @pagination="getList"
    />
    <!-- 批量操作结果 -->
    <ErrorDialog
      v-if="formVisible"
      :dialogTitle="dialogTitle"
      :formVisible="formVisible"
      :list="batchList"
      labelName="项目立项编号"
      @refresh="request"
      @close="handleDialogClose"
    />
    <Approval
      ref="approval"
      @close="handleDialogClose"
      @save-success="approvalSuccess"
    ></Approval>
    <ValidDialog
      v-if="validateVisible"
      :dialogTitle="dialogTitle"
      :formVisible="validateVisible"
      :list="batchList"
      :showProjectName="true"
      labelName="提交单位"
      @close="handleDialogClose"
    ></ValidDialog>
  </basic-container>
</template>

<script>
  import { Search, TableInfo, ErrorDialog, Approval } from './components';
  import {
    approveCheckPage,
    approvedVia,
    returnFlow,
    instanceCheckSubmit,
    approveCancel
  } from '@/api/workflow';
  import ValidDialog from '@/components/yk-validate-dialog';
  import { mapGetters } from 'vuex';
  import { trimAll } from '@/util/util';
  import 'nprogress/nprogress.css';

  export default {
    name: 'Project-progress',
    components: { Search, TableInfo, ErrorDialog, Approval, ValidDialog },
    data() {
      return {
        // 查询条件
        queryParams: {
          size: 20,
          current: 1
        },
        // 表格加载中
        loading: false,
        // 列表条目总数量
        total: 0,
        // 列表数据
        tableData: [],
        // 是否显示
        formVisible: false,
        addVisible: false,
        validateVisible: false,
        // dialog标题
        dialogTitle: '',
        // 选中项
        rows: [],
        // 批量提报结果
        batchList: [],
        activeName: '0'
      };
    },
    computed: {
      ...mapGetters(['permission'])
    },
    activated() {
      // if (!this.$route.query.remain) {
      //   this.rows = [];
      //   this.activeName = '0';
      // }
      if (this.$route.query.home === '1') {
        this.activeName = '0';
        this.rows = [];
        this.$refs.search.resetQuery();
        return this.query({});
      }
      this.request();
    },
    methods: {
      async approvalSuccess(obj) {
        try {
          const Func = obj.approval === '1' ? approvedVia : returnFlow;
          await Func(obj);
          this.$message.success('操作成功');
          this.rows = [];
          this.request();
        } catch (error) {
          console.log(error);
        }
      },
      handleClick() {
        // this.query({});
        this.rows = [];
        this.$refs.search.resetQuery();
      },
      // 撤回
      async withdrawal(row) {
        try {
          await this.confirm('确定要撤回吗？');
          this.loading = true;
          let params = {
            instanceId: row.id
          };
          const {
            data: { data }
          } = await approveCancel(params);
          this.batchList = data || [];
          this.$message.success('操作成功');
          this.request();
        } catch (e) {
          console.log(e);
        } finally {
          this.loading = false;
        }
      },
      handleAddTab() {
        this.dialogTitle = '添加标签';
        this.addVisible = true;
      },
      setPage(row) {
        let resultList = this.batchList;
        let bool = resultList.every((item) => item.result);
        if (!bool) return;
        let length = this.tableData.length;
        if (row) {
          if (length === 1) {
            this.queryParams.current = 1;
          }
        } else {
          let rowsLength = this.rows.length;
          if (rowsLength >= length) {
            console.log('rowsLength', rowsLength);
            this.queryParams.current = 1;
          }
        }
      },
      // 查询
      query(params) {
        if (params.isResetQuery) {
          this.rows = [];
        }
        Object.assign(
          this.queryParams,
          {
            current: 1,
            size: 20
          },
          { ...params, isResetQuery: undefined }
        );
        for (const key in this.queryParams) {
          this.queryParams[key] = trimAll(this.queryParams[key]);
        }
        this.request();
      },
      // 分页查询
      getList({ page, limit }) {
        Object.assign(this.queryParams, {
          current: page,
          size: limit
        });
        this.request();
      },
      // 请求列表数据
      async request() {
        try {
          this.loading = true;
          const { data } = await approveCheckPage({
            ...this.queryParams,
            approveType: this.activeName,
            businessType: '3'
          });
          const { total = 0, records = [] } = data.data ? data.data : {};
          this.total = total;
          this.tableData = records;
          this.setSelected(1);
        } catch (e) {
          console.error(e);
        } finally {
          setTimeout(() => {
            this.loading = false;
          }, 1500);
        }
      },
      setSelected(flag) {
        let ids = this.rows.map((item) => item.id);
        let selectedRows = this.tableData.filter((item) =>
          ids.includes(item.id)
        );
        if (selectedRows.length) {
          this.$nextTick(() => {
            selectedRows.forEach((row) => {
              this.$refs['tableInfo'].$refs['multipleTable'].toggleRowSelection(
                row,
                true
              );
            });
          });
        } else {
          this.$nextTick(() => {
            this.$refs['tableInfo'].$refs['multipleTable'].clearSelection();
          });
        }
        if (!flag) return;
        this.$nextTick(() => {
          this.$refs.tableInfo.showTable = false;
          setTimeout(() => {
            this.$refs.tableInfo.showTable = true;
            let ids = this.rows.map((item) => item.id);
            let selectedRows = this.tableData.filter((item) =>
              ids.includes(item.id)
            );
            if (selectedRows.length) {
              this.$nextTick(() => {
                selectedRows.forEach((row) => {
                  this.$refs['tableInfo'].$refs[
                    'multipleTable'
                  ].toggleRowSelection(row, true);
                });
              });
            } else {
              this.$nextTick(() => {
                this.$refs['tableInfo'].$refs['multipleTable'].clearSelection();
              });
            }
          }, 500);
        });
      },
      // 列表操作
      dispatch(type, data) {
        switch (type) {
          case 'commit':
            return this.withdrawal(data);
          case 'edit':
            return this.handleEdit(data);
          case 'check':
            return this.handleCheck(data);
          case 'view':
            return this.handleView(data);
          case 'refresh':
            return this.request();
          case 'selection':
            return this.handleSelect(data);
          case 'selectionAll':
            return this.handleSelectAll(data);
          default:
            return false;
        }
      },
      handleCheck(row) {
        this.$router.push(
          `/project-manage/project-schedule/schedule-check/${row.id}`
        );
      },
      handleSelect(row) {
        let index = this.rows.findIndex((item) => item.id === row.id);
        if (index >= 0) {
          this.rows.splice(index, 1);
        } else {
          this.rows.push(row);
        }
      },
      uniqueFunc(arr, uniId) {
        let hash = {};
        return arr.reduce((accum, item) => {
          hash[item[uniId]]
            ? ''
            : (hash[item[uniId]] = true && accum.push(item));
          return accum;
        }, []);
      },
      handleSelectAll(rows) {
        if (!this.rows.length && rows.length)
          return (this.rows = [...this.rows, ...rows]);
        if (rows.length) {
          this.rows = this.uniqueFunc([...this.rows, ...rows], 'id');
        } else {
          let clearSelection = this.tableData.map((item) => item.id);
          this.rows = this.rows.filter(
            (item) => !clearSelection.includes(item.id)
          );
        }
      },
      validSomeCurrFlowSort() {
        let complexArr = [...this.rows];
        let first = complexArr[0].currFlowSort;
        let bool = complexArr.every((item) => item.currFlowSort === first);
        return bool;
      },
      validSomeStatus() {
        let complexArr = [...this.rows];
        let first = complexArr[0].curStatus;
        let bool = complexArr.every((item) => item.curStatus === first);
        return bool;
      },
      validSameNextNode() {
        let complexArr = [...this.rows];
        let first = complexArr[0].nextStepName;
        let bool = complexArr.every((item) => item.nextStepName === first);
        return bool;
      },
      async handleAdd() {
        // let bool = this.validSomeCurrFlowSort();
        // if (!bool){
        //   this.handleDialogClose();
        //   return this.warning('不同流程节点的业务不能同时审核');
        // }
        let nextbool = this.validSameNextNode();
        if (!nextbool) {
          this.handleDialogClose();
          return this.warning('不同下个节点的流程不能同时办理');
        }
        let boolStatus = this.validSomeStatus();
        if (!boolStatus) {
          this.handleDialogClose();
          return this.warning('不同状态的流程不能同时办理');
        }
        let complexArr = [...this.rows];
        // 有退回
        let hasBack = complexArr.some((item) => item.curStatus === 2);
        // 有不同层级
        let hasDifferent = false;
        if (complexArr.length >= 2) {
          let iniLevel = complexArr.map((item) => item.iniLevel);
          hasDifferent = complexArr.length === new Set(iniLevel).size;
        }

        // if (hasBack && hasDifferent) {
        // this.handleDialogClose();
        //   return this.$message.warning(
        //     '请不要选择既是已退回状态又是不同层级的审核数据'
        //   );
        // }
        let checkObj = await this.instanceCheckSubmit(complexArr);
        let { dataList = [] } = checkObj;
        if (dataList.length) {
          this.batchList = dataList;
          this.dialogTitle = '验证结果';
          this.validateVisible = true;
          return;
        }
        const instanceIds = complexArr.map((item) => item.id);
        let params = {
          hasBack,
          hasDifferent,
          businessType: '3',
          instanceIds
        };
        this.$refs.approval.show(params);
      },
      // 校验提报数据
      async instanceCheckSubmit(rows) {
        try {
          this.loading = true;
          let params = rows.map((item) => ({
            id: item.id,
            currFlowSort: item.currFlowSort
          }));
          const {
            data: { data }
          } = await instanceCheckSubmit(params);
          return data || false;
        } catch (e) {
          return false;
        } finally {
          this.loading = false;
        }
      },
      handleSubmit(row) {
        if (!this.rows.length && !row)
          return this.$message({ type: 'warning', message: '请先选择项目' });
        console.log(row);
        try {
          this.loading = true;
          // const res = await getPage(this.queryParams);
          // const { total = 0, records = [] } = res.data.data;
          // this.total = total;
          // this.tableData = records;
          // this.setSelected();
          this.$message.success('操作成功！');
        } catch (e) {
          console.error(e);
        } finally {
          this.loading = false;
        }
        this.dialogTitle = '批量提报结果';
        this.formVisible = true;
      },
      handleView(row) {
        this.$router.push({
          path: `/project-manage/project-check/check-detail/${row.businessId}`,
          query: {
            flag: 'center',
            audit: this.activeName == '0' ? 'audit' : ''
          }
        });
      },
      handleEdit(row) {
        this.$router.push(
          `/project-manage/project-submit/submit-edit/${row.id}`
        );
      },
      handleDialogClose() {
        this.rows = [];
        this.setSelected();
        this.validateVisible = false;
        this.formVisible = false;
        this.addVisible = false;
      }
    }
  };
</script>

<style lang="scss" scoped>
  .el-pagination {
    margin-top: 20px;
  }
</style>
