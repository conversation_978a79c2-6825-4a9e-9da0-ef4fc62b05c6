import request from '@/router/axios';
import website from '@/config/website';

export const loginByUsername = (
  tenantId,
  deptId,
  roleId,
  username,
  password,
  type,
  key,
  code
) =>
  request({
    url: '/api/szyk-auth/oauth/token',
    method: 'post',
    headers: {
      'Tenant-Id': tenantId,
      'Dept-Id': website.switchMode ? deptId : '',
      'Role-Id': website.switchMode ? roleId : '',
      'Captcha-Key': key,
      'Captcha-Code': code
    },
    params: {
      tenantId,
      username,
      password,
      grant_type: website.captchaMode ? 'captcha' : 'password',
      scope: 'all',
      type
    }
  });
// 修改漏洞
export const LoginByUsernameCode = (
  tenantId,
  deptId,
  roleId,
  username,
  password,
  type,
  key,
  code,
  trackData
) =>
  request({
    url: '/api/szyk-auth/oauth/code',
    method: 'post',
    headers: {
      'Tenant-Id': tenantId,
      'Dept-Id': website.switchMode ? deptId : '',
      'Role-Id': website.switchMode ? roleId : '',
      'Captcha-Key': key,
      'Captcha-Code': code
    },
    // params: {
    //   tenantId,
    //   username,
    //   password,
    //   grant_type: website.captchaMode ? 'code' : 'password',
    //   scope: 'all',
    //   type
    // },
    data: {
      trackData,
      tenantId,
      username,
      password,
      grant_type: website.captchaMode ? 'code' : 'password',
      scope: 'all',
      type
    }
  });

export const loginBySocial = (tenantId, source, code, state) =>
  request({
    url: '/api/szyk-auth/oauth/token',
    method: 'post',
    headers: {
      'Tenant-Id': tenantId
    },
    params: {
      tenantId,
      source,
      code,
      state,
      grant_type: 'social',
      scope: 'all'
    }
  });

export const refreshToken = (refresh_token, tenantId, deptId, roleId) =>
  request({
    url: '/api/szyk-auth/oauth/token',
    method: 'post',
    headers: {
      'Tenant-Id': tenantId,
      'Dept-Id': website.switchMode ? deptId : '',
      'Role-Id': website.switchMode ? roleId : ''
    },
    params: {
      tenantId,
      refresh_token,
      grant_type: 'refresh_token',
      scope: 'all'
    }
  });

export const registerGuest = (form, oauthId) =>
  request({
    url: '/api/szyk-user/register-guest',
    method: 'post',
    params: {
      tenantId: form.tenantId,
      name: form.name,
      account: form.account,
      password: form.password,
      oauthId
    }
  });

export const getButtons = () =>
  request({
    url: '/api/szyk-system/menu/buttons',
    method: 'get'
  });

export const getCaptcha = () =>
  request({
    url: '/api/szyk-auth/oauth/captcha',
    method: 'get'
  });

export const getCaptchaCode = (type) =>
  request({
    url: '/api/szyk-auth/oauth/gen',
    method: 'get',
    params: {
      type
    }
  });

export const logout = () =>
  request({
    url: '/api/szyk-auth/oauth/logout',
    method: 'get'
  });

export const getUserInfo = () =>
  request({
    url: '/api/szyk-auth/oauth/user-info',
    method: 'get'
  });

export const sendLogs = (list) =>
  request({
    url: '/api/szyk-auth/oauth/logout',
    method: 'post',
    data: list
  });

export const clearCache = () =>
  request({
    url: '/api/szyk-auth/oauth/clear-cache',
    method: 'get'
  });
export const userCheck = (account) =>
  request({
    url: '/api/attila-system/user-check',
    method: 'get',
    params: { account }
  });
// sso 单点登录认证
export const getSSO = (data) =>
  request({
    url: '/api/szyk-auth/oauth/sso',
    method: 'post',
    data
  });
// sso 单点登录认证
export const testgetSSO = (data) =>
  request({
    url: '/api/szyk-auth/oauth/token',
    method: 'post',
    data
  });

// 发送用户pv cookies
export const postCookies = (data) =>
  request({
    url: '/api/szyk-user/user-behavior/write',
    method: 'post',
    headers: {
      ['Content-Type']: 'application/json'
    },
    data
  });

// 用户行为分析首页
export const getUserAnalysisData = () =>
  request({
    url: '/api/szyk-user/user-behavior/user-behavior',
    method: 'get'
  });

// 访问分析
export const getVisitData = (dim) =>
  request({
    url: `/api/szyk-user/user-behavior/page-view-list?dim=${dim}`,
    method: 'get'
  });

// 浏览器占比分析
export const getBrowserData = (dim) =>
  request({
    url: `/api/szyk-user/user-behavior/browser-rate?dim=${dim}`,
    method: 'get'
  });

// 活跃用户
export const getActiveData = (dim) =>
  request({
    url: `/api/szyk-user/user-behavior/active-user?dim=${dim}`,
    method: 'get'
  });
