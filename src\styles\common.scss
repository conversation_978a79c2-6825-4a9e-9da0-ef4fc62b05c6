// 全局变量
@import './variables';

// ele样式覆盖
@import './element-ui';

// 顶部右侧显示
@import './top';

// 导航标签
@import './tags';

// 工具类函数
@import './mixin';

// 侧面导航栏
@import './sidebar';

// 动画
@import './animate/vue-transition';

// 主题
@import './theme/index';

// 适配
@import './media';

// 通用配置
@import './normalize';

// 右下角消息弹窗
@import './notification';

// 驾驶舱
@import './cockpit';

a{
  color: #333;
  text-decoration: none;
}

*{
  outline: none;
}

.distribute-dialog{
  .el-dialog__header{
    padding: 10px 20px;
    border-bottom: 1px solid #EBEEF5
  }

  .el-dialog__headerbtn {
    top: 12px;
  }

  .el-dialog__footer{
    padding: 10px 20px;
    border-top: 1px solid #EBEEF5
  }
}

// 滚动条样式
@include scrollBar;

// 底部按钮组
.btn-wrapper {
  margin-top: 30px;
  text-align: center;
}

.el-notification{
 z-index: 999999999 !important;
}

.roleId-popper {
  display: none;
}

.roleId-height {
  height: 310px;
}

// .el-table__body-wrapper.is-scrolling-left~.el-table__fixed{
//   margin-bottom: 7px;
// }
.el-table--scrollable-x .el-table__fixed,.el-table--scrollable-x .el-table__fixed-right{
  // bottom: 14px !important;
  // height: calc(100% - 13px) !important;
}

.table_wrapper{
  margin-bottom: 1px;
}

// 左侧组织树折叠
.el-col {
  &.right-span {
    position: relative;

    &.full-width {
      margin-left: 20px !important;
    }

    .toggle-btn {
      position: absolute;
      top: 0;
      left: -11px;
      padding: 15px 3px;
      color: #fff;
      background: #004ca7;
    }
  }
}

.expert-review-wrap{
    position: fixed;
    top: auto;
    right: 40px;
    bottom: 43px;
    left: auto;
    z-index: 1026 !important;
    margin: auto;
    overflow: auto;
  background-color: antiquewhite;

    .el-dialog {
      margin: 0 auto;
      margin-top: 0 !important;
      border: 1px solid #ebeef5;
    }
}

.text_ellipsis_title {
  width: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
