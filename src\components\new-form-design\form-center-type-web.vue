<template>
  <draggable
    v-model="list"
    group="formDesign"
    animation="300"
    :scroll="true"
    class="formDesignWeb"
    :move="onMove"
  >
    <div
      v-for="(li, i) of list"
      :key="li.id"
      @click.stop="clickComponent(li)"
      class="formList"
      :class="{
        error: isErrorList(li.id),
        active: id === li.id,
        tips: li.type === 'tips',
        componentGroup: [
          'rest',
          'work',
          'out',
          'trip',
          'turnFormal',
          'leave'
        ].includes(li.type),
        bottomDistance: ['tips', 'area'].includes(li.type)
      }"
    >
      <div v-if="li.type !== 'form'" class="form-modal"></div>
      <!-- 多行输入框 -->
      <template v-if="li.type === 'textarea'">
        <el-form-item
          :class="{ isrequired: li.valueJson.required }"
          :label="li.valueJson.name"
        >
          <el-input
            type="textarea"
            :placeholder="li.valueJson.placeholder"
            :maxlength="li.valueJson.maxLength"
            show-word-limit
          >
          </el-input>
        </el-form-item>
      </template>
      <!-- 数字输入框 -->
      <template v-else-if="li.type === 'inputNumber'">
        <el-form-item
          :class="{ isrequired: li.valueJson.required }"
          :label="
            li.valueJson.unit
              ? li.valueJson.name + '(' + li.valueJson.unit + ')'
              : li.valueJson.name
          "
        >
          <el-input-number
            :placeholder="li.valueJson.placeholder"
            controls-position="right"
          ></el-input-number>
        </el-form-item>
      </template>
      <!-- 金额 -->
      <template v-else-if="li.type === 'inputMoney'">
        <el-form-item
          :class="{ isrequired: li.valueJson.required }"
          :label="li.valueJson.name"
        >
          <el-input-number
            :placeholder="li.valueJson.placeholder"
            controls-position="right"
          ></el-input-number>
        </el-form-item>
        <el-form-item v-if="li.valueJson.capital" label="大写"></el-form-item>
      </template>
      <!-- 说明文字 -->
      <template v-else-if="li.type === 'tips'">
        <span
          :class="{
            blueInfo: li.valueJson.link,
            redInfo: li.valueJson.fontColor
          }"
          >{{ li.valueJson.content }}</span
        >
      </template>
      <!-- 单选框 多选框 -->
      <template v-else-if="['radio', 'checkbox'].includes(li.type)">
        <el-form-item
          :label="li.valueJson.name"
          :class="{ isrequired: li.valueJson.required }"
        >
          <h-select :placeholder="li.valueJson.placeholder"></h-select>
        </el-form-item>
      </template>
      <!-- 日期 -->
      <template v-else-if="li.type === 'date'">
        <el-form-item
          :label="li.valueJson.name"
          :class="{ isrequired: li.valueJson.required }"
        >
          <template v-if="li.valueJson.dateType === 1"
            ><el-date-picker
              type="date"
              :placeholder="li.valueJson.placeholder"
            >
            </el-date-picker>
          </template>
          <template v-if="li.valueJson.dateType === 2">
            <div class="date-apm">
              <el-date-picker
                type="date"
                :placeholder="li.valueJson.placeholder"
              >
              </el-date-picker>
              <h-select></h-select>
            </div>
          </template>
          <template v-if="li.valueJson.dateType === 3">
            <el-date-picker
              type="datetime"
              :placeholder="li.valueJson.placeholder"
            >
            </el-date-picker>
          </template>
        </el-form-item>
      </template>
      <!-- 日期区间 -->
      <template v-else-if="li.type === 'daterange'">
        <el-form-item
          :label="li.valueJson.nameOne"
          :class="{ isrequired: li.valueJson.required }"
        >
          <template v-if="li.valueJson.dateType === 1"
            ><el-date-picker
              type="date"
              :placeholder="li.valueJson.placeholder"
            >
            </el-date-picker>
          </template>
          <template v-if="li.valueJson.dateType === 2">
            <div class="date-apm">
              <el-date-picker
                type="date"
                :placeholder="li.valueJson.placeholder"
              >
              </el-date-picker>
              <h-select></h-select>
            </div>
          </template>
          <template v-if="li.valueJson.dateType === 3">
            <el-date-picker
              type="datetime"
              :placeholder="li.valueJson.placeholder"
            >
            </el-date-picker>
          </template>
        </el-form-item>
        <el-form-item
          :label="li.valueJson.nameTwo"
          :class="{ isrequired: li.valueJson.required }"
        >
          <template v-if="li.valueJson.dateType === 1"
            ><el-date-picker
              type="date"
              :placeholder="li.valueJson.placeholder"
            >
            </el-date-picker>
          </template>
          <template v-if="li.valueJson.dateType === 2">
            <div class="date-apm">
              <el-date-picker
                type="date"
                :placeholder="li.valueJson.placeholder"
              >
              </el-date-picker>
              <h-select></h-select>
            </div>
          </template>
          <template v-if="li.valueJson.dateType === 3">
            <el-date-picker
              type="datetime"
              :placeholder="li.valueJson.placeholder"
            >
            </el-date-picker>
          </template>
        </el-form-item>
        <el-form-item :label="li.valueJson.nameThree"> 自动计算 </el-form-item>
      </template>
      <!-- 图片 -->
      <template v-else-if="li.type === 'image'">
        <el-form-item
          :label="li.valueJson.name"
          :class="{ isrequired: li.valueJson.required }"
        >
          <div class="imageContent">
            <span class="iconfont icon-plus-0"></span>
            <span class="word">上传图片</span>
          </div>
        </el-form-item>
      </template>

      <!-- 附件 -->
      <template v-else-if="li.type === 'file'">
        <el-form-item
          :label="li.valueJson.name"
          :class="{ isrequired: li.valueJson.required }"
        >
          <div class="file-btn">选择附件</div>
        </el-form-item>
      </template>

      <!-- 计算公式 -->
      <template v-else-if="li.type === 'computed'">
        <el-form-item
          :class="{ isrequired: li.valueJson.required }"
          :label="li.valueJson.name + '：'"
        >
          自动计算
        </el-form-item>
      </template>
      <!-- 省市区 -->
      <template v-else-if="li.type === 'region'">
        <el-form-item
          :class="{ isrequired: li.valueJson.required }"
          :label="li.valueJson.name + '：'"
        >
          <el-input placeholder="请选择"></el-input>
        </el-form-item>
        <el-form-item
          v-if="li.valueJson.provinceType === 2"
          :class="{ isrequired: li.valueJson.required }"
          label="街道"
        >
          <el-input
            placeholder="请输入"
            :maxlength="50"
            show-word-limit
          ></el-input>
        </el-form-item>
      </template>
      <!-- 明细/表格 -->
      <template v-else-if="li.type === 'form'">
        <el-form-item
          :class="{ isrequired: li.valueJson.required }"
          :label="li.valueJson.name"
        >
          <div class="form-tablecont">
            <div class="title">{{ li.valueJson.name + '1' }}</div>
            <form-center-type
              ref="formContent"
              @selectCell="updateParentCell"
              class="formContent"
              :is-tip="false"
              :children.sync="li.children"
            ></form-center-type>
          </div>
          <el-button class="form-table-btn" type="text" icon="el-icon-plus">{{
            li.valueJson.action
          }}</el-button>
        </el-form-item>
      </template>

      <!-- 请假/调休套件，加班套件，外出套件 -->
      <template v-else-if="['rest', 'work', 'out'].includes(li.type)">
        <div class="component-name">
          {{
            li.type === 'rest'
              ? '请假/调休套件'
              : li.type === 'work'
              ? '加班套件'
              : '外出套件'
          }}
        </div>
        <header v-if="li.header">
          {{ li.header }}
          <span class="importTag">*</span>
        </header>
        <p v-if="li.header" class="placeholder daterange">
          请选择
          <i class="el-icon-arrow-right"></i>
        </p>
        <header>
          开始时间
          <span class="importTag">*</span>
        </header>
        <p class="placeholder daterange">
          请选择 <i class="el-icon-arrow-right"></i>
        </p>
        <header>
          结束时间
          <span class="importTag">*</span>
        </header>
        <p class="placeholder daterange">
          请选择 <i class="el-icon-arrow-right"></i>
        </p>
        <header>时长</header>
        <p class="placeholder">自动计算</p>
      </template>

      <!-- 出差套件 -->
      <template v-else-if="li.type === 'trip'">
        <div class="component-name">出差套件</div>
        <header>
          出差事由
          <span class="sizeLength">0/200</span>
        </header>
        <p class="placeholder textareaContent">请输入</p>
        <header>交通工具<span class="importTag">*</span></header>
        <p class="placeholder daterange">
          请选择 <i class="el-icon-arrow-right"></i>
        </p>
        <header>单程/往返<span class="importTag">*</span></header>
        <p class="placeholder daterange">
          请选择 <i class="el-icon-arrow-right"></i>
        </p>
        <header>出发城市<span class="importTag">*</span></header>
        <p class="placeholder daterange">
          请选择 <i class="el-icon-arrow-right"></i>
        </p>
        <header>目的城市<span class="importTag">*</span></header>
        <p class="placeholder daterange">
          请选择 <i class="el-icon-arrow-right"></i>
        </p>
        <header>开始时间<span class="importTag">*</span></header>
        <p class="placeholder daterange">
          请选择 <i class="el-icon-arrow-right"></i>
        </p>
        <header>结束时间<span class="importTag">*</span></header>
        <p class="placeholder daterange">
          请选择 <i class="el-icon-arrow-right"></i>
        </p>
        <header>时长</header>
        <p class="placeholder daterange">自动计算</p>
        <el-button class="addBtn" type="text" icon="el-icon-circle-plus-outline"
          >添加</el-button
        >
        <header class="days">出差天数</header>
        <p class="placeholder daterange">自动计算</p>
        <header>出差备注<span class="sizeLength">0/200</span></header>
        <p class="placeholder textareaContent">请输入</p>
        <header v-if="li.valueJson.peerPeople">
          同行人<span class="importTag">*</span>
        </header>
        <p v-if="li.valueJson.peerPeople" class="placeholder daterange">
          请选择 <i class="el-icon-arrow-right"></i>
        </p>
      </template>

      <!-- 转正套件 -->
      <template v-else-if="li.type === 'turnFormal'">
        <div class="component-name">转正套件</div>
        <header>申请人<span class="importTag">*</span></header>
        <p class="placeholder daterange">
          请选择 <i class="el-icon-arrow-right"></i>
        </p>
        <header>
          入职时间
          <span class="importTag">*</span>
        </header>
        <p class="placeholder daterange">
          请选择 <i class="el-icon-arrow-right"></i>
        </p>
        <header>
          试用期
          <span class="importTag">*</span>
        </header>
        <p class="placeholder daterange">请输入</p>
        <header>转正时间</header>
        <p class="placeholder">请选择 <i class="el-icon-arrow-right"></i></p>
      </template>

      <!-- 离职套件 -->
      <template v-else-if="li.type === 'leave'">
        <div class="component-name">离职套件</div>
        <header>入职时间<span class="importTag">*</span></header>
        <p class="placeholder daterange">
          请选择 <i class="el-icon-arrow-right"></i>
        </p>
        <header>预计离职时间<span class="importTag">*</span></header>
        <p class="placeholder daterange">
          请选择 <i class="el-icon-arrow-right"></i>
        </p>
        <header>离职原因<span class="importTag">*</span></header>
        <p class="placeholder daterange">请输入</p>
        <header>
          离职原因备注
          <span class="sizeLength">0/200</span>
        </header>
        <p class="placeholder textareaContent">请输入</p>
        <header>工作交接人<span class="importTag">*</span></header>
        <p class="placeholder daterange">
          请选择 <i class="el-icon-arrow-right"></i>
        </p>
        <header>
          工作交接事项
          <span class="importTag">*</span>
          <span class="sizeLength">0/500</span>
        </header>
        <p class="placeholder textareaContent">请输入</p>
      </template>

      <!-- 审批下单 -->
      <template v-else-if="li.type === 'htToXd'">
        <div class="component-name">下单审批</div>
        <header>交易类型</header>
        <p class="placeholder daterange">请输入</p>
        <header>发布企业</header>
        <p class="placeholder daterange">请输入</p>
        <header>挂牌标题</header>
        <p class="placeholder daterange">请输入</p>
        <header>商品名称</header>
        <p class="placeholder daterange">请输入</p>
        <header>商品单价</header>
        <p class="placeholder daterange">请输入</p>
        <header>采购数量</header>
        <p class="placeholder daterange">请输入</p>
        <header>交易总额</header>
        <p class="placeholder daterange">请输入</p>
        <header>交易保证金</header>
        <p class="placeholder daterange">请输入</p>
        <header>服务费</header>
        <p class="placeholder daterange">请输入</p>
        <header>交割地</header>
        <p class="placeholder daterange">请输入</p>
        <header>产地</header>
        <p class="placeholder daterange">请输入</p>
        <header>交货方式</header>
        <p class="placeholder daterange">请输入</p>
      </template>

      <!-- 一口价挂牌 -->
      <template v-else-if="li.type === 'htToGp'">
        <div class="component-name">一口价挂牌</div>
        <header>交易类型</header>
        <p class="placeholder daterange">请输入</p>
        <header>挂牌标题</header>
        <p class="placeholder daterange">请输入</p>
        <header>交易截止时间</header>
        <p class="placeholder daterange">
          请选择 <i class="el-icon-arrow-right"></i>
        </p>
        <header>商品名称</header>
        <p class="placeholder daterange">请输入</p>
        <header>所属类目</header>
        <p class="placeholder daterange">请输入</p>
        <header>产地</header>
        <p class="placeholder daterange">请输入</p>
        <header>计量单位</header>
        <p class="placeholder daterange">请输入</p>
        <header>商品单价</header>
        <p class="placeholder daterange">请输入</p>
        <header>供应数量</header>
        <p class="placeholder daterange">请输入</p>
        <header>交货方式</header>
        <p class="placeholder daterange">请输入</p>
        <header>交割地</header>
        <p class="placeholder daterange">请输入</p>
        <header class="file">
          化验单
          <img v-oss src="/oa/attachent.png" alt="" />
        </header>
        <div class="fileContent">
          <img v-oss src="/oa/operation.png" class="fileImg" />
          <span>点击上传</span>
        </div>
        <p class="fileInfo">
          仅支持类型为docx/doc/pdf/jpg/png的文件，且文件大小不超过30M
        </p>
        <header>检测报告</header>
        <div class="fileContent">
          <img v-oss src="/oa/operation.png" class="fileImg" />
          <span>点击上传</span>
        </div>
        <p class="fileInfo">
          仅支持类型为docx/doc/pdf/jpg/png的文件，且文件大小不超过30M
        </p>
        <header>其他附件</header>
        <div class="fileContent">
          <img v-oss src="/oa/operation.png" class="fileImg" />
          <span>点击上传</span>
        </div>
        <p class="fileInfo">
          仅支持类型为docx/doc/pdf/jpg/png的文件，且文件大小不超过30M
        </p>
        <header>备注<span class="sizeLength">0/200</span></header>
        <p class="placeholder daterange">请输入</p>
      </template>

      <!-- 竞买挂牌 -->
      <template v-else-if="li.type === 'htToJmgp1'">
        <div class="component-name">竞买挂牌</div>
        <header>交易类型</header>
        <p class="placeholder daterange">请输入</p>
        <header>挂牌标题</header>
        <p class="placeholder daterange">请输入</p>
        <header>报名时间</header>
        <p class="placeholder daterange">
          请选择 <i class="el-icon-arrow-right"></i>
        </p>
        <header>竞价时间</header>
        <p class="placeholder daterange">
          请选择 <i class="el-icon-arrow-right"></i>
        </p>
        <header>商品名称</header>
        <p class="placeholder daterange">请输入</p>
        <header>商品品种</header>
        <p class="placeholder daterange">请输入</p>
        <header>产地</header>
        <p class="placeholder daterange">请输入</p>
        <header>计量单位</header>
        <p class="placeholder daterange">请输入</p>
        <header>起拍单价</header>
        <p class="placeholder daterange">请输入</p>
        <header>供应数量</header>
        <p class="placeholder daterange">请输入</p>
        <header>加价梯度</header>
        <p class="placeholder daterange">请输入</p>
        <header>交割方式</header>
        <p class="placeholder daterange">请输入</p>
        <header>交割地</header>
        <p class="placeholder daterange">请输入</p>
        <header class="file">
          化验单
          <img v-oss src="/oa/attachent.png" alt="" />
        </header>
        <div class="fileContent">
          <img v-oss src="/oa/operation.png" class="fileImg" />
          <span>点击上传</span>
        </div>
        <p class="fileInfo">
          仅支持类型为docx/doc/pdf/jpg/png的文件，且文件大小不超过30M
        </p>
        <header>检测报告</header>
        <div class="fileContent">
          <img v-oss src="/oa/operation.png" class="fileImg" />
          <span>点击上传</span>
        </div>
        <p class="fileInfo">
          仅支持类型为docx/doc/pdf/jpg/png的文件，且文件大小不超过30M
        </p>
        <header>其他附件</header>
        <div class="fileContent">
          <img v-oss src="/oa/operation.png" class="fileImg" />
          <span>点击上传</span>
        </div>
        <p class="fileInfo">
          仅支持类型为docx/doc/pdf/jpg/png的文件，且文件大小不超过30M
        </p>
        <header>备注<span class="sizeLength">0/200</span></header>
        <p class="placeholder daterange">请输入</p>
      </template>

      <!-- 竞卖挂牌 -->
      <template v-else-if="li.type === 'htToJmgp2'">
        <div class="component-name">竞卖挂牌</div>
        <header>交易类型</header>
        <p class="placeholder daterange">请输入</p>
        <header>挂牌标题</header>
        <p class="placeholder daterange">请输入</p>
        <header>报名时间</header>
        <p class="placeholder daterange">
          请选择 <i class="el-icon-arrow-right"></i>
        </p>
        <header>竞价时间</header>
        <p class="placeholder daterange">
          请选择 <i class="el-icon-arrow-right"></i>
        </p>
        <header>商品名称</header>
        <p class="placeholder daterange">请输入</p>
        <header>商品品种</header>
        <p class="placeholder daterange">请输入</p>
        <header>产地</header>
        <p class="placeholder daterange">请输入</p>
        <header>计量单位</header>
        <p class="placeholder daterange">请输入</p>
        <header>起拍单价</header>
        <p class="placeholder daterange">请输入</p>
        <header>供应数量</header>
        <p class="placeholder daterange">请输入</p>
        <header>加价梯度</header>
        <p class="placeholder daterange">请输入</p>
        <header>交割方式</header>
        <p class="placeholder daterange">请输入</p>
        <header>交割地</header>
        <p class="placeholder daterange">请输入</p>
        <header class="file">
          化验单
          <img v-oss src="/oa/attachent.png" alt="" />
        </header>
        <div class="fileContent">
          <img v-oss src="/oa/operation.png" class="fileImg" />
          <span>点击上传</span>
        </div>
        <p class="fileInfo">
          仅支持类型为docx/doc/pdf/jpg/png的文件，且文件大小不超过30M
        </p>
        <header>检测报告</header>
        <div class="fileContent">
          <img v-oss src="/oa/operation.png" class="fileImg" />
          <span>点击上传</span>
        </div>
        <p class="fileInfo">
          仅支持类型为docx/doc/pdf/jpg/png的文件，且文件大小不超过30M
        </p>
        <header>其他附件</header>
        <div class="fileContent">
          <img v-oss src="/oa/operation.png" class="fileImg" />
          <span>点击上传</span>
        </div>
        <p class="fileInfo">
          仅支持类型为docx/doc/pdf/jpg/png的文件，且文件大小不超过30M
        </p>
        <header>备注<span class="sizeLength">0/200</span></header>
        <p class="placeholder daterange">请输入</p>
      </template>

      <!-- 竞买报名 -->
      <template v-else-if="li.type === 'htToJmbm1'">
        <div class="component-name">竞买报名</div>
        <header>交易类型</header>
        <p class="placeholder daterange">请输入</p>
        <header>发布企业</header>
        <p class="placeholder daterange">请输入</p>
        <header>挂牌标题</header>
        <p class="placeholder daterange">请输入</p>
        <header>商品名称</header>
        <p class="placeholder daterange">请输入</p>
        <header>起拍单价</header>
        <p class="placeholder daterange">请输入</p>
        <header>供应数量</header>
        <p class="placeholder daterange">请输入</p>
        <header>交割地</header>
        <p class="placeholder daterange">请输入</p>
        <header>产地</header>
        <p class="placeholder daterange">请输入</p>
        <header>交割方式</header>
        <p class="placeholder daterange">请输入</p>
      </template>

      <!-- 竞卖报名 -->
      <template v-else-if="li.type === 'htToJmbm2'">
        <div class="component-name">竞卖报名</div>
        <header>交易类型</header>
        <p class="placeholder daterange">请输入</p>
        <header>发布企业</header>
        <p class="placeholder daterange">请输入</p>
        <header>挂牌标题</header>
        <p class="placeholder daterange">请输入</p>
        <header>商品名称</header>
        <p class="placeholder daterange">请输入</p>
        <header>起拍单价</header>
        <p class="placeholder daterange">请输入</p>
        <header>供应数量</header>
        <p class="placeholder daterange">请输入</p>
        <header>交割地</header>
        <p class="placeholder daterange">请输入</p>
        <header>产地</header>
        <p class="placeholder daterange">请输入</p>
        <header>交割方式</header>
        <p class="placeholder daterange">请输入</p>
      </template>

      <!-- 合同审批 -->
      <template v-else-if="li.type === 'htToHt'">
        <div class="component-name">合同审批</div>
        <header>合同名称</header>
        <p class="placeholder daterange">请输入</p>
        <header>合同编号</header>
        <p class="placeholder daterange">请输入</p>
        <header>发起方单位</header>
        <p class="placeholder daterange">请输入</p>
        <header>对方单位</header>
        <p class="placeholder daterange">请输入</p>
        <header>用章类型</header>
        <p class="placeholder daterange">请输入</p>
        <header>备注<span class="sizeLength">0/150</span></header>
        <p class="placeholder daterange">请输入</p>
        <header class="file">
          附件

          <img v-oss src="/oa/attachent.png" alt="" />
        </header>
        <div class="fileContent">
          <img v-oss src="/oa/operation.png" class="fileImg" />
          <span>点击上传</span>
        </div>
        <p class="fileInfo">
          仅支持类型为docx/doc/pdf/jpg/png的文件，且文件大小不超过30M
        </p>
      </template>

      <!-- 云盾相关 -->
      <div v-else-if="li.type.startsWith('yd-')" style="margin-bottom: 16px">
        {{ li.name }}
      </div>

      <template v-else>
        <el-form-item
          :class="{ isrequired: li.valueJson.required }"
          :label="li.valueJson.name"
        >
          <template v-if="li.type === 'idcard'">
            <el-input
              :placeholder="li.valueJson.placeholder"
              :maxlength="18"
              show-word-limit
            >
            </el-input>
          </template>
          <template v-else-if="li.type === 'phone'">
            <el-input
              :placeholder="li.valueJson.placeholder"
              :maxlength="li.valueJson.phoneType === 3 ? 11 : 13"
              show-word-limit
            >
            </el-input>
          </template>
          <template v-else>
            <el-input
              :placeholder="li.valueJson.placeholder"
              :maxlength="li.valueJson.maxLength"
              :show-word-limit="li.type === 'input'"
            >
            </el-input>
          </template>
        </el-form-item>
      </template>
      <img
        v-if="id === li.id"
        v-oss
        @click.stop="delectList(i)"
        src="/oa/list-close.png"
      />
    </div>
    <div v-show="!list.length" class="empty">
      <img v-if="isTip" v-oss src="/oa/pic_design_default.png" />
      <span>{{ isTip ? '请选择控件拖到此处' : '请拖入多个组件' }}</span>
    </div>
  </draggable>
</template>
<script>
  import draggable from 'vuedraggable';
  import { deepClone } from '@/util/util.js';

  export default {
    name: 'FormCenterType',
    components: { draggable },
    props: {
      isTip: {
        type: Boolean,
        default: true
      },
      children: {
        type: Array,
        default: () => []
      }
    },
    data() {
      return {
        list: [],
        listLength: -1,
        selectIcon: [
          'radio',
          'checkbox',
          'date',
          'people',
          'dept',
          'area',
          'replacecard',
          'provinceType',
          'relationApply'
        ]
      };
    },
    computed: {
      errorObj() {
        return this.$store.state.oaSetUp.oaComponentsErrorList;
      },
      isErrorList() {
        return (id) => {
          const list = this.errorObj;
          return (
            list[id] && list[id].length && this.$store.state.oaSetUp.isValidate
          );
        };
      },
      id: {
        get() {
          return this.$store.state.oaSetUp.oaComponentsId;
        },
        set(v) {
          this.$store.commit('SET_COMPONENTS_ID', v);
        }
      }
    },
    watch: {
      children: {
        handler() {
          this.list = this.children;
        },
        deep: true,
        immediate: true
      },
      isTip() {
        this.list = this.children;
      },
      list: {
        handler() {
          // listLength存上一次更新长度，数组元素增加时触发
          if (this.listLength < this.list.length) {
            const index = this.list.findIndex(({ id }) => !id);
            if (index > -1) {
              this.list = deepClone(this.list);
              // 更新选中状态
              this.id = `${this.list[index].type}${new Date().getTime()}`;
              this.list[index].id = this.id;

              // 更新右侧数据
              this.updateParentCell(this.list[index]);
            }
          }
          this.listLength = this.list.length;
          if (this.isTip) {
            this.$emit('setList', this.list);
          } else {
            this.$emit('update:children', this.list);
          }
        },
        deep: true
      }
    },
    methods: {
      onMove(e) {
        this.clickComponent(e.draggedContext.element);
        // 禁止表单拖拽到左侧
        if (!e.to.className) {
          return false;
        }

        // 禁止表单控件组拖拽到表单
        const type = e.draggedContext.element.type;
        const noCellToForm = [
          'form',
          'rest',
          'replacecard',
          'work',
          'out',
          'trip',
          'turnFormal',
          'leave'
        ];

        // 计算公式拖拽到明细中，清空其他明细
        if (type === 'computed' && e.to.className.indexOf('formContent') > -1) {
          const elementObj = e.draggedContext.element;
          this.setValueJson(this.list, elementObj);
        }

        return !(
          noCellToForm.includes(type) &&
          e.to.className.indexOf('formContent') > -1
        );
      },
      setValueJson(list, currentObj) {
        list.forEach((d) => {
          if (d.type === 'computed') {
            const isClear = d.valueJson.formula.some(
              (f) => f.value === currentObj.id
            );
            if (isClear) {
              d.valueJson.formula = [];
              d.valueJson.formulaInfo = '';
            }
          } else if (d.type === 'form') {
            this.setValueJson(d.children, currentObj);
          }
        });
      },
      delectList(i) {
        const currentObj = this.list[i];
        // 删除时，如果当前项被计算公式引用，需要清空对应计算公式里面的值
        if (
          ['inputMoney', 'inputNumber', 'computed'].includes(currentObj.type)
        ) {
          this.setValueJson(this.list, currentObj);
        }

        this.$store.commit('SET_DELETE_DESIGN_OBJ', this.list[i]);
        this.$store.commit('DELETE_ERROR_LIST', this.list[i].id);
        this.list.splice(i, 1);
        this.$emit('selectCell');
      },
      updateParentCell(obj) {
        this.$emit('selectCell', obj);
      },
      clickComponent(li) {
        this.id = li.id;
        this.updateParentCell(li);
      }
    }
  };
</script>
<style lang="scss">
  .formDesignWeb {
    flex: 1;
    overflow-y: auto;

    // background-color: #fff;
    border-radius: 2px;

    // padding: 0px !important;
    .empty {
      display: flex;
      flex-direction: column;
      align-items: center;

      img {
        width: 64px;
        height: 64px;
        margin-top: 141px;
        margin-bottom: 23px;
      }
    }

    .formInfo {
      padding-right: 0 !important;
      padding-left: 0 !important;

      button {
        width: 100%;
        color: #3c7cff;
      }
    }

    .formList {
      position: relative;
      margin-bottom: 10px;
      padding: 16px 68px 0 0;
      background-color: #fff;
      border: 1px solid #fff;
      cursor: all-scroll;

      .form-modal {
        position: absolute;
        top: 0;
        left: 0;
        z-index: 1;
        width: 100%;
        height: 100%;
      }

      .isrequired {
        .el-form-item__label {
          &::before {
            margin-right: 4px;
            color: #ff5151;
            content: '*';
          }
        }
      }

      .el-input-number,
      .el-date-editor {
        width: 100%;
      }

      .file-btn {
        width: 100px;
        height: 32px;
        line-height: 32px;
        text-align: center;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
      }

      .date-apm {
        display: flex;
        justify-content: space-between;

        .el-select {
          width: 130px;
        }

        .el-date-editor {
          width: calc(100% - 138px);
        }
      }

      button {
        cursor: all-scroll;
      }

      .capital {
        margin-bottom: 10px !important;
      }

      .sortable-chosen {
        cursor: all-scroll !important;
      }

      p {
        margin-bottom: 0 !important;
      }

      .component-name {
        height: 28px;
        color: #999;
        line-height: 28px;
      }

      .addBtn {
        width: 100%;
        color: #3c7cff;
      }

      .blueInfo {
        color: #3b7cff;
      }

      .redInfo {
        color: #ff5151;
      }

      .daterange {
        margin-bottom: 27px !important;
      }

      .textareaContent {
        padding-bottom: 40px !important;
      }

      .el-icon-arrow-right {
        float: right;
      }

      .imageContent {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 80px;
        height: 80px;
        margin-top: 10px;
        border: 1px dashed #d9d9d9;
        border-radius: 4px;

        .icon-plus-0 {
          color: #999;
          font-size: 24px;
        }

        .word {
          color: #999;
          line-height: 22px;
        }
      }

      .fileContent {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 54px;
        margin-top: 19px;
        border: 1px dashed #cfcfcf;
        border-radius: 4px;

        .empty {
          line-height: 64px;
        }
      }

      header {
        margin-top: 17px;

        &:first-child {
          margin-top: 0;
        }

        img {
          position: inherit;
        }

        .sizeLength {
          float: right;
          color: #cfcfcf;
        }
      }

      .days {
        margin-top: 13px;
      }

      img {
        position: absolute;
        top: -1px;
        right: -1px;
        z-index: 2;
        width: 20px;
        height: 20px;
        cursor: pointer;
      }

      .importTag {
        color: #ff5151;
      }

      .placeholder {
        margin-top: 15px;
        padding-bottom: 10px;
        color: #cfcfcf;
        font-size: 16px;
        line-height: 24px;

        &:last-of-type {
          margin-bottom: 0 !important;
        }

        .icon-QER {
          float: right;
          color: #5d92ff;
        }
      }

      .form {
        padding-left: 20px;
        text-align: left;
      }

      .fileImg {
        position: inherit;
        width: 20px;
        height: 24px;
        margin-right: 8px;
      }

      .file {
        display: flex;
        align-items: center;

        .importTag {
          margin-left: 3px;
        }

        img {
          width: 13px;
          height: 14px;
          margin-left: 3px;
        }
      }

      .textarea {
        padding-bottom: 40px !important;
      }

      .textAreaInfo {
        float: right;
        color: #cfcfcf;
      }

      .fileInfo {
        margin-top: 10px;
        color: #3c7cff;
        text-align: center;
        transform: scale(0.9);
      }

      .form-tablecont {
        border: 1px solid #d9d9d9;
        border-radius: 4px;

        .title {
          height: 46px;
          padding-left: 12px;
          line-height: 46px;
          border-bottom: 1px solid #d9d9d9;
        }
      }

      .form-table-btn {
        font-size: 14px;
      }

      .formContent {
        padding: 19px 0;
        color: #cfcfcf;
        font-size: 16px;
        box-shadow: inherit;

        header {
          color: #333;
        }

        .empty {
          line-height: 64px;
        }
      }

      button {
        padding-bottom: 0;
      }
    }

    .bottomDistance {
      padding-bottom: 16px;
    }

    .componentGroup .placeholder {
      margin-bottom: 0 !important;
      border-bottom: 1px solid rgba(0, 0, 0, 5%);
    }

    & > .tips {
      color: #333;
      background: #f0f2f5;
      border-color: #f0f2f5;
    }

    .active {
      border-color: #409eff;
    }

    .error {
      border-color: #ff5151;
    }
  }
</style>
