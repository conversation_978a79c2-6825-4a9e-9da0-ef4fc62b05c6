<template>
  <div class="rightDataHoliday">
    <div class="topType holidayHeader">
      <span>假期类型</span>
      <el-button @click="change" type="text">修改</el-button>
    </div>
    <div class="content topType headerBack">
      <span>假期类型</span>
      <span>最小请假单位</span>
    </div>
    <div v-for="li of holidayList" :key="li.id" class="content topType">
      <span>{{ li.name }}</span>
      <span>{{ li.smallLeaveUnitStr }}</span>
    </div>
    <p>根据假期类型中设置的规则进行计算</p>
  </div>
</template>
<script>
  import { leaveRule } from '@/api/desk/rules';

  export default {
    data() {
      return {
        holidayList: []
      };
    },
    created() {
      leaveRule().then((res) => {
        if (res && res.data && res.data.success) {
          this.holidayList = res.data.data;
        }
      });
    },
    methods: {
      change() {
        const jump = this.$router.resolve({
          name: 'holidayType'
        });
        this.$router.push(jump.location);
        // window.open(`/#${jump.resolved.fullPath}`, '__blank');
      }
    }
  };
</script>
<style lang="scss">
  .rightDataHoliday {
    margin-top: 16px;
    padding: 0 24px;

    & > div:last-of-type {
      border-bottom: 1px solid #d9d9d9;
      border-radius: 0 0 4px 4px;
    }

    .headerBack {
      background: #fafafa;
      border-radius: 4px 4px 0 0;
    }

    .holidayHeader {
      margin-bottom: 16px;
    }

    .topType {
      display: flex;
      justify-content: space-between;
      line-height: 14px;

      button {
        padding: 0;
      }
    }

    .content {
      height: 46px;
      padding: 0 12px;
      line-height: 46px;
      border: 1px solid #d9d9d9;
      border-bottom: 0;
    }

    p {
      margin-top: 16px;
      color: #999;
      line-height: 14px;
    }
  }
</style>
