<template>
  <base-echart
    class="fetr-echart"
    width="100%"
    height="100%"
    :option="option"
  />
</template>

<script>
  import BaseEchart from './base-echart.vue';

  export default {
    components: {
      BaseEchart
    },
    data() {
      return {
        option: {
          title: {
            text: '',
            x: 'center',
            y: 20,
            // textAlign: 'left',
            textStyle: {
              fontSize: 16,
              fontStyle: 'normal',
              fontWeight: 'normal',
              color: '#04edf9'
            }
          },
          // tooltip: {
          //   trigger: 'item',
          //   formatter: function (arg) {
          //     return arg.data.unit;
          //   }
          // },
          series: [
            {
              type: 'gauge',
              center: ['50%', '70%'],
              startAngle: 200,
              endAngle: -20,
              min: 0,
              max: 100,
              splitNumber: 8,
              itemStyle: {
                color: '#02a7f0'
              },
              progress: {
                show: true,
                width: 10
              },
              pointer: {
                show: false
              },
              axisLine: {
                lineStyle: {
                  width: 10
                }
              },
              axisLabel: {
                show: false
              },
              splitLine: {
                show: false
              },
              axisTick: {
                show: false
              },
              detail: {
                show: false,
                valueAnimation: true,
                width: '50%',
                lineHeight: 40,
                borderRadius: 8,
                offsetCenter: [0, '-15%'],
                fontSize: 20,
                fontWeight: 'bolder',
                formatter: '{value}',
                color: 'inherit'
              },
              data: [
                {
                  value: 20
                }
              ]
            }
          ]
        }
      };
    }
    // mounted() {
    //   setTimeout(() => {
    //     this.option = {
    //       title: {
    //         text: 'World Population'
    //       },
    //       tooltip: {
    //         trigger: 'axis',
    //         axisPointer: {
    //           type: 'shadow'
    //         }
    //       },
    //       legend: {},
    //       grid: {
    //         left: '3%',
    //         right: '4%',
    //         bottom: '3%',
    //         containLabel: true
    //       },
    //       xAxis: {
    //         type: 'value',
    //         boundaryGap: [0, 0.01]
    //       },
    //       yAxis: {
    //         type: 'category',
    //         data: ['Brazil', 'Indonesia', 'USA', 'India', 'China', 'World']
    //       },
    //       series: [
    //         {
    //           name: '2011',
    //           type: 'bar',
    //           data: [18203, 23489, 29034, 104970, 131744, 630230]
    //         },
    //         {
    //           name: '2012',
    //           type: 'bar',
    //           data: [19325, 23438, 31000, 121594, 134141, 681807]
    //         }
    //       ]
    //     };
    //   }, 3000);
    // }
  };
</script>

<style scoped></style>
