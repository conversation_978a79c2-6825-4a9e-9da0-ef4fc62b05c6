<template>
  <div class="relevanceApply">
    <h-select
      v-model="applyModel"
      :data-source="applyList"
      :props="DeptProps"
      placeholder="请选择关联审批"
    ></h-select>
  </div>
</template>
<script>
  import { definitionList } from '@/api/flow/process';
  export default {
    props: {
      value: {
        type: String,
        default: ''
      }
    },
    data() {
      return {
        applyModel: '',
        DeptProps: {
          value: 'id',
          label: 'processName'
        },
        applyList: []
      };
    },
    watch: {
      applyModel() {
        this.$emit('input', this.applyModel);
      }
    },
    created() {
      this.applyModel = this.value;
      definitionList({ id: this.$route.query.id, isForm: 0 }).then((res) => {
        if (res && res.data && res.data.success) {
          this.applyList = res.data.data;
        }
      });
    }
  };
</script>
