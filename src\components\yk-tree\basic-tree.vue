<template>
  <div>
    <div class="search-box">
      <el-input
        clearable
        placeholder="请输入检索组织"
        v-model.trim="query"
        @change="queryFn"
      >
        <i slot="suffix" class="el-input__icon el-icon-search" />
      </el-input>
      <el-button
        plain
        type="primary"
        @click="appendNode"
        style="margin-left: 6px"
        >新增</el-button
      >
    </div>
    <el-tree
      ref="tree"
      v-if="status"
      node-key="id"
      empty-text="暂无数据"
      lazy
      :load="loadNode"
      :props="defaultProps"
      :default-expanded-keys="queryExpanded"
      :expand-on-click-node="false"
      @node-click="handleNodeClick"
    >
      <span class="custom-tree-node show-hide" slot-scope="{ node, data }">
        <span>{{ node.label }}</span>
        <span style="display: none" v-if="isShow">
          <i
            class="el-icon-edit tree_icon"
            title="编辑"
            @click.stop="() => editNode(node, data)"
          ></i>
          <i
            class="el-icon-delete tree_icon"
            title="删除"
            style="color: red"
            @click.stop="() => removeNode(node, data)"
          ></i>
        </span>
      </span>
    </el-tree>
    <model v-model="open" :id="id" />
  </div>
</template>

<script>
  import Model from './components/model';
  import { treeListData } from './data';
  import { throttle } from 'lodash';

  export default {
    name: 'BasicTree',
    components: {
      Model
    },
    props: {
      // 一并删除子级
      rmChild: {
        type: Boolean,
        default: false
      },
      // 展示操作icon
      isShow: {
        type: Boolean,
        default: false
      },
      // 展开的ids
      queryExpanded: {
        type: Array,
        default() {
          return [];
        }
      }
    },
    data() {
      return {
        list: [],
        defaultProps: {
          isLeaf: 'leaf'
        },
        input: '',
        open: false,
        id: '',
        status: false,
        query: ''
      };
    },
    mounted() {
      this.init();
    },
    methods: {
      // 初始化
      init() {
        // 模拟接口
        const treeData = treeListData;
        const treeMap = {};
        for (let d of treeData) {
          let parentId = d['parentId'];
          if (!treeMap[parentId]) {
            treeMap[parentId] = [d];
          } else {
            treeMap[parentId].push(d);
          }
        }
        this.list = treeMap;
        this.status = true;
      },
      // 懒加载
      loadNode(node, resolve) {
        let list = [];
        if (node.level === 0) {
          list = this.list.ROOT;
        }
        if (node.level >= 1) {
          const isBool = this.queryExpanded.some((num) => num === node.data.id);
          if (isBool) {
            list = this.tree[node.data.id].filter((item) => {
              const bool = this.queryExpanded.some((num) => num === item.id);
              if (!bool && item.label.includes(this.query)) return item;
              if (bool) return item;
            });
          } else {
            list = this.list[node.data.id];
          }
        }
        return resolve(list);
      },
      // 点击节点
      handleNodeClick(obj, node) {
        console.log('obj-node', obj, node);
      },
      // 新增节点
      appendNode() {
        console.log('新增');
        this.id = '';
        this.open = true;
      },
      // 编辑节点
      editNode(obj, node) {
        console.log('编辑', obj, node);
        this.id = node.id;
        this.open = true;
      },
      // 删除节点
      removeNode(node, data) {
        console.log('删除', node, data);
        let message;
        if (this.rmChild) {
          message = `此操作将永久删除【${data.label}】及其子级节点, 是否继续?`;
        } else {
          if (node.data.leaf) {
            message = `此操作将永久删除【${data.label}】, 是否继续?`;
          } else {
            message = `此节点【${data.label}】下存在子节点, 请先删除子级节点`;
            this.$alert(message, '提示', {
              confirmButtonText: '确定',
              type: 'warning',
              callback: () => {}
            });
            return;
          }
        }

        this.$confirm(message, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.$message({
              type: 'success',
              message: '删除成功!'
            });
          })
          .catch(() => {});
      },
      // 查询
      queryFn: throttle(
        function () {
          // eslint-disable-next-line no-empty
          if (this.query.length) {
          }
        },
        2000,
        {
          throttle: false
        }
      )
    }
  };
</script>

<style lang="scss" scoped>
  .custom-tree-node {
    display: flex;
    flex: 1;
    align-items: center;
    justify-content: space-between;
    padding-right: 8px;
    font-size: 14px;
  }

  .show-hide:hover :nth-child(2) {
    display: inline-block !important;
  }

  .tree_icon {
    padding-left: 8px;
  }

  .search-box {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
  }
</style>
