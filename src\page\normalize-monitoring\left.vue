<template>
  <div class="left-monitoring-container">
    <div class="left-search">
      <div class="search-item">
        <span>年份：</span>
        <el-select
          v-model="form.year"
          filterable
          placeholder="请选择"
          style="width: 100px"
          clearable
        >
          <el-option
            v-for="dict in yearList"
            :key="dict.key"
            :label="dict.name"
            :value="dict.key"
          ></el-option>
        </el-select>
      </div>
      <div class="search-item">
        <span>信息系统：</span>
        <el-select
          v-model="form.year"
          filterable
          placeholder="请选择"
          style="width: 100%"
          clearable
        >
          <el-option
            v-for="dict in yearList"
            :key="dict.key"
            :label="dict.name"
            :value="dict.key"
          ></el-option>
        </el-select>
      </div>
    </div>
    <div class="left-content">
      <div class="left-content-item">
        <div class="content-title">登录人数</div>
        <div class="chart-wrap">
          <CurrentLoggedEchart></CurrentLoggedEchart>
        </div>
      </div>
      <div class="left-content-item">
        <div class="content-title">登录率</div>
        <div class="chart-wrap">
          <FetrEchart></FetrEchart>
        </div>
      </div>
      <div class="left-content-item">
        <div class="content-title">活跃人数</div>
        <div class="chart-wrap">
          <LiveEchart></LiveEchart>
        </div>
      </div>
      <div class="left-content-item">
        <div class="content-title">活跃率</div>
        <div class="chart-wrap">
          <ReachEchart></ReachEchart>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
  import {
    CurrentLoggedEchart,
    FetrEchart,
    LiveEchart,
    ReachEchart
  } from './components';
  export default {
    components: { CurrentLoggedEchart, FetrEchart, LiveEchart, ReachEchart },
    name: 'normalize-monitoring',
    data() {
      return {
        form: {},
        yearList: []
      };
    },
    methods: {}
  };
</script>

<style scoped lang="scss">
  @import './css/left';
</style>
