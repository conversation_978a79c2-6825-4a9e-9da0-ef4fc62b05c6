<template>
  <el-dialog
    @open="search"
    title="选择文件"
    :visible.sync="templateVisible"
    width="1000px"
    :before-close="handleClose"
    append-to-body
    class="templateDialog"
  >
    <el-tabs v-model="templateType" @tab-click="search">
      <el-tab-pane label="企业模板" name="1">
        <span>已选 : {{ selectObj.modelFullName || '--' }}</span>
        <template-list
          @update="search"
          @update-radio="updateData"
          :list="list"
          :radio-id="radioId"
          :type="templateType"
          :is-edit="false"
        ></template-list>
      </el-tab-pane>
      <el-tab-pane label="系统模板" name="2">
        <span>已选 : {{ selectObj.modelFullName || '--' }}</span>
        <template-list
          @update="search"
          @update-radio="updateData"
          :list="list"
          :radio-id="radioId"
          :type="templateType"
          :is-edit="false"
        ></template-list>
      </el-tab-pane>
    </el-tabs>
    <div slot="footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button @click="submit" type="primary">确定</el-button>
    </div>
  </el-dialog>
</template>
<script>
  import templateList from './templateList';
  import { listTree } from '@/api/resource/contractModel';

  export default {
    components: { templateList },
    props: {
      templateVisible: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        searchLoading: false,
        templateType: '1',
        list: [],
        radioId: '',
        selectObj: {}
      };
    },
    methods: {
      search() {
        this.searchLoading = true;
        listTree(this.templateType, 0)
          .then((res) => {
            if (res && res.data && res.data.success) {
              this.list = res.data.data;
            }
          })
          .finally(() => {
            this.searchLoading = false;
          });
      },
      updateData({ id, selectObj }) {
        this.radioId = id;
        this.selectObj = selectObj;
      },
      submit() {
        const { modelFullName, modelUrl, id, category } = this.selectObj;
        const obj = {
          docName: modelFullName,
          fileUrl: modelUrl,
          id,
          optionTime: category
        };
        this.$emit('update-temlate', { [`${this.radioId}_${category}`]: obj });
        this.handleClose();
      },
      handleClose() {
        this.radioId = '';
        this.selectObj = {};
        this.$emit('update:templateVisible', false);
      }
    }
  };
</script>
<style lang="scss">
  .templateDialog {
    .el-tabs__header {
      margin-bottom: 16px;
    }

    .template-content {
      height: 377px;
      overflow: auto;
    }
  }
</style>
