import request from '@/router/axios';
// 申报截止日期设置分页
export const getList = (params) => {
  return request({
    url: '/api/deadline/projectdeadline/page',
    method: 'get',
    params
  });
};
// 申报截止日期设置保存
export const add = (data) => {
  return request({
    url: '/api/deadline/projectdeadline/save',
    method: 'post',
    data
  });
};
// 获取最新的截止日期记录
export const getDetailById = (params) => {
  return request({
    url: '/api/deadline/projectdeadline/latest',
    method: 'get',
    params
  });
};
// 生成项目编号
export const createBatchNo = (data) => {
  return request({
    url: '/api/project_base/generateProjectNo',
    method: 'post',
    data
  });
};
