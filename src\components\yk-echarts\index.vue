<template>
  <div :id="id" class="chart" :style="{ height: height, width: width }" />
</template>

<script>
  import tdTheme from './theme.json'; // 引入默认主题
  import resizeMixins from '@/util/resizeMixins';
  import { mapGetters } from 'vuex';

  export default {
    name: 'echart',
    mixins: [resizeMixins],
    props: {
      id: {
        type: String,
        default: 'chart'
      },
      width: {
        type: String,
        default: '100%'
      },
      height: {
        type: String,
        default: '100%'
      },
      options: {
        type: Object,
        default: () => ({})
      },
      theme: {
        type: Boolean,
        default: true
      }
    },
    data() {
      return {
        chart: null
      };
    },
    watch: {
      options: {
        handler(options) {
          // 设置true清空echart缓存
          this.chart.setOption(options, true);
        },
        deep: true
      }
    },
    mounted() {
      this.$echarts.registerTheme('tdTheme', tdTheme); // 覆盖默认主题
      this.initChart();
    },
    methods: {
      initChart() {
        // 初始化echart
        const themeName = this.theme ? 'tdTheme' : undefined;
        this.chart = this.$echarts.init(this.$el, themeName);
        this.chart.setOption(this.options, true);
        this.$store.commit('ADD_COCKPIT_CHART', this.chart);
      }
    },
    computed: {
      ...mapGetters(['cockpitChartsList'])
    }
  };
</script>

<style lang="scss" scoped>
  .chart {
    transform: scale(0.909);
    transform-origin: 0 0;
    zoom: 1.1;
  }
</style>
