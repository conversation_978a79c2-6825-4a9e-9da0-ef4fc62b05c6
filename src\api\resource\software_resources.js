import request from '@/router/axios';

export const getList = (params) => {
  return request({
    url: '/api/software/page',
    method: 'post',
    data: {
      ...params
    }
  });
};

export const getSelfList = (current, size, params) => {
  return request({
    url: '/api/software/self/page',
    method: 'post',
    data: {
      ...params,
      current,
      size
    }
  });
};

export const deleteById = (data) => {
  return request({
    url: '/api/software/delete',
    method: 'post',
    data
  });
};

export const detailById = (id) => {
  return request({
    url: `/api/software/detail?id=${id}`,
    method: 'get'
  });
};

export const save = (data) => {
  return request({
    url: '/api/software/save',
    method: 'post',
    data
  });
};

export const softwareExport = (data) => {
  return request({
    url: `/api/software/export`,
    method: 'post',
    responseType: 'blob',
    data
  });
};

// 下载模板
export const getExportTemplate = () => {
  return request({
    url: `/api/software/exportTemplate`,
    method: 'get',
    responseType: 'blob'
  });
};

export const setStatus = (data) => {
  return request({
    url: `/api/software/status`,
    method: 'post',
    data
  });
};
