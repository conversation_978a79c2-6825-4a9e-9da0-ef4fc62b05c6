export default {
  install(Vue) {
    Vue.prototype.alert = function (msg) {
      return new Promise((resolve) => {
        this.$alert(msg, '提示', {
          confirmButtonText: '确定',
          callback: () => {
            resolve();
          }
        });
      });
    };
    Vue.prototype.confirm = function (
      msg = '确认操作?',
      title = '提示',
      closeOnClickModal = true,
      showCancelButton = true
    ) {
      return new Promise((resolve, reject) => {
        this.$confirm(msg, title, {
          showCancelButton,
          closeOnClickModal,
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            resolve();
          })
          .catch((e) => {
            reject(e);
          });
      });
    };
  }
};
