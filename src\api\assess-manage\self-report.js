import request from '@/router/axios';
// 列表
export const getPage = (data) => {
  return request({
    url: '/api/rw-manage/self/page',
    method: 'post',
    data
  });
};
// 专项检查-保存
export const saveFetch = (data) => {
  return request({
    url: '/api/rw-manage/self/save',
    method: 'post',
    data
  });
};
// 专项检查-提交
export const submitFetch = (data) => {
  return request({
    url: '/api/rw-manage/self/submit',
    method: 'post',
    data
  });
};

// 根据ID获取数据
export const getDetail = (params) => {
  return request({
    url: `/api/rw-manage/detail`,
    method: 'get',
    params
  });
};
