import request from '@/router/axios';

export const getList = (params) => {
  return request({
    url: '/api/software-use/page',
    method: 'post',
    data: {
      ...params
    }
  });
};

export const deleteById = (data) => {
  return request({
    url: '/api/software-use/delete',
    method: 'post',
    data
  });
};

export const detailById = (id) => {
  return request({
    url: `/api/software-use/detail?id=${id}`,
    method: 'get'
  });
};

export const save = (data) => {
  return request({
    url: '/api/software-use/save',
    method: 'post',
    data
  });
};

// 根据部门ID查询二级部门 - 公司名称
export const getCompanyName = (deptId) => {
  return request({
    url: `/api/szyk-system/dept/two-dept`,
    method: 'get',
    params: { deptId }
  });
};
