import request from '@/router/axios';
// 列表
export const getPage = (data) => {
  return request({
    url: '/api/rw-manage/process/page',
    method: 'post',
    data
  });
};
// 过程考核导出
export const exportExcel = (data) => {
  return request({
    url: '/api/rw-manage/exportProcess',
    method: 'post',
    responseType: 'blob',
    data
  });
};
// 过程考核-提交
export const submitFetch = (data) => {
  return request({
    url: '/api/rw-manage/process/submit',
    method: 'post',
    data
  });
};

// 根据ID获取数据
export const getDetail = (params) => {
  return request({
    url: `/api/rw-manage/detail`,
    method: 'get',
    params
  });
};
// 过程考核-删除 批量
export const batchDelete = (data) => {
  return request({
    url: '/api/rw-manage/process/delete',
    method: 'post',
    data
  });
};
// 评分方法-分页
export const getAssessMethod = (data) => {
  return request({
    url: '/api/rw-scheme-target/method/page',
    method: 'post',
    data
  });
};
// 评分方法-拼接数据-排序好的数据
export const getSortAssessMethod = (data) => {
  return request({
    url: '/api/rw-scheme-target/method/detail',
    method: 'post',
    data
  });
};
// 被考核记录的分页查询
export const assessedPage = (data) => {
  return request({
    url: '/api/rw-manage/assessedPage',
    method: 'post',
    data
  });
};
// 被考核记录导出
export const exportAssessed = (data) => {
  return request({
    url: '/api/rw-manage/exportAssessed',
    method: 'post',
    responseType: 'blob',
    data
  });
};
