// 混入代码 resize-mixins.js
import { debounce } from 'lodash';
const resizeChartMethod = '$__resizeChartMethod';
import store from '@/store';

export default {
  created() {
    window.addEventListener('resize', this[resizeChartMethod], false);
  },
  activated() {
    // 防止 keep-alive 之后图表变形
    this[resizeChartMethod];
  },
  beforeDestroy() {
    window.removeEventListener('reisze', this[resizeChartMethod]);
  },
  methods: {
    // 防抖函数来控制 resize 的频率
    [resizeChartMethod]: debounce(function () {
      const chartsList = store.getters.cockpitChartsList;
      console.log(chartsList.length);
      if (chartsList.length) {
        chartsList.forEach((chart) => {
          chart && chart.resize();
        });
      }
    }, 300)
  }
};
