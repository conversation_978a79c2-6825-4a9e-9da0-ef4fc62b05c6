<template>
  <div>
    <el-table
      :data="detail.stageList"
      border
      stripe
      :header-cell-style="{ backgroundColor: '#fafafa' }"
      style="width: 100%"
    >
      <el-table-column label="序号" type="index" align="center" width="50">
      </el-table-column>
      <!-- <el-table-column prop="memberType" align="center">
        <template slot="header"> 成员类型 </template>
        <template slot-scope="{ row }">
          {{ row.memberTypeName || '--' }}
        </template>
      </el-table-column> -->
      <el-table-column show-overflow-tooltip prop="memberName" align="center">
        <template slot="header"> 姓名 </template>
        <template slot-scope="{ row }">
          {{ row.memberName || '--' }}
        </template>
      </el-table-column>
      <el-table-column show-overflow-tooltip prop="unit" align="center">
        <template slot="header"> 单位 </template>
        <template slot-scope="{ row }">
          {{ row.unit || '--' }}
        </template>
      </el-table-column>
      <el-table-column show-overflow-tooltip prop="titles" align="center">
        <template slot="header"> 职称/职务 </template>
        <template slot-scope="{ row }">
          {{ row.titles || '--' }}
        </template>
      </el-table-column>
      <el-table-column show-overflow-tooltip prop="workDivision" align="center">
        <template slot="header"> 责任分工 </template>
        <template slot-scope="{ row }">
          {{ row.workDivision || '--' }}
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
<script>
  import { dateFormat } from '@/util/date';
  export default {
    name: 'ProjectLibraryBasicInfo',
    filters: {
      dateFormat(value) {
        if (!value) return '--';
        let val = new Date(value);
        let date = dateFormat(val, 'yyyy-MM-dd');
        return date || '--';
      }
    },
    props: {
      detail: {
        type: Object,
        default: () => {}
      }
    },
    data() {
      return {};
    },
    methods: {}
  };
</script>
<style lang="scss">
  .my-label {
    width: 130px;
    text-align: right;
  }

  .my-content {
    display: flex;
    gap: 5px;
    max-width: 430px;
  }

  .project-label {
    display: flex;
    flex-wrap: wrap;
  }

  .el-descriptions :not(.is-bordered) .el-descriptions-item__cell {
    vertical-align: baseline;
  }
</style>
