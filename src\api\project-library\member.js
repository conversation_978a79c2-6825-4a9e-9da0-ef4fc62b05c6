import request from '@/router/axios';

// 根据项目id查询联系人信息
export const getPlcontact = (projectId) => {
  return request({
    url: `/api/zbusiness-project/plcontact/fetchOne/${projectId}`,
    method: 'get'
  });
};

// 根据项目id查询项目成员列表
export const getPlmember = (projectId) => {
  return request({
    url: `/api/zbusiness-project/plmember/list/${projectId}`,
    method: 'get'
  });
};

export const getDicList = (params) => {
  return request({
    url: '/api/szyk-system/dict-biz/dictionary',
    method: 'get',
    params
  });
};
