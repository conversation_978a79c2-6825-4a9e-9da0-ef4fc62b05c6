<template>
  <el-popover
    placement="bottom"
    width="350"
    trigger="click"
    @show="handleNoticeShow"
    @after-leave="handleNoticeHide"
    popper-class="notice_popper"
  >
    <el-tabs v-model="activeName" class="box_tabs" @tab-click="handleTabClick">
      <el-tab-pane name="IN_APP">
        <span slot="label">
          <el-badge
            :value="countNotReadObj.inAppCount"
            :max="99"
            :hidden="countNotReadObj.inAppCount === 0"
            class="item"
          >
            消息</el-badge
          >
        </span>
      </el-tab-pane>
      <el-tab-pane name="WORK_TODO">
        <span slot="label">
          <el-badge
            :value="countNotReadObj.workToDoCount"
            :max="99"
            :hidden="countNotReadObj.workToDoCount === 0"
            class="item"
          >
            待办</el-badge
          >
        </span>
      </el-tab-pane>
      <el-tab-pane name="WARNING">
        <span slot="label">
          <el-badge
            :value="countNotReadObj.warningCount"
            :max="99"
            :hidden="countNotReadObj.warningCount === 0"
            class="item"
          >
            预警</el-badge
          >
        </span>
      </el-tab-pane>
      <el-tab-pane name="NOTICE">
        <span slot="label">
          <el-badge
            :value="countNotReadObj.noticeCount"
            :max="99"
            :hidden="countNotReadObj.noticeCount === 0"
            class="item"
          >
            通知</el-badge
          >
        </span>
      </el-tab-pane>
    </el-tabs>
    <el-scrollbar style="height: 300px">
      <avue-notice
        v-loading="loading"
        v-if="countNotRead !== 0"
        class="notice_wrapper"
        :data="data"
        :option="option"
        :finish="current >= countNotRead / 10"
        @click="handleView"
        @page-change="pageChange"
      ></avue-notice>
      <el-empty description="暂无消息" v-else></el-empty>
    </el-scrollbar>
    <div class="bottom-btn">
      <el-button type="text" @click="setAllRead" :disabled="countNotRead === 0"
        >全部已读</el-button
      >
      <el-button type="text" @click="$router.push('/push/manage')"
        >查看所有消息 <i class="el-icon-arrow-right"></i
      ></el-button>
    </div>
    <div slot="reference">
      <el-badge is-dot :hidden="countNotReadObj.totalCount === 0">
        <i class="el-icon-bell"></i>
      </el-badge>
    </div>
    <!-- 详情 -->
    <detail
      :formVisible="formVisible"
      :msgId="msgId"
      :hasRead="false"
      :hasReadId="hasReadId"
      @close="handleDialogClose"
    />
  </el-popover>
</template>

<script>
  import {
    getReceivePage,
    getCountNotRead,
    getMsgAllRead,
    getDetail,
    postMsgRead
  } from '@/api/message/message';
  import website from '@/config/website';
  import Detail from '@/views/push/manage/components/detail';

  const typeDic = {
    IN_APP: 'inAppCount',
    WORK_TODO: 'workToDoCount',
    WARNING: 'warningCount',
    NOTICE: 'noticeCount'
  };

  export default {
    name: 'top-notice',
    components: { Detail },
    data() {
      return {
        activeName: 'IN_APP',
        option: {
          props: {
            img: 'img',
            title: 'title',
            subtitle: 'createTime',
            status: 'status'
          }
        },
        loading: false,
        current: 1,
        data: [],
        countNotReadObj: {
          inAppCount: 0,
          noticeCount: 0,
          totalCount: 0,
          warningCount: 0,
          workToDoCount: 0
        },
        // 弹窗属性
        formVisible: false,
        msgId: '',
        hasReadId: ''
      };
    },
    mounted() {
      // if (website.WS.enable) {
      //   this.getMessageData();
      //   // 10秒间隔刷新获取最新消息 2022-06-21
      //   let messageTimer = setInterval(() => {
      //     this.getMessageData();
      //   }, 20000);
      //   this.$on('hook:beforeDestroy', () => {
      //     // 清除定时器
      //     clearInterval(messageTimer);
      //     messageTimer = null;
      //   });
      // }
    },
    methods: {
      // 弹出框显示回调
      handleNoticeShow() {
        this.$forceUpdate();
        if (website.WS.enable) {
          this.getMessageData();
        }
      },
      // 弹出框隐藏回调
      handleNoticeHide() {
        this.data.splice(0, Infinity);
        this.current = 1;
      },
      // tab change事件
      handleTabClick() {
        this.getMessageData();
      },
      // 获取消息数据
      async getMessageData() {
        try {
          // 将页码重置为1
          this.current = 1;
          this.loading = true;
          // 获取分页信息
          const { data: pageData } = await getReceivePage({
            type: this.activeName,
            hasRead: 0
          });
          const data = pageData.data.records;
          this.data = data.map((item) => ({
            img: '/img/bg/message.png',
            id: item.id,
            messageId: item.messageId,
            title: item.title,
            createTime: item.createTime,
            sender: item.sender
          }));
        } catch (e) {
          console.error(e);
        } finally {
          this.loading = false;
        }
        // 获取未读数据
        getCountNotRead().then((res) => {
          this.countNotReadObj = res.data.data;
        });
      },
      async pageChange(page, done) {
        try {
          this.loading = true;
          // 获取分页信息
          this.current++;
          const { data: pageData } = await getReceivePage({
            hasRead: 0,
            current: this.current
          });
          if (pageData.data.records.length === 0) {
            return this.$message.warning('暂无更多数据');
          }
          const data = pageData.data.records.map((item) => ({
            img: '/img/bg/message.png',
            id: item.id,
            messageId: item.messageId,
            title: item.title,
            createTime: item.createTime,
            sender: item.sender
          }));
          this.data = this.data.concat(data);
        } catch (e) {
          console.error(e);
        } finally {
          this.loading = false;
          done();
        }
      },
      // 弹窗显示
      handleView(row) {
        if (this.activeName === 'WORK_TODO') {
          this.getDetail(row);
        } else {
          this.formVisible = true;
          this.msgId = row.messageId;
          this.hasReadId = row.id;
        }
      },
      // 获取待办详情获取url跳转审批详情页面
      async getDetail(row) {
        try {
          const { data = {} } = await getDetail(row.messageId);
          if (data.success) {
            let obj = data.data;
            // 如果消息未读,把消息状态更改为已读
            await postMsgRead({ id: row.id });
            await this.getMessageData();
            this.$router.push(`${obj.url}&tabNum=1`);
            // window.open(`#${obj.url}&tabNum=1`, '_blank');
          }
        } catch (e) {
          console.error(e);
        }
      },
      // 弹窗隐藏
      handleDialogClose() {
        this.getMessageData();
        this.formVisible = false;
        this.msgId = '';
        this.hasReadId = '';
      },
      // 全部已读
      async setAllRead() {
        try {
          const { data } = await getMsgAllRead(this.activeName);
          if (data && data.code == 200) {
            this.$message.success('操作成功');
            this.getMessageData();
          }
        } catch (e) {
          console.error(e);
        }
      }
    },
    computed: {
      countNotRead() {
        const currentTab = typeDic[this.activeName];
        return this.countNotReadObj[currentTab];
      }
    }
  };
</script>

<style lang="scss">
  .notice_popper {
    padding: 0;

    .box_tabs {
      .el-tabs__header {
        margin: 0;

        .el-tabs__nav {
          transform: translateX(24px) !important;

          .el-tabs__item {
            height: 50px;
            line-height: 50px;

            .item .el-badge__content {
              top: 10px;
              height: 15px;
              padding: 0 4px;
              line-height: 15px;
            }
          }
        }
      }
    }

    .notice_wrapper {
      .avue-notice__item {
        padding: 8px 24px;
        border-bottom: none;

        .avue-notice__img {
          border-radius: 0;
        }
      }
    }

    .bottom-btn {
      display: flex;
      justify-content: space-between;
      padding: 0 12px;
      border-top: 1px solid #f1f1f1;

      .el-button {
        color: #999;
        font-size: 12px;
      }
    }
  }
</style>
