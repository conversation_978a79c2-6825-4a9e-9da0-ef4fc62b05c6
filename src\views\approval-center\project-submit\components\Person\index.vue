<template>
  <span class="wrapper">
    <!--  弹框  -->
    <Dialog
      :title="modelTitle"
      :visible="visible"
      width="85%"
      custom-class="org_dialog"
      @closed="cancel"
    >
      <div style="height: 500px; margin: 20px 0" v-if="visible">
        <!--      人员检索        -->
        <el-form
          ref="form"
          :model="queryParams"
          :inline="true"
          label-suffix="："
        >
          <el-form-item label="姓名" prop="userName">
            <el-input
              v-model.trim="queryParams.userName"
              placeholder="请输入"
              clearable
              :maxlength="20"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="handleQuery"
              >搜索</el-button
            >
            <el-button icon="el-icon-delete" @click="resetQuery"
              >清空</el-button
            >
          </el-form-item>
        </el-form>
        <!--   选择操作区域     -->
        <div class="content-wrapper">
          <tree-wrapper left-val="left_person" mid-val="mid_person">
            <template v-slot:left>
              <tree-org-sync
                :treeCheckedData="treeCheckedData"
                :loading="orgLoading"
                @queryPeople="getNode"
                @delPeople="delSelect"
              />
            </template>
            <template v-slot:mid>
              <el-row :gutter="15">
                <el-col :md="18" :lg="18">
                  <div class="list_wrapper">
                    <!--   人员列表   -->
                    <list
                      :loading="loading"
                      :arr="tableData"
                      @selectEmit="selectFn"
                      @selectOnline="selectOnline"
                    />
                    <yk-pagination
                      small
                      layout="total, prev, pager, next, jumper"
                      v-show="total > 0"
                      :total="total"
                      :page.sync="pages.pageNum"
                      :limit.sync="pages.pageSize"
                      :page-sizes="[10, 20]"
                      @pagination="getList"
                    />
                  </div>
                </el-col>
                <el-col :md="6" :lg="6" class="h100">
                  <!--  已选择人员  -->
                  <select-people
                    :loading="selectLoad"
                    :list="personArr"
                    @removeEmit="removeFn"
                  />
                </el-col>
              </el-row>
            </template>
          </tree-wrapper>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="ok">确 定</el-button>
      </span>
    </Dialog>
  </span>
</template>

<script>
  import Dialog from '@/components/basic-dialog';
  import List from './list';
  import TreeOrgSync from './tree-org-person-sync';
  import SelectPeople from './select';
  import { trimAll } from '@/util/util';
  import { getDeptIdList } from '@/api/system/dept';
  import { getExpertList } from '@/api/business-applications/expert-database';
  import TreeWrapper from '@/components/yk-organization-select/components/tree-wrapper';
  import website from '@/config/website';
  const query = {
    userName: ''
  };
  export default {
    name: 'Person',
    components: {
      TreeOrgSync,
      SelectPeople,
      List,
      TreeWrapper,
      Dialog
    },
    model: {
      prop: 'userArr',
      event: 'change'
    },
    props: {
      // 按钮标题
      btnTitle: {
        type: String,
        default: '人员选择'
      },
      // 弹框标题
      modelTitle: {
        type: String,
        default: '选择专家'
      },
      // 是否为圆形按钮
      circle: {
        type: Boolean,
        default: false
      },
      // 选择按钮 icon
      icon: {
        type: String,
        default: 'el-icon-s-custom'
      },
      // 已选人员数组
      userArr: {
        type: Array,
        default: function () {
          return [];
        }
      },
      // 已选部门数组
      treeCheckedData: {
        type: Array,
        default: function () {
          return [];
        }
      },
      // 单选/多选 默认多选
      single: {
        type: Boolean,
        default: false
      },
      appendToBody: {
        type: Boolean,
        default: true
      }
    },
    watch: {
      async visible(val) {
        if (val) {
          // 打开弹框
          let arr = this.userArr || [];
          const ids = arr.map((item) => item.deptId);
          if (ids.length) {
            // 设置返回展开ids
            try {
              const res = await getDeptIdList(
                ids.toString(),
                website.tenantName
              );
              this.expandedArr = res.data.data;
            } catch (e) {
              console.error(e);
            }
          } else {
            this.expandedArr = [];
          }
          // 单选
          if (this.single && arr.length > 1) {
            arr.length = 1;
          }
          // 已选人员ids,存储本地详细信息

          if (arr.length) {
            this.personArr = [...arr];
            this.personArr.forEach((item) => {
              if (!item.contactPerson) {
                item.contactPerson = '';
              }
              if (item.contactPerson.indexOf('WORK') !== -1) {
                this.oneKey = false;
              }
            });
          } else {
            this.personArr = [];
            this.oneKey = true;
          }
        }
      }
    },
    data() {
      return {
        queryParams: { ...query },
        oneKey: true,
        visible: false,
        loading: false,
        resetStatus: true,
        selectLoad: false,
        orgLoading: false,
        total: 0,
        pages: {
          pageNum: 1,
          pageSize: 10
        },
        tableData: [],
        node: null,
        personArr: [],
        expandedArr: [],
        expandedQueryArr: [],
        tree: [],
        treeAll: {}
      };
    },
    methods: {
      // 重置
      resetQuery() {
        this.$refs['form'].resetFields();
        this.handleQuery();
      },
      // 查询
      handleQuery() {
        Object.assign(this.pages, {
          pageNum: 1,
          pageSize: 10
        });
        for (const key in this.queryParams) {
          this.queryParams[key] = trimAll(this.queryParams[key]);
        }
        this.request();
      },

      // 分页查询
      getList({ page, limit }) {
        Object.assign(this.pages, {
          pageNum: page,
          pageSize: limit
        });
        this.request();
      },
      // 取消按钮
      cancel() {
        this.visible = false;
        this.total = 0;
        Object.assign(this.pages, {
          pageNum: 1,
          pageSize: 10
        });
        this.queryParams.userName = '';
        this.tableData = [];
        this.node = null;
      },
      // 确定按钮
      ok() {
        // if (this.personArr.length === 0) {
        //   this.$message({
        //     type: 'warning',
        //     message: '请选择专家'
        //   });
        //   return;
        // }
        this.$emit('save-success', this.personArr);
        this.$emit('tree-checked', this.node.split(','));
        this.dispatch('ElFormItem', 'el.form.change', this.personArr);
        this.cancel();
      },
      // 人员改动
      selectFn(person, status) {
        if (status) {
          // 新增
          if (this.single) {
            this.personArr = [];

            this.tableData.map((item) => {
              item.status = false;
              if (person.userId === item.userId) {
                item.status = true;
              }
              return item;
            });
          }
          this.personArr.push(person);
          this.selectOnline(person, true);
        } else {
          person.onlineStatus = false;
          // 移除
          const index = this.personArr.findIndex(
            (item) => item.userId === person.userId
          );
          if (index !== -1) {
            this.personArr.splice(index, 1);
          }
        }
      },
      selectOnline(person, onlineStatus) {
        if (onlineStatus) {
          if (person.attribute === '2' && !person.userId) {
            let msg = `${person.userName}专家在系统内无用户，如需进行线上评审，请先维护用户。`;
            this.$confirm(msg, '提示', {
              confirmButtonText: '确定',
              showCancelButton: false,
              type: 'warning'
            })
              .then(() => {})
              .catch(() => {});
            person.onlineStatus = false;
            return;
          }
          const row = this.personArr.find(
            (item) => item.userId === person.userId
          );
          row.onlineStatus = true;
          if (!person.status) {
            person.status = true;
            this.selectFn(person, true);
          }
        } else {
          const row = this.personArr.find(
            (item) => item.userId === person.userId
          );
          row.onlineStatus = false;
        }
      },
      // 删除人员
      removeFn(index, id) {
        this.personArr.splice(index, 1);
        // 修改状态
        this.tableData.map((item) => {
          if (id === item.userId) {
            item.status = false;
            item.onlineStatus = false;
          }
          return item;
        });
      },
      // 选择部门
      getNode(node) {
        this.node = node;
        Object.assign(this.pages, {
          pageNum: 1,
          pageSize: 10
        });
        this.request();
      },
      // 如果取消选中部门，相应部门下的选中的人员也取消掉
      delSelect(node) {
        let toBeRemoved = [];
        this.personArr.forEach((item, index) => {
          let groupPosition = item.groupPositionVoList || [];
          let groupIds = groupPosition.map((item) => item.groupId);
          if (groupIds.includes(node.id)) {
            toBeRemoved.push(index);
            this.tableData.forEach((item1) => {
              if (item.userId === item1.userId) {
                item1.status = false;
              }
            });
          }
        });
        for (let i = toBeRemoved.length - 1; i >= 0; i--) {
          this.personArr.splice(toBeRemoved[i], 1);
        }
      },
      // 请求人员列表
      async request() {
        this.loading = true;
        const groupIdListStr = this.node ? this.node : undefined;
        let userName = this.queryParams.userName || undefined;
        try {
          const res = await getExpertList({
            userName,
            unitRange: '1',
            expertStatus: '1',
            current: this.pages.pageNum,
            size: this.pages.pageSize,
            groupIdListStr
          });
          const { records, total } = res.data.data;
          this.total = total;
          this.tableData = records.map((item) => {
            item.status = false;
            item.onlineStatus = false;
            this.personArr.forEach((person) => {
              if (person.userId === item.userId) {
                item.status = true;
              }
              if (person.userId === item.userId && person.onlineStatus) {
                item.onlineStatus = true;
              }
              // 已评审
              if (person.userId === item.userId && person.reviewStatus == 3) {
                item.isDisabled = true;
              }
            });
            return item;
          });
        } catch (e) {
          console.error(e);
        } finally {
          this.loading = false;
        }
      },
      // 触发校验
      dispatch(componentName, eventName, params) {
        let parent = this.$parent || this.$root;
        let name = parent.$options.componentName;

        while (parent && (!name || name !== componentName)) {
          parent = parent.$parent;

          if (parent) {
            name = parent.$options.componentName;
          }
        }
        if (parent) {
          parent.$emit.apply(parent, [eventName].concat(params));
        }
      }
    }
  };
</script>

<style scoped>
  .content-wrapper {
    height: 91%;
  }

  .h100 {
    height: 100%;
  }

  .list_wrapper {
    width: 100%;

    /* padding-bottom: 20px; */
    overflow: hidden;
  }

  .search-box {
    display: flex;
    justify-content: space-between;
  }
</style>
