<template>
  <div class="table_wrapper">
    <el-table
      ref="multipleTable"
      :data="source"
      stripe
      border
      :header-cell-style="{ backgroundColor: '#fafafa' }"
      v-loading="loading"
      @select="handleSelectionChange"
      @select-all="handleSelectionAll"
      style="width: 100%"
    >
      <el-table-column
        align="center"
        type="selection"
        :selectable="selectable"
        width="30"
      >
      </el-table-column>
      <!-- <el-table-column
        align="center"
        type="index"
        label="序号"
        width="50"
      ></el-table-column> -->
      <el-table-column align="center" label="发起组织" show-overflow-tooltip>
        <template slot-scope="{ row }">
          {{ row.initiateOrgName || '--' }}
        </template>
      </el-table-column>

      <el-table-column align="center" label="体系名称" show-overflow-tooltip>
        <template slot-scope="{ row }">
          {{ row.schemeName || '--' }}
        </template>
      </el-table-column>

      <el-table-column
        align="center"
        label="状态"
        show-overflow-tooltip
        min-width="50"
      >
        <template slot-scope="{ row }">
          {{ row.schemeStatusName || '--' }}
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        label="创建人"
        show-overflow-tooltip
        min-width="75"
      >
        <template slot-scope="{ row }">
          {{ row.createUserName || '--' }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="创建日期" show-overflow-tooltip>
        <template slot-scope="{ row }">
          {{ row.createTime || '--' }}
        </template>
      </el-table-column>
      <!-- <el-table-column
        align="center"
        label="体系编号"
        show-overflow-tooltip
        min-width="75"
      >
        <template slot-scope="{ row }">
          {{ row.schemeNo || '--' }}
        </template>
      </el-table-column> -->
      <el-table-column
        class-name="all-table-operation"
        align="center"
        label="操作"
        width="130"
      >
        <template slot-scope="{ row }">
          <el-button
            type="text"
            v-if="permission['check-system-view']"
            @click="$emit('dispatch', 'view', row)"
            >查看
          </el-button>
          <el-button
            v-if="
              row.schemeStatus === '0' &&
              permission['check-system-edit'] &&
              row.canEdit
            "
            type="text"
            @click="$emit('dispatch', 'edit', row)"
            >编辑
          </el-button>
          <el-button
            type="text"
            v-if="permission['check-system-status'] && row.canEdit"
            @click="$emit('dispatch', 'onEnable', row)"
            :style="
              row.schemeStatus === '0' ? 'color: #F56C6C' : 'color: #67C23A'
            "
            >{{ row.schemeStatus === '0' ? '停用' : '启用' }}</el-button
          >
          <el-button
            type="text"
            v-if="
              row.schemeStatus === '0' &&
              permission['check-system-delete'] &&
              row.canEdit
            "
            @click="del(row)"
            style="color: #f56c6c"
            >删除
          </el-button>
          <el-button
            style="grid-column: 1/2 span"
            v-if="
              row.schemeStatus === '0' &&
              permission['check-system-launch'] &&
              row.canEdit
            "
            type="text"
            @click="commit(row)"
            >发起考核
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
  // import { cloneDeep } from 'lodash';
  import { mapGetters } from 'vuex';

  export default {
    name: 'ProjectLibraryTableInfo',
    props: {
      loading: {
        type: Boolean,
        default: false
      },
      source: {
        type: Array,
        default() {
          return [];
        }
      }
    },
    // watch: {
    //   source: {
    //     handler(arr) {
    //       this.list = cloneDeep(arr);
    //     },
    //     deep: true
    //   }
    // },
    data() {
      return {
        list: [],
        visited: false
      };
    },
    methods: {
      // 可选的 退回 3 待提交 0
      selectable(row) {
        if (['0'].includes(row.schemeStatus)) {
          return true;
        } else {
          return false;
        }
      },
      commit(row) {
        this.$emit('dispatch', 'commit', row);
      },
      // 项目删除
      del(row) {
        this.$emit('dispatch', 'delete', row);
      },
      // 多选框
      // no-unused-vars
      handleSelectionChange(selection, row) {
        console.log('selection', row);
        this.$emit('dispatch', 'selection', row);
      },
      // 全选
      handleSelectionAll(selection) {
        console.log('selectionAll', selection);
        this.$emit('dispatch', 'selectionAll', selection);
      }
    },
    computed: {
      ...mapGetters(['permission']),
      menuName() {
        let bool = this.$route.name.includes('schedule');
        return bool ? 'schedule' : 'check';
      },
      tableHeight() {
        let height = document.documentElement.clientHeight;
        let calcHeight = null;
        if (height > 800) {
          calcHeight = (height - (65 + 40 + 10 + 10 + 60 + 190)) / 1;
        } else {
          calcHeight = (height - (65 + 40 + 10 + 10 + 60 + 190)) / 1.18; // 顶部菜单、tab、margin、底部空隙、放大后冗余
        }
        return `${calcHeight}px`;
      }
    }
  };
</script>

<style lang="scss" scoped>
  .project-label {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;

    // justify-content: space-between;
  }

  .show-center {
    justify-content: center;
  }
</style>
