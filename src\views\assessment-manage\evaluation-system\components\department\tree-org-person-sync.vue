<template>
  <div style="width: 100%; height: 100%">
    <div class="tree-wrapper" v-loading="loading">
      <el-tree
        v-if="status"
        ref="tree"
        node-key="id"
        lazy
        highlight-current
        empty-text="暂无数据"
        :load="loadNode"
        :props="props"
        :render-content="renderContent"
        :default-expanded-keys="expandedQueryArr"
        @node-click="nodeClick"
      >
      </el-tree>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'TreePersonSync',
    data() {
      return {
        props: {
          label: 'deptName',
          children: 'children',
          isLeaf: (data) => !data.hasChildren
        }
      };
    },
    props: {
      tree: {
        type: Object,
        default() {
          return {};
        }
      },
      expandedQueryArr: {
        type: Array,
        default() {
          return [];
        }
      },
      status: {
        type: Boolean,
        default: false
      },
      loading: {
        type: Boolean,
        default: false
      },
      query: {
        type: String,
        default: ''
      }
    },
    methods: {
      // 加载数据
      async loadNode(node, resolve) {
        if (node.level === 0) {
          const list = this.tree.ROOT;
          return resolve(list);
        }
        if (node.level >= 1) {
          const isBool = this.expandedQueryArr.some(
            (num) => num === node.data.id
          );
          let list;
          if (isBool) {
            list = this.tree[node.data.id].filter((item) => {
              const bool = this.expandedQueryArr.some((num) => num === item.id);
              if (!bool && item.deptName.includes(this.query)) return item;
              if (bool) return item;
            });
          } else {
            list = this.tree[node.data.id];
          }
          return setTimeout(() => resolve(list), 1000);
        }
      },
      // 节点选取
      nodeClick(obj, node) {
        const nodeInfo = node.data;
        this.$emit('getNodeInfo', nodeInfo);
      },
      // 渲染内容
      renderContent(h, { data }) {
        // deptCategory (1:公司,2:部门,3:小组)
        const { deptCategory, deptName } = data;
        if (deptCategory === 2) {
          return (
            <span>
              <i className="el-icon-folder" style="margin-right: 5px" />
              {deptName}【部门】
            </span>
          );
        } else if (deptCategory === 1) {
          return (
            <span>
              <i class="el-icon-office-building" style="margin-right: 5px" />
              {deptName}
            </span>
          );
        } else {
          return <span>{deptName}</span>;
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .tree-wrapper {
    height: calc(100% - 40px);
    overflow: auto;
  }
</style>
