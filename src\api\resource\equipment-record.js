import request from '@/router/axios';
// 设备台账分类
export const getLedgerList = (params) => {
  return request({
    url: '/api/equipment-classify/tree/list',
    method: 'get',
    params
  });
};
// 设备台账分类- 保存编辑
export const ledgerSave = (data) => {
  return request({
    url: '/api/equipment-classify/save',
    method: 'post',
    data
  });
};
// 设备台账分类- 删除
export const ledgerDel = (data) => {
  return request({
    url: '/api/equipment-classify/delete',
    method: 'post',
    data
  });
};
// 设备台账分类 扩展属性
export const getExpandList = (params) => {
  return request({
    url: '/api/equipment-classify/attr/detail',
    method: 'get',
    params
  });
};
// 设备台账自定义属性- 保存编辑
export const ledgerExpandSave = (data) => {
  return request({
    url: '/api/equipment-classify/attr/save',
    method: 'post',
    data
  });
};
// 根据属性ID和待选值判断是否被选中(拓展属性)
export const attributeCheck = (params) => {
  return request({
    url: '/api/equipment-classify/attr/check',
    method: 'get',
    params
  });
};
// end

export const getList = (data) => {
  return request({
    url: '/api/equipment/page',
    method: 'post',
    data
  });
};
export const getComputerList = (params) => {
  return request({
    url: '/api/server-room/list',
    method: 'get',
    params
  });
};
// 供应商
export const getSupplierList = (data) => {
  return request({
    url: '/api/supplier/list',
    method: 'post',
    data
  });
};
export const getProjectList = (params) => {
  return request({
    url: '/api/szyk-zbusiness/plbase/list/dept',
    method: 'get',
    params
  });
};
export const equipmentSave = (row) => {
  return request({
    url: '/api/equipment/save',
    method: 'post',
    data: row
  });
};
// 批量删除
export const batchDelete = (data) => {
  return request({
    url: '/api/equipment/delete',
    method: 'post',
    data
  });
};
// 停用-启用
export const setStatus = (data) => {
  return request({
    url: '/api/equipment/status',
    method: 'post',
    data
  });
};
// 提交
export const submitFetch = (data) => {
  return request({
    url: '/api/equipment/submit',
    method: 'post',
    data
  });
};
export const getDetail = (params) => {
  return request({
    url: '/api/equipment/detail',
    method: 'get',
    params
  });
};
// 编辑设备台账分类-下级分类接口
export const getNextClassify = (params) => {
  return request({
    url: '/api/equipment-classify/tree/list/code',
    method: 'get',
    params
  });
};

// 导出
export const exportExcel = (data) => {
  return request({
    url: '/api/equipment/export',
    method: 'post',
    responseType: 'blob',
    data
  });
};
// 设备台账 导出模板
export const exportTemplateExcel = (data) => {
  return request({
    url: '/api/equipment/exportTemplate',
    method: 'post',
    responseType: 'blob',
    data
  });
};
// 导入设备台账
export const ImportExcel = (data) => {
  return request({
    url: '/api/equipment/import-equipment',
    method: 'post',
    data
  });
};
// 设备台账分类-懒加载
export const getRecordClassify = (params) => {
  return request({
    url: '/api/equipment-classify/lazy-tree',
    method: 'get',
    params
  });
};
// 批量提交设备台账
export const batchSubmit = (data) => {
  return request({
    url: '/api/equipment/batchSubmit',
    method: 'post',
    data
  });
};
// 设备台账分类-子级的懒加载树形结构
export const getSubDeviceLazyList = (params) => {
  return request({
    url: '/api/equipment/childrenLazyTree',
    method: 'get',
    params
  });
};
// 设备台账分类-子级的树形结构
export const getSubDeviceList = (params) => {
  return request({
    url: '/api/equipment/childrenTree',
    method: 'get',
    params
  });
};
