<template>
  <div v-if="!isError" class="sso-container">
    <div class="avue-home__main">
      <!-- <div class="avue-home__loading" v-loading="loading" alt="loading" /> -->
      <img
        class="avue-home__loading"
        src="/svg/loading-spin.svg"
        alt="loading"
      />
      <div class="avue-home__title">正在加载资源</div>
      <div class="avue-home__sub-title d">
        初次加载资源可能需要较多时间 请耐心等待
      </div>
    </div>
  </div>
  <div v-else class="sso-error-container">
    <div class="avue-home__main">
      <el-result icon="warning" title="警告提示" subTitle="认证失败">
        <template slot="extra">
          <el-button type="primary" @click="returnLogin">返回</el-button>
        </template>
      </el-result>
    </div>
  </div>
</template>
<script>
  // import { mapGetters, mapState } from 'vuex';

  export default {
    name: 'sso',
    data() {
      return {
        loading: true,
        isError: false
      };
    },
    created() {
      this.initialization();
    },
    mounted() {},
    computed: {
      // 登录账号
      username() {
        return this.$route.query.username;
      },
      // 业务范围id
      deptScopeId() {
        return this.$route.query.deptScopeId;
      },
      // 要跳转的url
      url() {
        return this.$route.query.url;
      }
    },
    methods: {
      returnLogin() {
        if (process.env.VUE_APP_ENV === 'production') {
          window.location.href = 'https://xxh.shandong-energy.com/api/oauth'; // 要改统一认证
        } else if (process.env.VUE_APP_ENV === 'show') {
          window.location.href = 'http://xxhdev.shandong-energy.com/api/oauth'; // 要改统一认证
        } else {
          this.$router.replace({ path: '/#/login' });
        }
      },
      initialization() {
        let params = { username: this.username, deptScopeId: this.deptScopeId };
        this.$store
          .dispatch('LoginByToken', params)
          .then(() => {
            let path = this.url ? this.url : '/';
            this.$router.replace({ path });
          })
          .catch((err) => {
            this.isError = true;
            console.log(err);
          });
      }
    }
  };
</script>

<style lang="scss">
  .sso-error-container {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    background-color: #efefef;

    .avue-home__main {
      display: flex;
      flex-direction: column;
      flex-grow: 1;
      align-items: center;
      justify-content: center;
      width: 100%;
      user-select: none;
    }
  }

  .sso-container {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    background-color: #303133;

    .title {
      margin-bottom: 8px;
      color: #333;
    }

    .avue-home__main {
      display: flex;
      flex-direction: column;
      flex-grow: 1;
      align-items: center;
      justify-content: center;
      width: 100%;
      user-select: none;
    }

    .avue-home__footer {
      flex-grow: 0;
      width: 100%;
      padding: 1em 0;
      text-align: center;
    }

    .avue-home__footer > a {
      color: #ababab;
      font-size: 12px;
      text-decoration: none;
    }

    .avue-home__loading {
      width: 32px;
      height: 32px;
      margin-bottom: 20px;
    }

    .avue-home__title {
      margin-bottom: 10px;
      color: #fff;
      font-size: 14px;
    }

    .avue-home__sub-title {
      color: #ababab;
      font-size: 12px;
    }
  }

  .sso-container::before {
    position: absolute;
    top: 0;
    left: 0;
    z-index: -999;
    width: 100%;
    height: 100%;
    background-image: url('/img/bg/login.png');
    background-size: cover;
    content: '';
  }

  .sso-form {
    width: 300px;
  }
</style>
