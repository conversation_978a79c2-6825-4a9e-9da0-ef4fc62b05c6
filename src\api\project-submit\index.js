import request from '@/router/axios';
// 测试通过
export const testPass = (data) => {
  return request({
    url: `/api/project_base/audit`,
    method: 'get',
    params: data
  });
};
// 业务字典-接口  项目分类  列支渠道
export const getBizDict = (params) => {
  return request({
    url: '/api/dict-common/list',
    method: 'get',
    params
  });
};
// 列表
export const getPage = (data) => {
  return request({
    url: '/api/project_base/page',
    method: 'post',
    data
  });
};
// 保存
export const addFetch = (data) => {
  return request({
    url: '/api/project_base/save',
    method: 'post',
    data
  });
};
// 提交
export const submitFetch = (data) => {
  return request({
    url: '/api/project_base/submit',
    method: 'post',
    data
  });
};
// 添加标签
export const addSaveLabel = (data) => {
  return request({
    url: '/api/project_base/save/label',
    method: 'post',
    data
  });
};
// 删除标签
export const delLabel = (data) => {
  return request({
    url: `/api/project_base/delete/label`,
    method: 'get',
    params: data
  });
};
// 批量删除
export const batchDelete = (data) => {
  return request({
    url: '/api/project_base/delete',
    method: 'post',
    data
  });
};
// 获取详情
export const getDetail = (params) => {
  return request({
    url: `/api/project_base/detail`,
    method: 'get',
    params
  });
};
// 批量提报
export const batchReport = (data) => {
  return request({
    url: '/api/project_base/report',
    method: 'post',
    data
  });
};
// 批量下发
export const batchIssue = (data) => {
  return request({
    url: '/api/project_base/report',
    method: 'post',
    data
  });
};
// 导出
export const exportExcel = (data) => {
  return request({
    url: '/api/project_base/export',
    method: 'post',
    responseType: 'blob',
    data
  });
};
// -------------------------
// 根据建设单位id查询项目基础信息
export const getProjectList = (constructionUnitId) => {
  return request({
    url: `/api/zbusiness-project/plExternal/getPlBase/${constructionUnitId}`,
    method: 'get'
  });
};
// 根据项目id查询项目库信息
export const getProjectInfo = (projectId) => {
  return request({
    url: `/api/zbusiness-project/plExternal/getPlInfo/${projectId}`,
    method: 'get'
  });
};
// 根据部门ID查询二级部门 - 公司名称
export const getCompanyName = (deptId) => {
  return request({
    url: `/api/szyk-system/dept/two-dept`,
    method: 'get',
    params: { deptId }
  });
};

// 获取编辑详情
export const getEditDetail = (params) => {
  return request({
    url: `/api/project_progress/detail/edit`,
    method: 'get',
    params
  });
};

// -----------------------

// 项目标签分页
export const labelList = (data) => {
  return request({
    url: '/api/dict-label/page',
    method: 'post',
    data
  });
};
// 项目标签分类
export const getLabelClassification = () => {
  return request({
    url: `/api/dict-label-classify/tree/list`,
    method: 'get'
  });
};
// 新增项目标签
export const addLabel = (data) => {
  return request({
    url: '/api/dict-label/save',
    method: 'post',
    data
  });
};
// 查询项目考核记录
export const getApprovedMemo = (params) => {
  return request({
    url: `/api/rw-manage/getByProject`,
    method: 'get',
    params
  });
};
// 上传补充材料
export const uploadSupplement = (data) => {
  return request({
    url: '/api/project_base/uploadSupplement',
    method: 'post',
    data
  });
};
// 获取补充材料列表
export const supplementList = (params) => {
  return request({
    url: `/api/project_base/supplementList`,
    method: 'get',
    params
  });
};
// 补充材料归档
export const placeOnFile = (params) => {
  return request({
    url: `/api/project_base/placeOnFile`,
    method: 'get',
    params
  });
};
// 文审状态详情
export const docReviewDetail = (params) => {
  return request({
    url: `/api/zbusiness-flow/instance/docReviewDetail`,
    method: 'get',
    params
  });
};
// 更新文审状态
export const updateDocReview = (data) => {
  return request({
    url: '/api/zbusiness-flow/instance/updateDocReview',
    method: 'post',
    data
  });
};
// 作废
export const nullify = (id) => {
  return request({
    url: `/api/project_base/nullify/${id}`,
    method: 'post'
  });
};

// 获取流程所有名称
export const getFlowNameList = (params) => {
  return request({
    url: `/api/zbusiness-flow/instance/getAllStepNameList`,
    method: 'get',
    params
  });
};

// 获取流程所有名称
export const getDeadlineForFiling = (params) => {
  return request({
    url: `/api/zbusiness-flow/instance/getAllStepNameList`,
    method: 'get',
    params
  });
};
// 查看项目终止证明材料列表
export const terminateFiles = (params) => {
  return request({
    url: `/api/project_progress_new/terminate-files`,
    method: 'get',
    params
  });
};
// 项目终止
export const terminate = (data) => {
  return request({
    url: `/api/project_progress_new/terminate`,
    method: 'post',
    data
  });
};
