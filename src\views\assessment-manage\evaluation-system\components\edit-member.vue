<template>
  <!-- @click.capture="cancelcurrentRow"&& permission['check-system-indicator'] -->
  <div>
    <el-button
      v-if="isEdit"
      type="primary"
      icon="el-icon-circle-plus-outline"
      style="margin-bottom: 15px"
      @click="handleAdd"
    >
      评价指标</el-button
    >
    <el-form label-width="150px" :model="form" ref="form" label-suffix="：">
      <!-- :row-class-name="tableRowClassName" -->
      <el-table
        :data="form.stageList"
        border
        :header-cell-style="{ backgroundColor: '#fafafa' }"
        ref="singleTable"
        :span-method="objectSpanMethod"
        highlight-current-row
        @row-click="handleCurrentChange"
        style="width: 100%"
        v-loading="isLoading"
        ><el-table-column
          align="center"
          width="40"
          type="index"
          label="序号"
        ></el-table-column>
        <el-table-column align="center" width="75" label="指标分类">
          <template slot-scope="{ row }">
            {{ row.classifyName || '--' }}
          </template>
        </el-table-column>
        <el-table-column align="center" width="70" label="分类分值">
          <template slot-scope="{ row }">
            {{ row.classifyScore || '--' }}
          </template>
        </el-table-column>
        <el-table-column align="center" width="95" label="是否加分项">
          <template slot-scope="{ row }">
            {{ row.addScore === '0' ? '否' : '是' }}
          </template>
        </el-table-column>
        <el-table-column header-align="center" label="评价指标">
          <template slot-scope="{ row }">
            {{ row.evaluateTarget || '--' }}
          </template>
        </el-table-column>
        <!-- <el-table-column  align="center" label="指标解释">
          <template slot-scope="{ row }">
            {{ row.targetExplain || '--' }}
          </template>
        </el-table-column> -->
        <el-table-column align="center" width="70" label="指标分值">
          <template slot-scope="{ row }">
            {{ row.score || '--' }}
          </template>
        </el-table-column>
        <el-table-column align="center" width="70" label="考核方式">
          <template slot-scope="{ row }">
            {{ row.rwTypeName || '--' }}
          </template>
        </el-table-column>
        <el-table-column
          :width="isEdit ? 145 : 110"
          align="center"
          label="操作"
        >
          <template slot-scope="{ row, $index }">
            <el-button type="text" @click="handleScoring(row, $index)"
              >评分标准（{{ row.list ? row.list.length : 0 }}）
            </el-button>
            <el-button type="text" v-if="isEdit" @click="del(row, $index)"
              >删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-form>
    <AddIndicator
      ref="AddIndicator"
      :indicatorList="indicatorList"
      @save-success="saveSuccess"
      @getIndicatorList="getIndicatorList"
    />
    <AddScoringMethod
      :isEdit="isEdit"
      :id="id"
      ref="AddScoringMethod"
      @save-success="methodSaveSuccess"
    />
  </div>
</template>
<script>
  import { ShowFile } from '@/components/yk-upload-file';
  import AddIndicator from './add-indicator.vue';
  import AddScoringMethod from './add-scoring-method.vue';
  import { deepClone } from '@/util/util.js';
  import {
    getIndicator,
    getMethodList,
    validEvaluateRemove
  } from '@/api/assess-manage';
  import { mapGetters } from 'vuex';
  export default {
    name: 'solution-configuration',
    components: {
      ShowFile,
      AddIndicator,
      AddScoringMethod
    },
    props: {
      formInfo: {
        type: Object,
        default: () => {}
      },
      form: {
        type: Object,
        default: () => {}
      },
      isEdit: {
        type: Boolean,
        default: false
      },
      id: {
        type: String,
        default: ''
      }
    },
    data() {
      return {
        isLoading: false,
        indicatorList: [],
        currentRow: {}
      };
    },
    computed: {
      ...mapGetters(['permission'])
    },
    methods: {
      cancelcurrentRow() {
        this.$refs.singleTable.setCurrentRow();
        this.currentRow = {};
      },
      handleCurrentChange(val) {
        let { classifyId } = this.currentRow;
        if (classifyId == val.classifyId) {
          return this.cancelcurrentRow();
        }
        this.currentRow = val;
      },
      tableRowClassName({ row }) {
        if (row.list && !row.list.length) {
          return 'warning-row-sty';
        }
        return '';
      },
      // 合并
      objectSpanMethod({ row, rowIndex, columnIndex }) {
        let mergeColumn = [1, 2];
        if (mergeColumn.includes(columnIndex)) {
          let colspan = this.getDynamicColspan();
          // console.log(colspan);
          if (colspan.includes(rowIndex)) {
            return {
              rowspan: row.columnIndicator,
              colspan: 1
            };
          } else {
            return {
              rowspan: 0,
              colspan: 0
            };
          }
        }
      },
      // 获取动态开始合并行号 = 排序、数量
      getDynamicColspan() {
        let list = this.form.stageList;
        let uniqueArr = this.uniqueFunc(list);
        // let arr = list.map((item) => item.columnIndicator);
        return uniqueArr;
      },
      // 计算开始合并的初始行号
      uniqueFunc(arr) {
        // 去重
        const res = new Map();
        for (const item of arr) {
          res.set(item.classifyId, item);
        }
        let uniqueArr = [...res.values()];
        // console.log('uniqueArr', uniqueArr);

        let classifyId = uniqueArr.map((item) => item.columnIndicator);
        // console.log('classifyId', classifyId);
        // 计算开始合并的初始行号
        let additionArr = [];
        classifyId.forEach((e, i) => {
          if (i === 0) {
            additionArr.push(0, e);
          } else {
            additionArr.push(additionArr[i] + e);
          }
        });
        return additionArr;
      },
      async handleScoring(row, index) {
        if (row.id && !row.list) {
          let arr = await this.getMethodList(row);
          row.list = arr;
        }
        this.$refs.AddScoringMethod.show(row, index);
      },
      validForm() {
        // let length = this.form.stageList.length;
        // return !!length;
        // let bool = false;
        // this.$refs.form.validate((valid) => {
        //   bool = valid;
        // });
        // return bool;
      },
      methodSaveSuccess(methodArr, index) {
        this.form.stageList[index].list = methodArr || [];
      },
      // 弹窗确定
      saveSuccess(arr) {
        const list = deepClone(arr);
        this.$emit('setStageList', list);
        this.cancelcurrentRow();
      },
      handleAdd() {
        let stageList = this.form.stageList;
        const list = deepClone(stageList);
        list.forEach((item) => {
          item.isReadonly = false;
        });
        this.$refs.AddIndicator.show(list, this.currentRow);
      },
      // 是否被引用
      async hasReferenced(row) {
        try {
          // true 可以删除
          // false 不能删除
          this.isLoading = true;
          let {
            data: { data }
          } = await validEvaluateRemove({
            evaluateTargetId: row.id
          });
          this.isLoading = false;
          return data || false;
        } catch (error) {
          this.isLoading = false;
          console.log(error);
          return false;
        }
      },
      // 删除
      async del(row, index) {
        if (row.id) {
          let bool = await this.hasReferenced(row);
          if (!bool)
            return this.$message.warning(
              '该评价指标中的评分标准已被引用，无法删除！'
            );
        }

        let stageList = this.form.stageList;
        stageList.splice(index, 1);
        // 重新计算行号
        stageList.forEach((item) => {
          let columnIndicator = this.getNum(stageList, item.classifyId);
          item.columnIndicator = columnIndicator;
        });
        this.$emit('setStageList', stageList);
      },
      getNum(arr, classifyId) {
        let filterList = arr.filter((item) => item.classifyId === classifyId);
        return filterList.length;
      },
      // 保存、提交
      save(submitFlag) {
        this.$refs.form.validate((valid) => {
          if (valid) {
            this.isLoading = true;
            if (this.form.stageList && this.form.stageList.length) {
              let param = {
                ...this.form,
                submitFlag
              };
              this.applyAdd(param);
            } else {
              this.isLoading = false;
              this.$message.error('请选择配送物料后提交/保存！');
            }
          }
        });
      },
      // 保存、提交接口
      async applyAdd(param) {
        try {
          // let url = this.statusFlag === '0' ? applyEdit : applyAgainEdit;
          // await url(this.queryId, param);
          let msg = param.submitFlag ? '提交成功' : '保存成功';
          this.$message.success(msg);
          this.back();
        } catch (error) {
          console.log(error);
        } finally {
          this.isLoading = false;
        }
      },
      // 获取指标列表
      async getIndicatorList() {
        try {
          let { data: data } = await getIndicator({
            classifyStatus: '0',
            companyId: this.formInfo.initiateOrgId
          });
          this.indicatorList = data ? data.data : [];
        } catch (error) {
          console.log(error);
        }
      },
      // 获取方法列表
      async getMethodList({ id }) {
        try {
          let { data: data } = await getMethodList({ id });
          let arr = data.data.list ? data.data.list : [];
          let methodArr = arr.map((item) => ({
            scoreMethod: item.scoreMethod
          }));
          return methodArr;
        } catch (error) {
          console.log(error);
        }
      }
    }
  };
</script>
<style lang="scss">
  .el-table .warning-row-sty {
    background: #fffbf2;
  }

  .table-info {
    margin-bottom: 15px;
    padding: 5px 8px;
    color: #666;
    font-size: 14px;
    background-color: rgba(230, 247, 255, 100%);
    border-color: rgba(145, 213, 255, 100%);
    border-style: solid;
    border-width: 1px;
    border-radius: 4px;

    i {
      margin: 0 5px;
      color: #108ee9;
    }

    span {
      color: #f39a23;
    }
  }

  .past,
  .precede {
    font-size: 18px;
    font-style: normal;
  }

  .past {
    color: rgba(230, 52, 34, 100%);
  }

  .precede {
    color: #34c2a2;
  }

  .delay,
  .normality,
  .notStarted {
    padding: 2px 5px;
    font-style: normal;
  }

  .delay {
    color: #fff;
    background-color: rgba(230, 52, 34, 100%);
  }

  .normality {
    color: #34c2a2;
    background-color: #ddf7f2;
  }

  .notStarted {
    color: #b3b3b3;
    background-color: #efefef;
  }
</style>
