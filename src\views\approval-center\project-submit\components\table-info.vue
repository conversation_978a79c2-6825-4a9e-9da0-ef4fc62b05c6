<template>
  <div class="table_wrapper">
    <yk-table
      ref="multipleTable"
      :data="source"
      stripe
      border
      v-loading="loading"
      v-if="showTable && ['0'].includes(activeName)"
      row-key="id"
      className="two-line-clamp"
      @select="handleSelectionChange"
      @select-all="handleSelectionAll"
      style="width: 100%"
      @cell-mouse-enter="tableCellEnter"
    >
      <!-- :selectable="selectable" -->
      <slot name="operateBtnContent" slot="operateBtnContent"></slot>
      <el-table-column
        fixed="left"
        align="center"
        :selectable="selectable"
        type="selection"
        width="30"
      >
      </el-table-column>
      <el-table-column
        align="center"
        prop="description"
        min-width="200"
        label="项目名称"
      >
        <template slot-scope="{ row }">
          <el-button
            type="text"
            @click.stop="$emit('dispatch', 'view', row)"
            class="text_ellipsis_title"
          >
            {{ row.description || '--' }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column align="center" label="申报年份" prop="year" width="70">
        <template slot-scope="{ row }">
          {{ row.year || '/' }}
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        label="批次"
        prop="batchName"
        show-overflow-tooltip
        width="100"
      >
        <template slot-scope="{ row }">
          {{ row.batchName || '/' }}
        </template>
      </el-table-column>
      <!--  v-if="activeName === '0'" -->
      <el-table-column
        align="center"
        :key="1"
        label="当前节点"
        prop="curStepName"
      >
        <template slot-scope="{ row }">
          {{ row.curStepName || '/' }}
        </template>
      </el-table-column>
      <!-- v-if="activeName === '0'" -->
      <el-table-column
        :key="2"
        align="center"
        label="下一节点"
        prop="nextStepName"
      >
        <template slot-scope="{ row }">
          {{ row.nextStepName || '/' }}
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        width="100"
        :key="5"
        v-if="activeName !== '2'"
        label="提交单位"
        prop="submitDeptName"
      >
        <template slot-scope="{ row }">
          {{ row.submitDeptName || '/' }}
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        width="100"
        label="建设单位"
        prop="constructionUnitName"
      >
        <template slot-scope="{ row }">
          {{ row.constructionUnitName || '/' }}
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        width="75"
        label="专业分类"
        prop="specialtyClassificationName"
      >
        <template slot-scope="{ row }">
          {{ row.specialtyClassificationName || '/' }}
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        width="75"
        label="项目分类"
        prop="projectClassificationName"
      >
        <template slot-scope="{ row }">
          {{ row.projectClassificationName || '/' }}
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        width="75"
        label="列支渠道"
        prop="distributionChannelName"
      >
        <template slot-scope="{ row }">
          {{ row.distributionChannelName || '/' }}
        </template>
      </el-table-column>
      <el-table-column
        width="75"
        :key="4"
        v-if="activeName !== '2'"
        align="center"
        label="提交人"
        prop="submitUserName"
      >
        <template slot-scope="{ row }">
          {{ row.submitUserName || '/' }}
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        width="130"
        :key="6"
        v-if="
          [1, 2].includes(currentBusinessScope['unitLevel']) &&
          activeName !== '2'
        "
        label="二级单位审查意见"
        prop="secApproveRemark"
      >
        <template slot-scope="{ row }">
          {{ row.secApproveRemark || '/' }}
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        width="100"
        :key="8"
        v-if="
          [1].includes(currentBusinessScope['unitLevel']) && activeName !== '2'
        "
        label="集团审查意见"
        prop="corpApproveRemark"
      >
        <template slot-scope="{ row }">
          {{ row.corpApproveRemark || '/' }}
        </template>
      </el-table-column>
      <!-- v-if="activeName == '0'" -->
      <!-- <el-table-column
        align="center"
        width="100"
        :key="55"
        label="分管领导意见"
        prop="curReceive"
      >
        <template slot-scope="{ row }">
          {{ row.curReceive || '/' }}
        </template>
      </el-table-column> -->
      <el-table-column
        align="center"
        width="100"
        :key="7"
        v-if="
          [1].includes(currentBusinessScope['unitLevel']) && activeName !== '2'
        "
        label="集团审查结论"
        prop="corpApproveResult"
      >
        <template slot-scope="{ row }">
          {{ row.corpApproveResult || '/' }}
        </template>
      </el-table-column>
      <el-table-column
        width="130"
        align="center"
        :key="9"
        v-if="activeName === '2'"
        label="发起时间"
        prop="applyTime"
        ><template slot-scope="{ row }">
          {{ row.applyTime || '/' }}
        </template>
      </el-table-column>
      <el-table-column
        width="75"
        :key="10"
        v-if="activeName === '2'"
        align="center"
        label="发起人"
        prop="applyUserName"
      >
        <template slot-scope="{ row }">
          {{ row.applyUserName || '/' }}
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        width="100"
        :key="11"
        v-if="activeName === '2'"
        label="发起人部门"
        prop="applyUserDeptName"
      >
        <template slot-scope="{ row }">
          {{ row.applyUserDeptName || '/' }}
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        min-width="140"
        :key="12"
        label="当前办理状态"
        prop="curStatus"
      >
        <template slot-scope="{ row }">
          <!-- {{ row.curStatusName || '/' }} -->
          <el-tag v-if="row.curStatus == 0">{{ row.curStatusName }}</el-tag>
          <el-tag type="success" v-if="row.curStatus == 1">{{
            row.curStatusName
          }}</el-tag>
          <el-tag type="danger" v-if="row.curStatus == 2">{{
            row.curStatusName
          }}</el-tag>
          <el-tag type="info" v-if="row.curStatus == 3">{{
            row.curStatusName
          }}</el-tag>
          <el-tag type="warning" v-if="row.curStatus == 4">{{
            row.curStatusName
          }}</el-tag>
        </template>
      </el-table-column>
      <!-- v-if="activeName === '0'" -->
      <el-table-column
        align="center"
        :key="13"
        width="100"
        label="文审状态"
        prop="docReview"
        ><template slot-scope="{ row }">
          <el-tag type="info" v-if="row.docReview == 0">{{
            row.docReviewName
          }}</el-tag>
          <el-tag type="success" v-if="row.docReview == 1">{{
            row.docReviewName
          }}</el-tag>
          <el-tag type="danger" v-if="row.docReview == 2">{{
            row.docReviewName
          }}</el-tag>
          <template v-if="[null].includes(row.docReview)">/</template>
        </template>
      </el-table-column>
      <!-- activeName === '0' && currentBusinessScope['parentId'] === '0' -->
      <el-table-column
        align="center"
        v-if="currentBusinessScope['parentId'] === '0'"
        :key="133"
        width="75"
        prop="expertReviewList"
        label="专家评审"
        ><template slot-scope="{ row }">
          <template
            v-if="
              !row.expertReviewList ||
              (Array.isArray(row.expertReviewList) &&
                !row.expertReviewList.length)
            "
            >未转发</template
          >
          <div class="expert-review" v-else>
            <el-button type="text" @click="viewExpert(row)">专家名单</el-button>
          </div>
        </template>
      </el-table-column>
      <!-- v-if="activeName === '1'" -->
      <!-- <el-table-column
        align="center"
        :key="138"
        label="办理操作"
        prop="approveStatus"
        ><template slot-scope="{ row }">
          <el-tag type="success" v-if="row.approveStatus == 1">{{
            row.approveStatusName
          }}</el-tag>
          <el-tag type="warning" v-if="row.approveStatus == 2">{{
            row.approveStatusName
          }}</el-tag>
          <el-tag type="danger" v-if="row.approveStatus == 3">{{
            row.approveStatusName
          }}</el-tag>
          <template v-if="[null].includes(row.approveStatus)">/</template>
        </template>
      </el-table-column> -->
      <!-- v-if="activeName !== '2'" -->
      <el-table-column
        width="140"
        align="center"
        :key="3"
        label="提交时间"
        prop="submitTime"
        ><template slot-scope="{ row }">
          {{ row.submitTime || '/' }}
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        label="操作"
        prop="columnOpt"
        fixed="right"
        width="40"
      >
        <template slot-scope="{ row }">
          <el-dropdown trigger="click" @command="(v) => handleCommand(v, row)">
            <i class="el-icon-more"></i>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                v-if="permission['approval-submit-view']"
                command="view"
                >查看</el-dropdown-item
              >
              <el-dropdown-item
                v-if="
                  currentBusinessScope['parentId'] === '0' &&
                  (currentBusinessScope['contactPerson'] || '').includes(
                    'PROJECT'
                  )
                "
                command="edit"
                >编辑</el-dropdown-item
              >
              <el-dropdown-item
                v-if="row.checkFlag && permission['approval-submit-opinion']"
                command="check"
                >审查意见</el-dropdown-item
              >
              <el-dropdown-item v-if="row.sendExpertFlag" command="expertReview"
                >一键转发专家评审</el-dropdown-item
              >
              <el-dropdown-item
                v-if="
                  currentBusinessScope['parentId'] === '0' &&
                  (currentBusinessScope['contactPerson'] || '').includes(
                    'PROJECT'
                  )
                "
                command="move"
                >移到办理中</el-dropdown-item
              >
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </yk-table>
    <slot
      v-if="showTable && ['1', '2'].includes(activeName)"
      name="operateBtnContent"
    ></slot>
    <el-table
      ref="multipleTable"
      :data="source"
      stripe
      border
      v-loading="loading"
      v-if="showTable && ['1', '2'].includes(activeName)"
      class="two-line-clamp"
      @select="handleSelectionChange"
      @select-all="handleSelectionAll"
      style="width: 100%"
      @cell-mouse-enter="tableCellEnter"
    >
      <el-table-column
        fixed="left"
        align="center"
        :selectable="selectable"
        type="selection"
        key="selection"
        width="30"
      >
      </el-table-column>
      <el-table-column
        :key="15"
        min-width="140"
        v-if="['2'].includes(activeName)"
        align="center"
        label="项目编号"
      >
        <template slot-scope="{ row }">
          {{ row.projectNo || '/' }}
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        prop="description"
        key="description"
        min-width="200"
        label="项目名称"
      >
        <template slot-scope="{ row }">
          <el-button
            type="text"
            v-if="activeName === '2'"
            @click.stop="$emit('dispatch', 'view', row)"
            class="text_ellipsis_title"
          >
            {{ row.description || '--' }}
          </el-button>
          <template v-else>{{ row.description || '/' }} </template>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        label="申报年份"
        key="year"
        prop="year"
        width="70"
      >
        <template slot-scope="{ row }">
          {{ row.year || '/' }}
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        label="批次"
        key="batchName"
        prop="batchName"
        width="100"
      >
        <template slot-scope="{ row }">
          {{ row.batchName || '/' }}
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        key="curStepName"
        label="当前节点"
        prop="curStepName"
        v-if="activeName === '2'"
      >
        <template slot-scope="{ row }">
          {{ row.curStepName || '/' }}
        </template>
      </el-table-column>
      <!-- v-if="activeName === '0'" -->
      <el-table-column
        align="center"
        label="下一节点"
        v-if="activeName === '2'"
        prop="nextStepName"
        key="nextStepName"
      >
        <template slot-scope="{ row }">
          {{ row.nextStepName || '/' }}
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        width="100"
        key="submitDeptName"
        label="提交单位"
        prop="submitDeptName"
      >
        <template slot-scope="{ row }">
          {{ row.submitDeptName || '/' }}
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        width="100"
        label="建设单位"
        prop="constructionUnitName"
        key="constructionUnitName"
      >
        <template slot-scope="{ row }">
          {{ row.constructionUnitName || '/' }}
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        width="75"
        label="专业分类"
        prop="specialtyClassificationName"
        key="specialtyClassificationName"
      >
        <template slot-scope="{ row }">
          {{ row.specialtyClassificationName || '/' }}
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        width="75"
        label="项目分类"
        prop="projectClassificationName"
        key="projectClassificationName"
      >
        <template slot-scope="{ row }">
          {{ row.projectClassificationName || '/' }}
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        width="75"
        label="列支渠道"
        prop="distributionChannelName"
        key="distributionChannelName"
      >
        <template slot-scope="{ row }">
          {{ row.distributionChannelName || '/' }}
        </template>
      </el-table-column>
      <el-table-column
        width="75"
        align="center"
        label="提交人"
        prop="submitUserName"
        key="submitUserName"
      >
        <template slot-scope="{ row }">
          {{ row.submitUserName || '/' }}
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        width="130"
        v-if="[1, 2].includes(currentBusinessScope['unitLevel'])"
        label="二级单位审查意见"
        prop="secApproveRemark"
        key="secApproveRemark"
      >
        <template slot-scope="{ row }">
          {{ row.secApproveRemark || '/' }}
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        width="100"
        v-if="[1].includes(currentBusinessScope['unitLevel'])"
        label="集团审查意见"
        prop="corpApproveRemark"
        key="corpApproveRemark"
      >
        <template slot-scope="{ row }">
          {{ row.corpApproveRemark || '/' }}
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        width="100"
        v-if="[1].includes(currentBusinessScope['unitLevel'])"
        label="集团审查结论"
        prop="corpApproveResult"
        key="corpApproveResult"
      >
        <template slot-scope="{ row }">
          {{ row.corpApproveResult || '/' }}
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        min-width="140"
        label="当前办理状态"
        prop="curStatus"
        key="curStatus"
      >
        <template slot-scope="{ row }">
          <!-- {{ row.curStatusName || '/' }} -->
          <el-tag v-if="row.curStatus == 0">{{ row.curStatusName }}</el-tag>
          <el-tag type="success" v-if="row.curStatus == 1">{{
            row.curStatusName
          }}</el-tag>
          <el-tag type="danger" v-if="row.curStatus == 2">{{
            row.curStatusName
          }}</el-tag>
          <el-tag type="info" v-if="row.curStatus == 3">{{
            row.curStatusName
          }}</el-tag>
          <el-tag type="warning" v-if="row.curStatus == 4">{{
            row.curStatusName
          }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        width="100"
        label="文审状态"
        v-if="activeName === '2'"
        prop="docReview"
        key="docReview"
        ><template slot-scope="{ row }">
          <el-tag type="info" v-if="row.docReview == 0">{{
            row.docReviewName
          }}</el-tag>
          <el-tag type="success" v-if="row.docReview == 1">{{
            row.docReviewName
          }}</el-tag>
          <el-tag type="danger" v-if="row.docReview == 2">{{
            row.docReviewName
          }}</el-tag>
          <template v-if="[null].includes(row.docReview)">/</template>
        </template>
      </el-table-column>
      <!-- activeName === '0' && currentBusinessScope['parentId'] === '0' -->
      <el-table-column
        align="center"
        v-if="currentBusinessScope['parentId'] === '0' && activeName === '2'"
        width="75"
        prop="expertReviewList"
        label="专家评审"
        key="expertReviewList"
        ><template slot-scope="{ row }">
          <template
            v-if="
              !row.expertReviewList ||
              (Array.isArray(row.expertReviewList) &&
                !row.expertReviewList.length)
            "
            >未转发</template
          >
          <div class="expert-review" v-else>
            <el-button type="text" @click="viewExpert(row)">专家名单</el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        label="办理操作"
        v-if="activeName === '1'"
        prop="approveStatus"
        key="approveStatus"
        ><template slot-scope="{ row }">
          <el-tag type="success" v-if="row.approveStatus == 1">{{
            row.approveStatusName
          }}</el-tag>
          <el-tag type="warning" v-if="row.approveStatus == 2">{{
            row.approveStatusName
          }}</el-tag>
          <el-tag type="danger" v-if="row.approveStatus == 3">{{
            row.approveStatusName
          }}</el-tag>
          <template v-if="[null].includes(row.approveStatus)">/</template>
        </template>
      </el-table-column>
      <!-- v-if="activeName !== '2'" -->
      <el-table-column
        width="140"
        align="center"
        label="提交时间"
        prop="submitTime"
        key="submitTime"
        ><template slot-scope="{ row }">
          {{ row.submitTime || '/' }}
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        label="操作"
        prop="columnOpt"
        key="columnOpt"
        fixed="right"
        width="40"
      >
        <template slot-scope="{ row }">
          <el-dropdown trigger="click" @command="(v) => handleCommand(v, row)">
            <i class="el-icon-more"></i>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                v-if="permission['approval-submit-view']"
                command="view"
                >查看</el-dropdown-item
              >
              <el-dropdown-item
                v-if="
                  ['0', '2'].includes(activeName) &&
                  currentBusinessScope['parentId'] === '0' &&
                  (currentBusinessScope['contactPerson'] || '').includes(
                    'PROJECT'
                  )
                "
                command="edit"
                >编辑</el-dropdown-item
              >
              <el-dropdown-item
                v-if="
                  ['0', '2'].includes(activeName) &&
                  row.checkFlag &&
                  permission['approval-submit-opinion']
                "
                command="check"
                >审查意见</el-dropdown-item
              >
              <el-dropdown-item
                v-if="['0', '2'].includes(activeName) && row.sendExpertFlag"
                command="expertReview"
                >一键转发专家评审</el-dropdown-item
              >
              <el-dropdown-item
                v-if="
                  activeName === '2' &&
                  currentBusinessScope['parentId'] === '0' &&
                  (currentBusinessScope['contactPerson'] || '').includes(
                    'PROJECT'
                  )
                "
                command="move"
                >移到待办理</el-dropdown-item
              >
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
    <ExpertList ref="expertList"></ExpertList>
  </div>
</template>

<script>
  // import { cloneDeep } from 'lodash';
  import { delLabel } from '@/api/project-submit';
  import { mapGetters } from 'vuex';
  import { getStore } from '@/util/store';
  import ExpertList from './expert-list.vue';
  import { tableCellEnter } from '@/util/dom';
  export default {
    components: { ExpertList },
    // serviceDicts: ['project_approval_opinion'],
    name: 'ProjectLibraryTableInfo',
    props: {
      loading: {
        type: Boolean,
        default: false
      },
      showReviewBtn: {
        type: Number,
        default: 0
      },
      activeName: {
        type: String,
        default: ''
      },
      source: {
        type: Array,
        default() {
          return [];
        }
      }
    },
    // watch: {
    //   source: {
    //     handler(arr) {
    //       this.list = cloneDeep(arr);
    //     },
    //     deep: true
    //   }
    // },
    data() {
      return {
        tableCellEnter,
        list: [],
        visited: false,
        showTable: true
      };
    },
    methods: {
      // 操作列
      handleCommand(command, row) {
        switch (command) {
          case 'view':
            this.$emit('dispatch', 'view', row);
            break;
          case 'edit':
            this.$emit('dispatch', 'edit', row);
            break;
          case 'check':
            this.$emit('dispatch', 'check', row);
            break;
          case 'move':
            this.$emit('dispatch', 'move', row);
            break;
          case 'expertReview':
            this.$emit('dispatch', 'expertReview', row);
            break;
          default:
            break;
        }
      },
      viewExpert(row) {
        this.$refs.expertList.show(row.expertReviewList);
      },
      async handleClose(tag) {
        let params = {
          id: tag.id,
          labelKey: tag.dictKey
        };
        await delLabel(params);
        this.$message({
          type: 'success',
          message: '删除成功!'
        });
        this.$emit('dispatch', 'refresh');
      },
      // 可选的 退回 3 待提交 0 什么情况都可以打标签
      selectable() {
        if (['0', '2'].includes(this.activeName)) {
          return true;
        } else {
          return false;
        }
      },
      commit(row) {
        this.$emit('dispatch', 'commit', row);
      },
      // 项目删除
      del(row) {
        this.$emit('dispatch', 'delete', row);
      },
      // 多选框
      // no-unused-vars
      handleSelectionChange(selection, row) {
        console.log('selection', row);
        this.$emit('dispatch', 'selection', row);
      },
      // 全选
      handleSelectionAll(selection) {
        console.log('selectionAll', selection);
        this.$emit('dispatch', 'selectionAll', selection);
      }
    },
    computed: {
      ...mapGetters(['permission']),
      menuName() {
        let bool = this.$route.name.includes('schedule');
        return bool ? 'schedule' : 'check';
      },
      currentBusinessScope() {
        let org = getStore({ name: 'current-organization' });
        return org || {};
      },
      tableHeight() {
        let height = document.documentElement.clientHeight;
        let calcHeight = null;
        if (height > 800) {
          if (this.activeName === '0')
            calcHeight = (height - (65 + 40 + 10 + 10 + 60 + 190)) / 1.2;
          else calcHeight = (height - (65 + 40 + 10 + 10 + 60 + 190)) / 1;
        } else {
          if (this.activeName === '0')
            calcHeight = (height - (65 + 40 + 10 + 10 + 60 + 190)) / 1.61;
          else calcHeight = (height - (65 + 40 + 10 + 10 + 60 + 190)) / 1.21;
        }
        return `${calcHeight}px`;
      }
    }
  };
</script>

<style lang="scss" scoped>
  .expert-review {
    margin: 10px 0;

    .expert {
      padding-right: 30px;
    }
  }

  .project-label {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;

    // justify-content: space-between;
  }

  /deep/ .el-table .cell {
    font-size: 14px;
  }

  .show-center {
    justify-content: center;
  }
</style>
