import request from '@/router/axios';
// 列表
export const getPage = (data) => {
  return request({
    url: '/api/scheme-target/page',
    method: 'post',
    data
  });
};
// 保存-编辑
export const submitFetch = (data) => {
  return request({
    url: '/api/scheme-target/save',
    method: 'post',
    data
  });
};
// 批量删除
export const batchDelete = (data) => {
  return request({
    url: '/api/scheme-target/delete',
    method: 'post',
    data
  });
};
// 根据ID获取数据
export const getDetail = (params) => {
  return request({
    url: `/api/scheme-target/detail`,
    method: 'get',
    params
  });
};
// 启用、停用
export const majorStatus = (data) => {
  return request({
    url: '/api/scheme-target/status',
    method: 'post',
    data
  });
};
// 指标列表
export const getIndicator = (params) => {
  return request({
    url: `/api/evaluate-target-classify/list`,
    method: 'get',
    params
  });
};
// 指标方法列表
export const getMethodList = (params) => {
  return request({
    url: `/api/scheme-target/method/detail`,
    method: 'get',
    params
  });
};
// 查询小组成员分页
export const getMemberPage = (data) => {
  return request({
    url: '/api/project_evaluate/getMemberPage',
    method: 'post',
    data
  });
};
