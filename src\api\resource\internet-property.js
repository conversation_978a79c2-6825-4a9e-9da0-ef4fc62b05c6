import request from '@/router/axios';
// 分页
export const getList = (params) => {
  return request({
    url: '/api/internet/page',
    method: 'get',
    params
  });
};
// 保存编辑
export const ledgerSave = (data) => {
  return request({
    url: '/api/internet/save',
    method: 'post',
    data
  });
};
// 批量删除
export const batchDelete = (data) => {
  return request({
    url: '/api/internet/delete',
    method: 'post',
    data
  });
};
// 停用-启用
export const setStatus = (data) => {
  return request({
    url: '/api/internet/updateResourceStatus',
    method: 'post',
    data
  });
};
export const getDetail = (params) => {
  return request({
    url: '/api/internet/detail',
    method: 'get',
    params
  });
};
export const orgDetail = (params) => {
  return request({
    url: '/api/szyk-system/dept/orgDetail',
    method: 'get',
    params
  });
};
// 导出
export const exportExcel = (data) => {
  return request({
    url: '/api/internet/export',
    method: 'post',
    responseType: 'blob',
    data
  });
};
export const getEquipmentList = (data) => {
  return request({
    url: '/api/equipment/page',
    method: 'post',
    data
  });
};
export const getInformationList = (data) => {
  return request({
    url: '/api/system/page',
    method: 'post',
    data
  });
};
