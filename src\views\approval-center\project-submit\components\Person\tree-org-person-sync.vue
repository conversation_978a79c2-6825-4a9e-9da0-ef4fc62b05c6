<template>
  <div style="width: 100%; height: 100%; overflow-y: auto">
    <!-- 左侧树 -->
    <el-tree
      ref="myTree"
      v-show="!ifShowSearchDept"
      :data="deptData"
      show-checkbox
      node-key="id"
      default-expand-all
      highlight-current
      :props="defaultProps"
      @check="checkChange"
    >
      <span
        class="el-tree-node__label"
        slot-scope="{ node }"
        @mouseenter="visibilityChange($event)"
      >
        <el-tooltip
          :disabled="!isShowTooltip"
          effect="dark"
          :content="node.label"
          placement="top-start"
        >
          <span class="label">{{ node.label }}</span>
        </el-tooltip>
      </span>
    </el-tree>
  </div>
</template>

<script>
  import { getGroupTree } from '@/api/business-applications/expert-database';
  import { mapGetters } from 'vuex';
  export default {
    name: 'TreePersonSync',
    data() {
      return {
        deptData: [],
        searchDeptList: [],
        searchLoading: false,
        ifShowSearchDept: false,
        searchTreeParam: '',
        isShowTooltip: false,
        defaultProps: {
          label: 'groupName',
          value: 'id',
          children: 'children'
        }
      };
    },
    props: {
      parentIds: {
        type: String,
        default: '3'
      },
      tree: {
        type: Array,
        default() {
          return [];
        }
      },
      loading: {
        type: Boolean,
        default: false
      },
      // 已选部门数组
      treeCheckedData: {
        type: Array,
        default: function () {
          return [];
        }
      }
    },
    watch: {
      filterText(val) {
        this.$refs.tree.filter(val);
      }
    },
    computed: {
      ...mapGetters(['permission', 'userInfo'])
    },
    created() {
      this.getDeptData();
    },
    methods: {
      treeForeach(tree, func) {
        let node,
          list = [...tree];
        while ((node = list.shift())) {
          func(node);
          node.children && list.push(...node.children);
        }
      },
      async getDeptData() {
        let res = await getGroupTree();
        this.deptData = res.data.data || [];
        if (this.treeCheckedData.length === 0) {
          let keys = [];
          this.treeForeach(this.deptData, (node) => {
            keys.push(node.id);
          });
          this.$refs.myTree.setCheckedKeys(keys);
        } else {
          this.$refs.myTree.setCheckedKeys(this.treeCheckedData);
        }
        this.$nextTick(() => {
          this.checkChange({}, { checkedKeys: [] });
        });
      },
      // 选中状态变化 data, isCheck
      checkChange() {
        // if (!isCheck.checkedKeys.includes(data.id)) {
        //   this.$emit('delPeople', data);
        // }
        this.$emit(
          'queryPeople',
          this.$refs.myTree.getCheckedKeys().toString()
        );
      },
      searchDept() {
        this.getDeptData();
      },
      visibilityChange(event) {
        const ev = event.target;
        const ev_weight = ev.children[0].offsetWidth;
        const content_weight = ev.clientWidth;
        if (ev_weight > content_weight) {
          this.isShowTooltip = true;
        } else {
          this.isShowTooltip = false;
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .tree-wrapper {
    height: calc(100% - 40px);
    overflow: auto;
  }

  .dept-list {
    min-height: 100px;
    padding: 10px 0;
    overflow: hidden;
  }

  .dept-item {
    height: 26px;
    padding: 0 5px;
    color: rgb(96, 98, 102);
    font-size: 14px;
    line-height: 26px;
    white-space: nowrap;
    cursor: pointer;
    text-size-adjust: 100%;
  }

  .dept-item.hover {
    color: #409eff;
    background-color: #f5f7fa;
  }
</style>
