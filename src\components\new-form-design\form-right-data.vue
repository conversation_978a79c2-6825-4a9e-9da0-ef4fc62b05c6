<template>
  <div class="form-right-cont">
    <div v-if="formJson && formJson.length" class="componentsData">
      <header>
        {{ listObj.name }}

        <el-tooltip
          effect="dark"
          :content="listObj.headerTooltip"
          placement="top"
        >
          <span
            v-if="listObj.headerTooltip"
            class="iconfont icon-question-circle-fill"
          ></span>
        </el-tooltip>
      </header>
      <!-- store里面的对象里有值而且已点击了校验方法才能调用有报错提示 -->
      <div
        v-for="li of formJson"
        :key="li.type"
        :class="{
          componentsDataError:
            isErrorList[listObj.id].includes(li.type) &&
            $store.state.oaSetUp.isValidate
        }"
      >
        <!-- 判断如果数字输入框而且是限制小数位数时，关闭不显示小数位数 -->
        <div
          v-if="!(li.type === 'decimalLength' && !valueJson.decimal) && li.name"
          class="header"
        >
          <span v-if="li.require" class="import">*</span>
          <span>{{ li.name }}:</span>
        </div>
        <el-input
          v-if="isInput.includes(li.type)"
          v-model.trim="valueJson[li.type]"
          @input="link(li.type, valueJson[li.type])"
          :maxlength="li.maxLength || 30"
        ></el-input>

        <el-switch
          v-if="isSwitch.includes(li.type)"
          v-model.trim="valueJson[li.type]"
          @change="fontColor(li.type, valueJson[li.type])"
        >
        </el-switch>
        <el-tooltip
          effect="dark"
          content="勾选后发起人可以为同事提交申请"
          placement="top"
        >
          <span
            v-if="li.type === 'instead'"
            class="iconfont icon-question-circle-fill"
          ></span>
        </el-tooltip>

        <!-- 判断如果数字输入框而且是限制小数位数时，关闭不显示小数位数 -->
        <el-input-number
          v-if="
            isInputNumber.includes(li.type) ||
            (li.type === 'decimalLength' && valueJson.decimal)
          "
          v-model="valueJson[li.type]"
          @blur="numberChange(valueJson, li.type)"
          controls-position="right"
          :min="0"
          step-strictly
          :step="1"
          :max="li.max"
        ></el-input-number>

        <!-- 选项 -->
        <select-list
          v-if="isSelectList.includes(li.type)"
          v-model="valueJson[li.type]"
        ></select-list>

        <!-- 关联审批 -->
        <relevance-apply
          v-if="li.type === 'relevanceApply'"
          v-model="valueJson[li.type]"
        ></relevance-apply>

        <!-- 计算公式 -->
        <formula
          v-if="isComputed.includes(li.type)"
          v-model="valueJson[li.type]"
          @formula="formula"
          :id="listObj.id"
          :formula-name="valueJson.formulaInfo"
          :data-list="dataList"
        ></formula>

        <!-- 请假/调休组件 -->
        <holiday v-if="li.type === 'holiday'"></holiday>

        <!-- 类型 -->
        <div v-if="isRadioType.includes(li.type)" class="radio">
          <el-radio
            v-for="v of li.children"
            :key="v.value"
            v-model="valueJson[li.type]"
            :label="v.value"
            >{{ v.label }}</el-radio
          >
        </div>
      </div>
    </div>
  </div>
</template>
<script>
  import selectList from './right-data-components/select-list';
  import relevanceApply from './right-data-components/relevance-apply';
  import formula from './right-data-components/formula';
  import holiday from './right-data-components/holiday';
  import { getObjType } from '@/util/util';

  const IS_INPUT = [
    'name',
    'placeholder',
    'link',
    'unit',
    'nameOne',
    'nameTwo',
    'nameThree',
    'action',
    'content'
  ];
  const IS_SWITCH = [
    'required',
    'scanCode',
    'fontColor',
    'noApply',
    'decimal',
    'capital',
    'instead',
    'peerPeople'
  ];
  const IS_INPUT_NUMBER = ['maxLength'];
  const IS_COMPUTED = ['formula'];
  const IS_SELECT_LIST = ['options'];
  const IS_RADIO_TYPE = [
    'dateType',
    'phoneType',
    'peopleSelect',
    'deptSelect',
    'provinceType'
  ];
  export default {
    components: { selectList, relevanceApply, formula, holiday },
    props: {
      listObj: {
        type: Object,
        default: () => {}
      },
      dataList: {
        type: Array,
        default: () => []
      }
    },
    data() {
      return {
        isInput: IS_INPUT,
        isSwitch: IS_SWITCH,
        isInputNumber: IS_INPUT_NUMBER,
        isComputed: IS_COMPUTED,
        isSelectList: IS_SELECT_LIST,
        isRadioType: IS_RADIO_TYPE,
        formJson: [],
        valueJson: {}
      };
    },
    computed: {
      isErrorList() {
        return this.$store.state.oaSetUp.oaComponentsErrorList;
      }
    },
    watch: {
      dataList: {
        handler() {
          const obj = {};
          this.dataList.forEach((d) => {
            if (d.type === 'form') {
              obj[d.id] = !d.children.length ? ['formError'] : [];
            }
          });
          this.$store.commit('SET_ERROR_LIST', obj);
        },
        deep: true
      },
      listObj: {
        handler() {
          if (this.listObj) {
            this.formJson = this.listObj.formJson;
            this.valueJson = this.listObj.valueJson;
            this.formJson && this.validate();

            // 如果当前修改项跟计算公式有关，需要将有关的计算公式里面数据更新一遍
            const type = this.listObj.type;
            if (['inputMoney', 'inputNumber', 'computed'].includes(type)) {
              this.computedChange(this.dataList);
            }
          } else {
            this.formJson = [];
            this.valueJson = [];
          }
        },
        deep: true
      }
    },
    methods: {
      // 更新计算公式
      computedChange(list) {
        list.forEach((d) => {
          if (d.type === 'computed') {
            d.valueJson.formula.forEach((f) => {
              if (f.value === this.listObj.id) {
                f.label = this.listObj.valueJson.name;
              }
            });
            d.valueJson.formulaInfo = d.valueJson.formula
              .map((l) => l.label)
              .join('');
          } else if (d.type === 'form') {
            this.computedChange(d.children);
          }
        });
      },
      validate() {
        const obj = {};
        obj[this.listObj.id] = [];

        // 需要判断列表中又没有明细/表格，有的话判断children有没有东西
        this.dataList.forEach((d) => {
          if (d.type === 'form') {
            obj[d.id] = !d.children.length ? ['formError'] : [];
          }
        });

        this.formJson.forEach((f) => {
          if (f.require && getObjType(this.valueJson[f.type]) === 'array') {
            if (['radio', 'checkbox'].includes(this.listObj.type)) {
              // 判断是否单选多选，是的话需要选项值最少两项而且不为空
              const arrayNoEmpty = this.valueJson[f.type].every((l) => l.value);
              if (!arrayNoEmpty || this.valueJson[f.type].length < 2) {
                obj[this.listObj.id].push(f.type);
              }
            }
            if (
              ['computed'].includes(this.listObj.type) &&
              this.valueJson[f.type].length < 1
            ) {
              // 计算公式的话需要length>0
              obj[this.listObj.id].push(f.type);
            }
          } else if (f.require && !this.valueJson[f.type]) {
            obj[this.listObj.id].push(f.type);
          }
        });
        this.$store.commit('SET_ERROR_LIST', obj);
      },
      numberChange(val, type) {
        if (type === 'decimalLength' && !val[type] && val[type] !== 0) {
          val[type] = 2;
        }
      },
      fontColor(type, bl) {
        if (type === 'fontColor' && bl) {
          this.valueJson.link = '';
        }
      },
      link(type, bl) {
        if (type === 'link' && bl) {
          this.valueJson.fontColor = false;
        }
      },
      formula(value) {
        this.valueJson.formulaInfo = value;
      }
    }
  };
</script>
<style lang="scss">
  .form-right-cont {
    min-width: 380px;

    .componentsData {
      box-sizing: border-box;
      width: 380px;
      height: 100%;
      padding: 24px;
      padding-left: 0;
      overflow-y: auto;
      background: #fff;

      .icon-question-circle-fill {
        margin-left: 4px;
        color: #c4c4c4;
        font-size: 16px;
      }

      header {
        margin-left: 24px;
        color: #333;
        font-size: 18px;
        line-height: 18px;
      }

      .header {
        width: 135px;
        margin-right: 4px;
        text-align: right;
      }

      .el-switch {
        margin-top: 5px;
      }

      .radio {
        display: flex;
        flex-direction: column;

        & > label {
          margin-top: 9px;
          margin-bottom: 15px;
        }

        & > label:last-child {
          margin-bottom: 0;
        }
      }

      & > div {
        display: flex;
        flex-wrap: wrap;
        margin-top: 24px;
        line-height: 32px;

        & > div:not(.header) {
          flex: 1;
        }

        .el-switch {
          flex: inherit !important;
        }

        & > span {
          margin-right: 4px;
        }
      }

      .import {
        color: #ff5151;
      }

      .componentsDataError {
        .el-input__inner {
          border-color: #ff5151;
        }
      }
    }
  }
</style>
