import request from '@/router/axios';
// 列表
export const getTaskmodelList = (data) => {
  return request({
    url: '/api/zbusiness-task/taskmodel/page',
    method: 'post',
    data
  });
};
// 新增、编辑
export const taskmodelSave = (data) => {
  return request({
    url: '/api/zbusiness-task/taskmodel/save',
    method: 'post',
    data
  });
};
// 启用、停用
export const taskmodelStatus = (data) => {
  return request({
    url: '/api/zbusiness-task/taskmodel/updateStatus',
    method: 'post',
    data
  });
};
// 删除
export const taskmodelDel = (params) => {
  return request({
    url: '/api/zbusiness-task/taskmodel/deleteByIds',
    method: 'delete',
    params
  });
};
// 详情
export const taskmodelDetail = (params) => {
  return request({
    url: '/api/zbusiness-task/taskmodel/fetchById',
    method: 'get',
    params
  });
};
