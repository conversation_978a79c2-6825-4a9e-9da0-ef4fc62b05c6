<!--
 * @description: 用户详情弹窗
 * @param:
 * @author: Fei
 * @return:
 * @Date: 2020-11-27 15:22:26
-->
<template>
  <div class="">
    <el-dialog
      v-loading="loading"
      @close="close"
      :visible="visible"
      width="400px"
      class="user-info-dialog impage-user-info-dialog"
      :modal="false"
      append-to-body
      :close-on-click-modal="true"
      :show-close="false"
    >
      <!-- 顶部信息 -->
      <div class="impage-user-info-dialog-context">
        <div class="impage-user-info-top">
          <div class="impage-user-info-top-left">
            <div class="user-name">
              <!-- {{ getUserName(userInfo) }} -->
              <img v-if="userInfo.sex" v-oss :src="sexImg" />
            </div>
            <div class="user-diskname">
              <span class="title">昵称：</span>
              <span v-text="userInfo.name || '---'" class="context"></span>
            </div>
            <div class="user-phone">
              <span class="title">手机号：</span>
              <span v-text="userInfo.phone || '---'" class="context"></span>
            </div>
          </div>
          <el-image
            v-if="userInfo.avatar"
            :src="userInfo.avatar"
            :preview-src-list="[userInfo.avatar]"
            class="user-photo"
          ></el-image>
          <img
            v-if="!userInfo.avatar"
            v-oss
            src="/im/user_defalut_photo.png"
            class="user-photo"
          />
        </div>
        <!-- 底部信息 -->
        <ul class="impage-user-info-bottom">
          <!-- <li class="impage-user-info-list">
            <div class="title">企业名称</div>
            <div v-text="userInfo.orgName" class="context"></div>
          </li> -->
          <li class="impage-user-info-list">
            <div class="title">姓名</div>
            <div v-text="userInfo.realName || '---'" class="context"></div>
          </li>
          <li class="impage-user-info-list">
            <div class="title">部门</div>
            <div v-text="userInfo.deptName || '---'" class="context">
              <!-- 青岛九五数字科技有限公司 -->
            </div>
          </li>
          <li class="impage-user-info-list">
            <div class="title">岗位</div>
            <div v-text="userInfo.postName || '---'" class="context"></div>
          </li>

          <li class="impage-user-info-list-divider"></li>
          <!--  <li class="impage-user-info-list">
            <div class="title">手机号</div>
            <div v-text="userInfo.phone || '---'" class="context"></div>
          </li>
          <li v-if="myInfo.id !== userInfo.id" class="impage-user-info-list">
            <div class="title">好友来源</div>
            <div v-text="getApplySource(userInfo)" class="context"></div>
          </li> -->
        </ul>
        <!-- <div class="impage-user-info-bottom-btns"> -->
        <!--  v-if="myInfo.id!==userInfo.id" -->
        <!-- 用户与我是好友
          <el-button
            v-if="
              (userInfo.imUserFriend && userInfo.imUserFriend.id) ||
              userInfo.status === 2
            "
            @click="sendMessage"
            class="send-message-width"
            >发消息</el-button
          > -->
        <!-- 用户与我在同一组织机构，不是好友 stayOrgStatus：是否有在同一组织架构 0-不在 1-在
          <div
            v-if="
              userInfo.status !== 2 &&
              (!userInfo.imUserFriend || !userInfo.imUserFriend.id) &&
              userInfo.stayOrgStatus === 1 &&
              myInfo.id !== userInfo.id
            "
          >
            <el-button @click="sendMessage" class="btn-col2">发消息</el-button>
            <el-button
              @click="addFriends"
              class="btn-col2 add-friends"
              type="primary"
              >添加好友</el-button
            >
          </div> -->
        <!-- 用户与我既不是好友也不再同一组织机构 stayOrgStatus：是否有在同一组织架构 0-不在 1-在 
          <el-button
            v-if="
              userInfo.status !== 2 &&
              (!userInfo.imUserFriend || !userInfo.imUserFriend.id) &&
              userInfo.stayOrgStatus === 0
            "
            @click="addFriends"
            class="add-friends-width"
            type="primary"
            >添加好友</el-button
          >
        </div> -->
      </div>

      <!-- <span>需要注意的是内容是默认不居中的</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="centerDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="centerDialogVisible = false"
          >确 定</el-button
        >
      </span> -->
    </el-dialog>
    <el-dialog
      @close="addFriendsVisible = false"
      width="528px"
      class="add-friends-dialog"
      center
      title="好友请求"
      :visible.sync="addFriendsVisible"
      append-to-body
      :modal="false"
      :close-on-click-modal="true"
      :show-close="false"
    >
      <div class="add-friends-box">
        <div class="title">好友请求</div>
        <el-input
          v-model="friendsAddResone"
          type="textarea"
          class="add-friends-textarea"
          maxlength="50"
          placeholder="请输入"
          :rows="4"
        />
        <div class="title">备注名</div>
        <el-input
          ref="friendsRemarksDom"
          v-model="friendsRemarks"
          class="add-friends-input"
          maxlength="20"
          placeholder="请输入"
        />
      </div>
      <div class="add-friends-bottom-btn">
        <el-button @click="addFriendsVisible = false" class="bottom-btn-close"
          >取消</el-button
        >
        <el-button @click="sendAddApply" type="primary" class="bottom-btn-send"
          >发送</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { addApply, userDetail } from '@/api/desk/im';
  export default {
    props: {
      visible: {
        type: Boolean,
        default: false
      },
      userInfoId: {
        type: String
      },
      orgId: {
        type: String
      }
      // applySource: {
      //   type: Number,
      // },
    },
    data() {
      return {
        addFriendsVisible: false,
        myInfo: {},
        userInfo: {},
        friendsAddResone: '',
        friendsRemarks: null,
        loading: false
      };
    },
    computed: {
      sexImg() {
        return `/im/${this.userInfo.sex === 1 ? 'boy' : 'girl'}.png`;
      }
    },
    watch: {
      visible(val) {
        if (val && this.userInfoId) {
          this.loading = true;
          let params = {
            id: this.userInfoId
          };
          // if (this.orgId) {
          //   params.orgId = this.orgId;
          // }
          userDetail(params)
            .then((res) => {
              this.loading = false;

              if (res.data.code === 200) {
                this.userInfo = res.data.data;
              }
            })
            .catch((err) => {
              this.loading = false;

              console.error(err);
            });
        }
      }
    },
    created() {
      this.myInfo = this.$store.state.user.userInfo;
    },
    updated() {},
    methods: {
      addFriends() {
        this.addFriendsVisible = true;
        let nickName = '';
        if (this.userInfo.imUserFriend && this.userInfo.imUserFriend.id) {
          nickName = this.userInfo.imUserFriend.displayName;
        } else {
          nickName = this.userInfo.nickName;
        }
        this.friendsRemarks = nickName;
        // this.$forceUpdate();
        // this.friendsRemarks = nickName;
      },
      deptName(item) {
        if (item) {
          let deptArr = item.split(',');
          return deptArr.join('/');
        }
      },
      // 好友来源显示
      getApplySource(user) {
        let { applySource, imUserFriend } = user;

        if (imUserFriend) {
          let { applySourceName } = imUserFriend;
          if (applySourceName) {
            return applySourceName;
          }
        }
        if (applySource) {
          if (applySource === 1) {
            return '通过扫一扫添加';
          } else if (applySource === 2) {
            return '通过手机号添加';
          } else if (applySource === 3) {
            return '通过群组添加';
          } else if (applySource === 4) {
            return '通过企业内部组织添加';
          }
        }
      },
      getUserNickName(item) {
        if (item.imUserFriend && item.imUserFriend.targetName) {
          return item.imUserFriend.targetName;
        }
        // this.friendsRemarks = nickName;
        return item.nickName;
      },
      getUserName(item) {
        // if (item.friendRemarkName) {
        //   return item.friendRemarkName;
        // } else if (item.employeeName) {
        //   return item.employeeName;
        // } else if (item.nickName) {
        //   return item.nickName;
        // } else if (item.imName) {
        //   return item.imName;
        // }
        // 优先展示备注 没有备注展示昵称
        if (item.friendRemarkName) {
          return item.friendRemarkName;
        } else if (item.nickName) {
          return item.nickName;
        }
      },
      sendAddApply() {
        let params = {
          applyMessage: this.friendsAddResone,
          displayName: this.friendsRemarks,
          targetId: this.userInfo.targetId
            ? this.userInfo.targetId
            : this.userInfo.id,
          applySource: this.userInfo.imUserFriend.applySource || 4
        };
        addApply(params)
          .then((res) => {
            if (res.data.code === 200) {
              if (res.data.data) {
                this.$message({
                  message: '好友请求已发送',
                  type: 'success'
                });
              } else {
                this.$message({
                  message: '好友请求发送失败',
                  type: 'error'
                });
              }
              this.addFriendsVisible = false;
              this.friendsAddResone = '';
              this.friendsRemarks = '';
              this.applyMessage = null;
              this.displayName = null;
            }
          })
          .catch((error) => {
            console.error(error);
          });
      },
      sendMessage() {
        this.$emit('goToChatPage', this.userInfo, 1);
        this.$emit('close');
      },
      close() {
        this.friendsAddResone = '';
        this.friendsRemarks = '';
        this.userInfo = {};
        this.$emit('close');
      }
    }
  };
</script>

<style lang="scss">
  .impage-user-info-dialog {
    line-height: 14px;
    text-align: left;

    &.user-info-dialog {
      .el-dialog {
        box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 8%);
        box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 8%);
      }
    }

    &.el-dialog__wrapper .el-dialog .el-dialog__header {
      display: none;
    }

    &.el-dialog__wrapper .el-dialog .el-dialog__body {
      padding: 0 !important;
    }
  }

  .el-dialog__wrapper {
    &.add-friends-dialog {
      .el-dialog {
        .el-dialog__header {
          padding: 36px 24px 20px !important;
          border-bottom: 0;
        }

        .el-dialog__body {
          padding: 0 24px;
        }
      }
    }
  }
</style>
<style lang="scss" scoped>
  .impage-user-info-dialog {
    .impage-user-info-dialog-context {
      background: #fff;
    }

    .title {
      color: #999;
      font-size: 12px;
    }

    .context {
      color: #333;
      font-size: 14px;
    }

    .impage-user-info-top {
      position: relative;
      padding: 30px;
      background: #f7f8fa;

      .impage-user-info-top-left {
        .user-name {
          padding-right: 80px;
          padding-bottom: 24px;
          color: #333;
          font-weight: 500;
          font-size: 20px;
          line-height: 20px;
        }

        .user-diskname {
          .title {
            font-size: 14px;
          }
          padding-bottom: 16px;
        }

        .user-phone {
          .title {
            font-size: 14px;
          }
        }

        // display: inline-block;
      }

      .user-photo {
        position: absolute;
        top: 30px;
        right: 30px;
        width: 60px;
        height: 60px;
        border-radius: 60px;
      }
    }

    .impage-user-info-bottom {
      margin: 0;
      padding: 6px 30px 0;

      .impage-user-info-list {
        list-style: none;

        .title {
          padding-top: 24px;
        }

        .context {
          padding-top: 12px;
        }
      }

      .impage-user-info-list-divider {
        height: 1px;
        margin-top: 30px;
        margin-bottom: 6px;
        list-style: none;
        background: #e8e8e8;
      }
    }

    .impage-user-info-bottom-btns {
      padding: 30px;
      text-align: center;

      .send-message-width,
      .add-friends-width {
        width: 100%;
        margin: 0;
      }

      .btn-col2 {
        width: 160px;
        margin: 0;
      }

      .add-friends {
        margin-left: 20px;
      }
    }
  }

  .add-friends-box {
    .title {
      padding-bottom: 16px;
    }

    .add-friends-textarea {
      height: 102px;
      padding-bottom: 24px;
    }
  }

  .add-friends-bottom-btn {
    padding-top: 26px;
    padding-bottom: 24px;
    text-align: center;

    .bottom-btn-close,
    .bottom-btn-send {
      width: 148px;
    }
  }
</style>
