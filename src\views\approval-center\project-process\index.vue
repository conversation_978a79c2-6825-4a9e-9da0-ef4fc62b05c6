<template>
  <basic-container autoHeight>
    <search @search="query" />
    <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
      <el-tab-pane label="待审批" name="0"></el-tab-pane>
      <el-tab-pane label="已审批" name="1"></el-tab-pane>
      <el-tab-pane label="已发起" name="2"></el-tab-pane>
    </el-tabs>
    <div style="margin-bottom: 15px">
      <el-button
        v-if="activeName === '0'"
        :disabled="!rows.length"
        type="primary"
        icon="el-icon-check"
        @click="handleAdd"
        >审批</el-button
      >
      <!-- <el-button
        v-if="activeName === '0' && permission['approval-process-opinion']"
        :disabled="!rows.length"
        type="primary"
        icon="el-icon-check"
        
        @click="handleReview"
        >审查意见</el-button
      > -->
    </div>
    <transition name="el-fade-in-linear">
      <table-info
        :source="tableData"
        ref="tableInfo"
        :loading="loading"
        :activeName="activeName"
        @dispatch="dispatch"
      />
    </transition>

    <yk-pagination
      small
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.current"
      :limit.sync="queryParams.size"
      @pagination="getList"
    />
    <!-- 批量操作结果 -->
    <ErrorDialog
      v-if="formVisible"
      :dialogTitle="dialogTitle"
      :formVisible="formVisible"
      :list="batchList"
      labelName="项目立项编号"
      @refresh="request"
      @close="handleDialogClose"
    />
    <Approval
      ref="approval"
      @close="handleDialogClose"
      @save-success="approvalSuccess"
    ></Approval>
  </basic-container>
</template>

<script>
  import { Search, TableInfo, ErrorDialog, Approval } from './components';
  import {
    approvePage,
    approvedVia,
    returnFlow,
    approveCancel
  } from '@/api/workflow';
  import { mapGetters } from 'vuex';
  import { trimAll } from '@/util/util';
  import 'nprogress/nprogress.css';

  export default {
    name: 'Project-progress',
    components: { Search, TableInfo, ErrorDialog, Approval },
    data() {
      return {
        // 查询条件
        queryParams: {
          size: 10,
          current: 1
        },
        // 表格加载中
        loading: false,
        // 列表条目总数量
        total: 0,
        // 列表数据
        tableData: [],
        // 是否显示
        formVisible: false,
        addVisible: false,
        // dialog标题
        dialogTitle: '',
        // 选中项
        rows: [],
        // 批量提报结果
        batchList: [],
        activeName: '0'
      };
    },
    computed: {
      ...mapGetters(['permission'])
    },
    activated() {
      if (!this.$route.query.remain) {
        this.activeName = '0';
      }
      this.request();
    },
    methods: {
      async approvalSuccess(obj) {
        try {
          this.loading = true;
          const Func = obj.approval === '1' ? approvedVia : returnFlow;
          await Func(obj);
          this.$message.success('操作成功');
          this.rows = [];
          this.request();
        } catch (error) {
          console.log(error);
        } finally {
          this.loading = false;
        }
      },
      handleClick() {
        this.query({});
        this.rows = [];
      },
      // 撤回
      async withdrawal(row) {
        try {
          await this.confirm('确定要撤回吗？');
          this.loading = true;
          let params = {
            instanceId: row.id
          };
          const {
            data: { data }
          } = await approveCancel(params);
          this.batchList = data || [];
          this.$message.success('操作成功');
          this.request();
        } catch (e) {
          console.log(e);
        } finally {
          this.loading = false;
        }
      },
      handleAddTab() {
        this.dialogTitle = '添加标签';
        this.addVisible = true;
      },
      setPage(row) {
        let resultList = this.batchList;
        let bool = resultList.every((item) => item.result);
        if (!bool) return;
        let length = this.tableData.length;
        if (row) {
          if (length === 1) {
            this.queryParams.current = 1;
          }
        } else {
          let rowsLength = this.rows.length;
          if (rowsLength >= length) {
            console.log('rowsLength', rowsLength);
            this.queryParams.current = 1;
          }
        }
      },
      // 查询
      query(params) {
        if (params.isResetQuery) {
          this.rows = [];
        }
        Object.assign(
          this.queryParams,
          {
            current: 1,
            size: 10
          },
          { ...params, isResetQuery: undefined }
        );
        for (const key in this.queryParams) {
          this.queryParams[key] = trimAll(this.queryParams[key]);
        }
        this.request();
      },
      // 分页查询
      getList({ page, limit }) {
        Object.assign(this.queryParams, {
          current: page,
          size: limit
        });
        this.request();
      },
      // 请求列表数据
      async request() {
        try {
          this.loading = true;
          const { data } = await approvePage({
            ...this.queryParams,
            approveType: this.activeName,
            businessType: '2'
          });
          const { total = 0, records = [] } = data.data ? data.data : {};
          this.total = total;
          this.tableData = records;
          this.setSelected();
        } catch (e) {
          console.error(e);
        } finally {
          this.loading = false;
        }
      },
      setSelected() {
        let ids = this.rows.map((item) => item.id);
        let selectedRows = this.tableData.filter((item) =>
          ids.includes(item.id)
        );
        if (selectedRows.length) {
          this.$nextTick(() => {
            selectedRows.forEach((row) => {
              this.$refs['tableInfo'].$refs['multipleTable'].toggleRowSelection(
                row,
                true
              );
            });
          });
        } else {
          this.$nextTick(() => {
            this.$refs['tableInfo'].$refs['multipleTable'].clearSelection();
          });
        }
      },
      // 列表操作
      dispatch(type, data) {
        switch (type) {
          case 'commit':
            return this.withdrawal(data);
          case 'edit':
            return this.handleEdit(data);
          case 'check':
            return this.handleCheck(data);
          case 'view':
            return this.handleView(data);
          case 'refresh':
            return this.request();
          case 'selection':
            return this.handleSelect(data);
          case 'selectionAll':
            return this.handleSelectAll(data);
          default:
            return false;
        }
      },
      handleCheck(row) {
        this.$router.push(
          `/project-manage/project-schedule/schedule-check/${row.id}`
        );
      },
      handleSelect(row) {
        let index = this.rows.findIndex((item) => item.id === row.id);
        if (index >= 0) {
          this.rows.splice(index, 1);
        } else {
          this.rows.push(row);
        }
      },
      uniqueFunc(arr, uniId) {
        let hash = {};
        return arr.reduce((accum, item) => {
          hash[item[uniId]]
            ? ''
            : (hash[item[uniId]] = true && accum.push(item));
          return accum;
        }, []);
      },
      handleSelectAll(rows) {
        if (!this.rows.length && rows.length)
          return (this.rows = [...this.rows, ...rows]);
        if (rows.length) {
          this.rows = this.uniqueFunc([...this.rows, ...rows], 'id');
        } else {
          let clearSelection = this.tableData.map((item) => item.id);
          this.rows = this.rows.filter(
            (item) => !clearSelection.includes(item.id)
          );
        }
      },
      handleAdd() {
        let complexArr = [...this.rows];
        // 有退回
        let hasBack = complexArr.some((item) => item.curStatus === 2);
        // 有不同层级
        let hasDifferent = false;
        if (complexArr.length >= 2) {
          let iniLevel = complexArr.map((item) => item.iniLevel);
          hasDifferent = complexArr.length === new Set(iniLevel).size;
        }

        if (hasBack && hasDifferent) {
          return this.$message.warning(
            '请不要选择既是已退回状态又是不同层级的审核数据'
          );
        }
        const instanceIds = complexArr.map((item) => item.id);
        let params = {
          hasBack,
          hasDifferent,
          businessType: '2',
          instanceIds
        };
        this.$refs.approval.show(params);
      },
      handleSubmit(row) {
        if (!this.rows.length && !row)
          return this.$message({ type: 'warning', message: '请先选择项目' });
        console.log(row);
        try {
          this.loading = true;
          // const res = await getPage(this.queryParams);
          // const { total = 0, records = [] } = res.data.data;
          // this.total = total;
          // this.tableData = records;
          // this.setSelected();
          this.$message.success('操作成功！');
        } catch (e) {
          console.error(e);
        } finally {
          this.loading = false;
        }
        this.dialogTitle = '批量提报结果';
        this.formVisible = true;
      },
      handleView(row) {
        this.$router.push({
          path: `/project-manage/project-schedule/schedule-detail/${row.businessId}`,
          query: {
            flag: 'center',
            audit: this.activeName == '0' ? 'audit' : ''
          }
        });
      },
      handleEdit(row) {
        this.$router.push(
          `/project-manage/project-submit/submit-edit/${row.id}`
        );
      },
      handleDialogClose() {
        this.rows = [];
        this.setSelected();
        this.formVisible = false;
        this.addVisible = false;
      }
    }
  };
</script>

<style lang="scss" scoped>
  .el-pagination {
    margin-top: 20px;
  }
</style>
