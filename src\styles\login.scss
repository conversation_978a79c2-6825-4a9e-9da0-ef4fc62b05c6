.login-container {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
  height: 100%;
  margin: 0 auto;
  background-image: url("/img/bg/bg.png");
  background-size: 100% 100%;
}

.login-weaper {
  width: 1000px;
  margin: 0 auto;

  // box-shadow: -4px 5px 10px rgba(0, 0, 0, 40%);
  box-shadow: 8.5px 5px 10.5px rgba(0, 0, 0, 0.05%),68px 40px 84px rgba(0, 0, 0, 0.07%);

  .el-input-group__append {
    border: none;
  }
}

.login-left,
.login-border {
  position: relative;
  display: flex;
  align-items: center;
  min-height: 500px;
}

.login-left {
  position: relative;
  flex-direction: column;
  justify-content: center;
  float: left;
  width: 50%;
  color: #fff;

  // background-color: #8b9aac;
  // background-image: url('/img/bg/bg-sub.png');
  background-size: 100% 100%;
  border-top-left-radius: 5px;
  border-bottom-left-radius: 5px;
}

.login-left .img {
  width: 135px;
  margin-top: 50px;

  // box-shadow: 8.5px 5px 10.5px rgba(0, 0, 0, 0.05%),68px 40px 84px rgba(0, 0, 0, 0.07%);
  // box-shadow: 0 5px 20px rgba(0,0,0,50%)
}

.login-time {
  position: absolute;
  top: 12px;
  left: 25px;
  width: 100%;
  overflow: hidden;
  color: #fff;
  font-weight: 200;
  font-weight: 400;
  font-size: 24px;
}

.login-left .title {
  margin: 0;
  margin-top: 26px;
  color: #fff;
  font-weight: bold;
  font-size: 30px;
  letter-spacing: 2px;
  text-align: center;
  text-shadow: 2px 2px 5px #3a6fdd;
}

.login-border {
  float: left;
  box-sizing: border-box;
  width: 50%;
  color: #fff;
  background-color: #fff;
  border-left: none;
  border-top-right-radius: 5px;
  border-bottom-right-radius: 5px;
}

.login-main {
  box-sizing: border-box;
  width: 65%;
  margin: 0 auto;
}

.login-main > h3 {
  margin-bottom: 20px;
}

.login-main > p {
  color: #76838f;
}

.login-title {
  margin-bottom: 40px;
  color: #333;
  font-weight: bold;
  font-size: 22px;
  letter-spacing: 4px;
  text-align: center;
}

.login-menu {
  width: 100%;
  margin-top: 40px;
  text-align: center;

  a {
    margin: 0 8px;
    color: #999;
    font-size: 12px;
  }
}

.login-submit {
  width: 100%;
  height: 45px;
  margin-top: 30px;
  color: #409EFF;
  font-weight: 300;
  font-size: 18px;
  font-family: neo;
  letter-spacing: 2px;
  background: none;
  border: 1px solid #409EFF;
  cursor: pointer;
  transition: 0.25s;
}

.login-form {
  margin: 10px 0;

  i {
    color: #333;
  }

  .el-form-item__content {
    width: 100%;
  }

  .el-form-item {
    margin-bottom: 12px;
  }

  .el-input {
    input {
      padding-bottom: 10px;
      color: #333;
      text-indent: 5px;
      background: transparent;
      border: none;
      border-bottom: 1px solid rgb(235, 237, 242);
      border-radius: 0;
    }

    .el-input__prefix {
      i {
        padding: 0 5px;
        font-size: 16px !important;
      }
    }
  }
}

.login-code {
  display: flex;
  align-items: center;
  justify-content: space-around;
  margin: 0 0 0 10px;
}

.login-code-img {
  width: 100px;
  height: 38px;
  margin-top: 2px;
  color: #333;
  font-weight: bold;
  font-size: 14px;
  line-height: 38px;
  letter-spacing: 5px;
  text-align: center;
  text-indent: 5px;
  background-color: #fdfdfd;
  border: 1px solid #f0f0f0;
  cursor: pointer!important;
}
