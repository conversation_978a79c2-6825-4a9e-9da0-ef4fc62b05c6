<template>
  <div class="h-upload" id="h-upload" :class="{ 'is-deleted': isDeleted }">
    <el-upload
      v-if="(uploadable && !closeUpload) || againUpload"
      :action="action"
      :headers="hHeaders"
      :multiple="multiple"
      :show-file-list="false"
      :accept="accept"
      :list-type="listType"
      :before-upload="handleBeforeUpload"
      :on-success="handleOnSuccess"
      :on-error="handleOnError"
      :on-exceed="handleExceed"
      :disabled="hDisabled"
      :drag="drag"
      :auto-upload="autoUpload"
      :on-change="onChange"
      :data="uploadData"
      class="h-upload-content"
    >
      <slot></slot>
      <slot name="trigger"></slot>
      <div v-if="showTips" slot="tip" class="h-upload-tips">{{ tip }}</div>
    </el-upload>
    <template v-else-if="!fileList.length && noTip">无</template>
    <template v-if="showFileList">
      <div
        v-for="(
          { url, icon, name, type, createTime, id, attachId }, index
        ) in fileList"
        :key="url + index"
        class="file-item"
        :class="{ 'is-disabled': hDisabled }"
      >
        <el-image
          :src="encodeOSSUrl(url)"
          :preview-src-list="getPreviewList(url)"
          :class="`js_imgPreviewModel${key}${index} img-preview-model`"
        >
        </el-image>
        <img v-oss :src="icon" class="file-item-icon" />
        <span
          class="file-name"
          :style="{ width: `${visualWidth - (disabled ? 85 : 20)}px` }"
          :title="name + ' ' + createTime"
        >
          <el-button
            v-if="disabled"
            :style="{ width: `${visualWidth - 90}px` }"
            class="button-file-name"
            style="color: #606266"
            type="text"
            @click="seeFile(type, index, url, name)"
            >{{ name }}</el-button
          >
          <template v-else>{{ name }}</template>
          <template v-if="showCreateTime">{{ createTime }}</template>
        </span>
        <!-- <el-button @click="seeFile(index)" type="link">{{ name }}</el-button> -->
        <i class="el-icon-circle-check" />
        <i
          @click="handleOnRemove(index)"
          class="el-icon-circle-close file-list-close-btn"
        />

        <template v-if="!isDeleted">
          <!-- <span
            v-if="docViewJudgeFun(type) || imageType(type)"
            @click="seeFile(type, index, url, name)"
            class="el-button el-button--text"
          >
            查看
          </span> -->
          <span
            @click="
              resetDownLoadFunction(encodeOSSUrl(url), name, id, attachId)
            "
            class="el-button-down el-button--text"
          >
            下载
          </span>
        </template>
      </div>
    </template>
  </div>
</template>

<script>
  import website from '@/config/website';
  // import { Base64 } from 'js-base64';
  import {
    deepClone,
    getDocIcon,
    docViewJudge,
    encodeOSSUrl,
    downloadFileBlob,
    downloadXls
  } from '@/util/util';
  import { resetRouter } from '@/router/router';
  // import { getWpsViewUrl } from '@/api/resource/wps';
  import { mapGetters } from 'vuex';
  import { dateFormat } from '@/util/date';
  import { onlinePreview } from '@/util/util';
  import { downloadFile } from '@/api/common';
  export default {
    name: 'HUpload',
    inject: {
      elForm: {
        default: ''
      }
    },
    props: {
      value: [String, Array],
      drag: {
        type: Boolean,
        default: false
      },
      // default: '/api/attila-resource/oss/endpoint/put-file-by-original-name'
      action: {
        type: String,
        default: '/api/szyk-resource/oss/endpoint/put-file-attach'
      },
      headers: {
        type: Object,
        default() {
          return {};
        }
      },
      uploadData: {
        type: Object,
        default() {
          return {};
        }
      },
      multiple: Boolean,
      limit: {
        type: Number,
        default: 30
      },
      showFileList: {
        type: Boolean,
        default: false
      },
      accept: {
        type: String,
        default:
          '.jpg, .jpeg, .png, .gif, .bmp, .doc, .docx, .xls, .xlsx, .pdf, .ppt, .pptx, .zip, .rar, .7z'
      },
      beforeUpload: Function,
      onSuccess: Function,
      onError: Function,
      onRemove: Function,
      listType: {
        type: String,
        validator(value) {
          return ['text', 'picture', 'picture-card'].indexOf(value) !== -1;
        }
      },
      disabled: {
        type: Boolean,
        default: false
      },
      showLoading: {
        type: Boolean,
        default: false
      },
      showCreateTime: {
        type: Boolean,
        default: false
      },
      againUpload: {
        type: Boolean,
        default: false
      },
      // 上传文件最大限制
      maxFileSize: {
        type: Number,
        default: 100
      },
      tip: String,
      // 判断文件列表没数据时是否显示无
      noTip: {
        type: Boolean,
        default: true
      },
      autoUpload: {
        type: Boolean,
        default: true
      },
      onChange: Function,
      resetDownLoadFunction: {
        type: Function,
        default: async (url, fileName, id, attachId) => {
          if (id || attachId) {
            downloadFile(id || attachId)
              .then((res) => {
                downloadXls(res.data, fileName);
              })
              .catch(() => {});
          } else {
            downloadFileBlob(url, fileName);
          }
        }
      },
      isDeleted: {
        type: Boolean,
        default: false
      },
      closeUpload: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        loading: null,
        key: '',
        encodeOSSUrl,
        // uploadData: {
        //   menuId: ''
        // },
        visualWidth: 0
      };
    },
    computed: {
      hHeaders() {
        let headers = {
          Authorization: `Basic ${website.correspondentSec}`,
          'Tenant-Id': this.$store.getters.tenantId,
          'User-Type': 'web',
          ...this.headers
        };
        headers[website.tokenHeader] = 'bearer ' + this.$store.getters.token;
        return headers;
      },
      hDisabled() {
        return this.disabled || (this.elForm || {}).disabled;
      },
      uploadable() {
        return (
          !this.hDisabled &&
          ((!this.multiple && !this.value) ||
            (this.multiple && this.value.length < this.limit))
        );
      },
      showTips() {
        return this.tip && this.uploadable;
      },
      fileList() {
        return this.getFile(this.value);
        // let list = [];
        // if (this.multiple) {
        //   list = [...this.value];
        // } else if (this.value) {
        //   list = [this.value];
        // }
        // const u = list.filter((l) => l);
        // return Array.from(u, (url) => {
        //   return this.getFile(url);
        // });
      },
      srcList() {
        return this.fileList
          .filter(({ type }) => this.imageType(type))
          .map(({ url }) => url);
      },
      ...mapGetters(['userInfo', 'menu'])
    },
    watch: {
      value(val) {
        if (this.$parent.$options.componentName === 'ElFormItem') {
          this.$parent.$emit('el.form.change', val);
        }
      }
    },
    created() {
      this.key = new Date().getTime();
    },
    mounted() {
      this.computeFileNameWith();
    },
    methods: {
      computeFileNameWith() {
        const element = document.getElementById('h-upload');
        const visualWidth = element.parentNode.offsetWidth;
        this.visualWidth = visualWidth;
      },
      // 菜单树的遍历（广度优先搜索算法）
      bfsTreeEach(tree, func) {
        let node,
          nodes = tree.slice();
        while ((node = nodes.shift())) {
          func(node);
          if (node.children && node.children.length) {
            nodes.push(...node.children);
          }
          if (this.uploadData.menuId !== '') {
            break;
          }
        }
      },
      // 添加文件预览按钮是否显示判定
      docViewJudgeFun(type) {
        return docViewJudge(type);
      },
      imageType(type) {
        return ['jpg', 'jpeg', 'png', 'gif', 'bmp'].includes(type);
      },
      // 查看文档
      seeFile(type, index, url) {
        if (this.imageType(type)) {
          let imgBox = document.getElementsByClassName(
            `js_imgPreviewModel${this.key}${index}`
          )[0];
          imgBox.getElementsByTagName('img')[0].click();
        } else {
          onlinePreview(url);
        }
      },
      handleBeforeUpload(file) {
        this.uploadData.menuId = '';
        this.bfsTreeEach(this.menu, (node) => {
          if (node.code === this.$route.meta.remark) {
            this.uploadData.menuId = node.id;
          }
        });

        let { name, size } = file;
        let typeList = this.accept
          .split(',')
          .map((item) => item.trim().toLowerCase().substr(1));
        // 文件类型校验
        let dotIndex = name.lastIndexOf('.');
        if (dotIndex === -1) {
          this.$message.error(
            `请上传正确格式的文件。支持格式：${typeList.join('/')}`
          );
          return false;
        } else {
          let suffix = name.substring(dotIndex + 1);
          if (typeList.indexOf(suffix.toLowerCase()) === -1) {
            this.$message.error(
              `请上传正确格式的文件。支持格式：${typeList.join('/')}`
            );
            return false;
          }
        }
        let result = true;
        if (this.multiple && this.value.length >= this.maxFileSize) {
          this.$message.error('文件数量超过上限');
          return false;
        }
        if (size > 1048576 * this.maxFileSize) {
          this.$message.error(`文件大小不能超过${this.maxFileSize}M！`);
          return false;
        }

        if (this.beforeUpload) {
          result = this.beforeUpload(file);
          if (result === false) {
            return false;
          }
        }
        if (this.showLoading) {
          this.loading = this.$loading({
            lock: true,
            text: '正在上传',
            spinner: 'el-icon-loading'
          });
        }
        if (result !== true) {
          return Promise.resolve(result);
        }
      },
      handleOnSuccess(response, file) {
        // console.log('response', response);
        // console.log('file', file);
        if (this.showLoading && this.loading) {
          this.loading.close();
          this.loading = null;
        }
        let value = response.data;
        if (this.multiple && this.value) {
          value = [...this.value, value];
        }
        // 原先
        // this.$emit('input', value);
        this.$emit('input', this.getFile(value));
        if (this.onSuccess) {
          this.$nextTick(() => {
            this.onSuccess(response, file, this.fileList);
          });
        }
      },
      handleOnError(err, file) {
        if (this.showLoading && this.loading) {
          this.loading.close();
          this.loading = null;
        }
        if (err.status === 401) {
          this.$store.commit('SET_REFRESH_LOCK', true);
          // 如果是401则跳转到登录页面
          if (!this.$store.getters.reloginFlag) {
            this.$store.commit('SET_RELOGIN_FLAG', true);
            this.$confirm(
              '您尚未登录或者登录信息已失效，请重新登录',
              '请登录',
              {
                confirmButtonText: '前往登录'
              }
            )
              .then(() => {
                this.$store.dispatch('FedLogOut').then(() => {
                  resetRouter();
                  this.$router.push({ name: 'login' });
                });
              })
              .catch(() => {
                this.$store.commit('SET_RELOGIN_FLAG', false);
              });
          }
          return;
        }
        let msg = JSON.parse(err.message).msg;
        this.$message.error(msg);
        if (this.onError) {
          this.onError(err, file, this.fileList);
        }
      },
      handleExceed() {
        this.$message.warning(`当前限制最多上传 ${this.limit} 个文件`);
      },
      handleOnRemove(index) {
        let file = deepClone(this.fileList[index]);
        let value;
        if (this.multiple) {
          value = [...this.value];
          value.splice(index, 1);
        } else {
          value = '';
        }
        this.$emit('input', value);
        if (this.onRemove) {
          this.$nextTick(() => {
            this.onRemove(file, this.fileList);
          });
        }
      },
      getFile(list) {
        if (!list) return [];
        list = Array.isArray(list) ? list : [list];
        let createUser = this.userInfo['user_id'];
        let createUserName = this.userInfo['nick_name'];
        list.forEach((item) => {
          item.id = item.id ? item.id : item.attachId;
          item.name = item.originalName || '';
          let type = item.name.split('.');
          item.type = type[type.length - 1];
          item.url = item.link;
          item.createUser = createUser;
          item.createUserName = createUserName;
          const d = item.createTime ? new Date(item.createTime) : new Date();
          let createTime = dateFormat(d, 'yyyy-MM-dd');
          item.createTime = createTime;
          item.icon = getDocIcon(item.type);
        });
        return list;
        // let name = '';
        // let type = '';
        // let icon = '';
        // let index = url.lastIndexOf('/');
        // if (index > -1) {
        //   name = url.substring(index + 1, url.length);
        // }
        // if (name.split('.').length > 2) {
        //   index = name.indexOf('.');
        //   if (index === 32) {
        //     name = name.substring(index + 1, name.length);
        //   }
        // }
        // index = name.lastIndexOf('.');
        // if (index > -1) {
        //   type = name.substring(index + 1, name.length);
        // }
        // icon = getDocIcon(type);
        // return { url, name, type, icon };
      },
      getPreviewList(url) {
        let list = [...this.srcList];
        let index = list.indexOf(url);
        if (index > -1) {
          let preList = list.splice(0, index) || [];
          list = list.concat(preList);
        }
        return list;
      }
    }
  };
</script>

<style lang="scss" scoped>
  @import '@/styles/element-ui';

  .h-upload {
    .button-file-name {
      padding: 0;
      padding-bottom: 2px;
      overflow: hidden; // 溢出隐藏
      white-space: nowrap; // 禁止换行
      text-align: left;
      text-overflow: ellipsis; // ...
    }

    .file-name {
      // display: flex;
      flex: 1 !important;

      // 修改省略号
      overflow: hidden; // 溢出隐藏
      white-space: nowrap; // 禁止换行
      text-align: left;
      text-overflow: ellipsis; // ...
    }

    &.is-deleted {
      .file-name {
        text-decoration: line-through;

        // overflow: hidden;
        // text-overflow: ellipsis;
        // width: 150px;
        // height: 32px;
        // font-size: 14px;
        // line-height: 16px;
        // overflow: hidden;
        // text-overflow: ellipsis;
        // display: -webkit-box;
        // -webkit-line-clamp: 1;
        // -webkit-box-orient: vertical;
        // border: 1px solid black;
      }
    }

    .img-preview-model {
      width: 0;
      height: 0;
    }

    .h-upload-content {
      width: 100%;
      height: 100%;

      & + .file-item {
        margin-top: 4px;
      }

      .h-upload-tips {
        margin-left: 10px;
        color: #909399;
        font-size: 12px;
      }
    }

    .file-item {
      display: flex;
      align-items: baseline;

      // align-items: center;
      // padding: 12px 18px;
      line-height: 22px;
      background: #f7f8fa;

      .el-button--text {
        cursor: pointer;
      }

      .file-item-icon {
        height: 14px;
        margin-right: 8px;
      }

      span {
        flex: 1;
      }

      .el-icon-circle-check {
        color: #70cd44;
        font-size: 16px;
      }

      .file-list-close-btn {
        display: none;
        color: #a3a3a3;
        font-size: 16px;
        cursor: pointer;

        &:hover {
          color: #757575;
        }
      }

      .el-button-down {
        display: inline-block;

        // margin-left: 5px;
        width: 40px;
        padding: 0;
        font-size: 14px;
      }

      &:hover {
        .el-icon-circle-check {
          display: none;
        }

        .file-list-close-btn {
          display: inline-block;
        }
      }

      &.is-disabled {
        span {
          flex: unset;
        }

        .el-icon-circle-check,
        .file-list-close-btn {
          display: none;
        }

        &:hover {
          .el-icon-circle-check,
          .file-list-close-btn {
            display: none;
          }
        }
      }

      & + .file-item {
        margin-top: 4px;
      }
    }
  }
</style>
