import request from '@/router/axios';
// ------ 我的收藏 -------
// 列表
export const getMycollectionList = (data) => {
  return request({
    url: '/api/zbusiness-knowledge/mycollection/page',
    method: 'post',
    data
  });
};
// 批量加入、取消收藏
export const mycollectionSave = (type, data) => {
  return request({
    url: `/api/zbusiness-knowledge/mycollection/collection?type=${type}`,
    method: 'post',
    data
  });
};
// 详情
export const mycollectionDetail = (kId) => {
  return request({
    url: `/api/zbusiness-knowledge/mycollection/fetchById/${kId}`,
    method: 'get'
  });
};
