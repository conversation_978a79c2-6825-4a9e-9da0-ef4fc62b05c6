<template>
  <Dialog
    v-if="visible"
    :visible="visible"
    :title="title"
    width="800px"
    @closed="hide"
  >
    <el-form
      label-suffix=":"
      ref="form"
      :rules="rules"
      v-loading="loading"
      label-width="150px"
      :model="form"
    >
      <el-form-item label="操作" prop="approval">
        <el-radio-group
          v-model="form.approval"
          class="approval-opinion"
          @change="handleApproval"
        >
          <el-radio-button label="1" :disabled="params.hasBack"
            ><i class="el-icon-check"></i> 提交</el-radio-button
          >
          <el-radio-button label="2" :disabled="params.hasDifferent"
            ><i class="el-icon-back"></i> 退回</el-radio-button
          >
        </el-radio-group>
      </el-form-item>
      <el-form-item
        v-if="form.approval === '1' && showAssignPoint"
        label="下一节点审核人"
        prop="step"
      >
        <el-select
          v-model="form.step"
          style="width: 100%"
          @change="handleStep"
          placeholder="请选择"
        >
          <el-option
            v-for="dict in stepObj.users"
            :key="dict.stepUserId"
            :label="dict.stepUserName"
            :value="dict.stepUserId"
          ></el-option>
        </el-select>
      </el-form-item>
      <!-- <el-form-item
        v-if="form.approval === '2' && showAssignPoint"
        label="退回下一节点"
        prop="step"
      >
        <el-select
          v-model="form.step"
          @change="handleStep"
          style="width: 100%"
          placeholder="请选择"
        >
          <el-option
            v-for="dict in stepObj.users"
            :key="dict.flowSort"
            :label="dict.stepName"
            :value="dict.flowSort"
          >
            <span
              >{{ dict.stepName
              }}<span style="color: #8492a6; font-size: 13px"
                >（{{ dict.approveUserName }}）</span
              ></span
            >
          </el-option>
        </el-select>
      </el-form-item> -->
      <el-form-item label="备注" prop="operateRemark">
        <el-input
          v-model="form.operateRemark"
          type="textarea"
          :maxlength="200"
          placeholder="请输入"
        ></el-input>
      </el-form-item>
    </el-form>
    <div slot="footer">
      <el-button type="primary" plain @click="hide">取消</el-button>
      <el-button type="primary" @click="onSubmit">确定 </el-button>
    </div>
  </Dialog>
</template>
<script>
  import Dialog from '@/components/basic-dialog';
  import { getNext, getReturnProcess } from '@/api/workflow';
  const form = {
    approval: undefined,
    step: undefined,
    operateRemark: undefined
  };
  export default {
    components: { Dialog },
    data() {
      return {
        visible: false,
        loading: false,
        showAssignPoint: false,
        title: '审核操作',
        // ------------
        params: {}, // 传参
        form: { ...form },
        rules: {
          approval: [
            {
              required: true,
              message: '请选择',
              trigger: ['change']
            }
          ],
          step: [
            {
              required: true,
              message: '请选择',
              trigger: ['change']
            }
          ]
        },
        stepObj: {},
        editSelected: {}
      };
    },
    methods: {
      handleApproval(val) {
        switch (val) {
          case '1':
            this.getNext();
            break;
          // case '2':
          //   this.getReturnProcess();
          //   break;
        }
      },
      handleStep(val) {
        let obj = this.stepObj.users.find(
          (item) => (item.stepUserId || item.flowSort) === val
        );
        this.editSelected = obj;
      },
      // 获取下一审核节点
      async getNext() {
        try {
          this.loading = true;
          let {
            data: { data }
          } = await getNext({ ...this.params });
          this.stepObj = data || {};
          let users = this.stepObj.users || [];
          if (users.length) {
            this.showAssignPoint = true;
            if (users.length === 1) {
              this.form.step = users[0].stepUserId;
              this.editSelected = users[0];
            }
          } else {
            this.showAssignPoint = false;
            this.form.step = undefined;
            this.editSelected = {};
          }
        } catch (error) {
          console.log(error);
        } finally {
          this.loading = false;
        }
      },
      // 获取退回列表
      async getReturnProcess() {
        try {
          this.loading = true;
          let {
            data: { data }
          } = await getReturnProcess({ ...this.params });
          this.stepObj.users = data || [];
          this.showAssignPoint = true;
          let users = this.stepObj.users;
          if (users.length === 1) {
            this.form.step = users[0].flowSort;
            this.editSelected = users[0];
          } else {
            // this.showAssignPoint = false;
            this.form.step = undefined;
            this.editSelected = {};
          }
        } catch (error) {
          console.log(error);
        } finally {
          this.loading = false;
        }
      },
      validForm() {
        let bool = false;
        this.$refs.form.validate((val) => {
          bool = val;
        });
        return bool;
      },
      // 提交
      onSubmit() {
        let bool = this.validForm();
        if (!bool) return;
        let params = {};
        let { operateRemark, approval } = this.form;
        let { instanceIds } = this.params;
        if (this.form.approval === '1') {
          let { submitChoose } = this.stepObj;
          let { stepDeptId, stepDeptName, stepUserId, stepUserName } =
            this.editSelected;
          params = {
            instanceIds,
            operateRemark,
            stepDeptId,
            stepDeptName,
            stepUserId,
            stepUserName,
            submitChoose
          };
        } else {
          params = {
            // flowSort: step,
            instanceIds,
            operateRemark
          };
        }
        this.$emit('save-success', { ...params, approval });
        this.hide();
      },
      async show(params) {
        this.visible = true;
        this.params = params;
      },
      hide() {
        this.params = {};
        this.stepObj = {};
        this.editSelected = {};
        this.form = { ...form };
        this.$emit('close');
        this.visible = false;
      }
    }
  };
</script>
<style lang="scss" scoped>
  /deep/ .approval-opinion {
    .el-radio-button:nth-of-type(1) .el-radio-button__inner {
      color: #67c23a;
      background: #f0f9eb;
      border-color: #c2e7b0;
    }

    .el-radio-button:nth-of-type(2) .el-radio-button__inner {
      color: #e6a23c;
      background: #fdf6ec;
      border-color: #f5dab1;
    }

    .el-radio-button:nth-of-type(1).is-active .el-radio-button__inner {
      color: #fff;
      background-color: #67c23a;
      border-color: #67c23a;
      box-shadow: -1px 0 0 0 #67c23a;
    }

    .el-radio-button:nth-of-type(2).is-active .el-radio-button__inner {
      color: #fff;
      background-color: #e6a23c;
      border-color: #e6a23c;
      box-shadow: -1px 0 0 0 #e6a23c;
    }

    .el-radio-button.is-disabled .el-radio-button__inner {
      color: #b9b9b9b8;
      background-color: #d7d7d7;
      border-color: #e5e5e5;
    }
  }
</style>
