import request from '@/router/axios';
// 列表
export const getMajorList = (data) => {
  return request({
    url: '/api/rw-period/page',
    method: 'post',
    data
  });
};
// 新增、编辑
export const phaseSave = (data) => {
  return request({
    url: '/api/rw-period/save',
    method: 'post',
    data
  });
};
// 启用、停用
export const phaseStatus = (data) => {
  return request({
    url: '/api/rw-period/status',
    method: 'post',
    data
  });
};
// 删除
export const phaseDel = (data) => {
  return request({
    url: '/api/rw-period/delete',
    method: 'post',
    data
  });
};
// 详情
export const phaseDetail = (params) => {
  return request({
    url: '/api/rw-period/detail',
    method: 'get',
    params
  });
};
