import request from '@/router/axios';

export const getList = (data) => {
  return request({
    url: '/api/server-room/page',
    method: 'post',
    data
  });
};

export const deleteById = (data) => {
  return request({
    url: '/api/server-room/delete',
    method: 'post',
    data
  });
};

export const detailById = (id) => {
  return request({
    url: `/api/server-room/detail?id=${id}`,
    method: 'get'
  });
};
export const batchSubmit = (data) => {
  return request({
    url: '/api/server-room/batchSubmit',
    method: 'post',
    data
  });
};
export const submit = (data) => {
  return request({
    url: '/api/server-room/submit',
    method: 'post',
    data
  });
};

export const save = (data) => {
  return request({
    url: '/api/server-room/save',
    method: 'post',
    data
  });
};

export const setStatus = (data) => {
  return request({
    url: `/api/server-room/status`,
    method: 'post',
    data
  });
};

export const serverRoomExport = (data) => {
  return request({
    url: `/api/server-room/export`,
    method: 'post',
    responseType: 'blob',
    data
  });
};

// 根据部门ID查询二级部门 - 公司名称
export const getCompanyName = (deptId) => {
  return request({
    url: `/api/szyk-system/dept/two-dept`,
    method: 'get',
    params: { deptId }
  });
};
// 信息系统关联机柜全部展开
export const getSubDeviceList = (data) => {
  return request({
    url: `/api/server-room/getRoomLinkCabinetChildren`,
    method: 'post',
    data
  });
};
// 信息系统关联机柜查询
export const getCabinet = (data) => {
  return request({
    url: `/api/server-room/getRoomLinkCabinetList`,
    method: 'post',
    data
  });
};
