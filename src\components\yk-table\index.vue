<template>
  <div class="yk-table">
    <el-popover
      placement="bottom-end"
      popper-class="table-cloumn-setting-popper"
      trigger="click"
      @hide="saveColumnData"
    >
      <el-button type="primary" plain size="mini" @click="checkAll"
        >全部选中</el-button
      >
      <div class="setting-row-content">
        <div v-for="clo in storageList" :key="clo.label" class="setting-row">
          <yk-svg-icon icon-class="move" class="svg-move" />
          <el-checkbox
            v-model="clo.show"
            class="label"
            @change="showOrHidden($event, clo)"
            >{{ clo.label }}</el-checkbox
          >
          <div style="float: right">
            <el-tooltip
              content="固定在左侧"
              effect="light"
              placement="top"
              :open-delay="300"
            >
              <yk-svg-icon
                :icon-class="clo.fixed === 'left' ? 'pinActive' : 'pin'"
                class="svg-left"
                @click.native="setFixed('left', clo)"
              />
            </el-tooltip>
            <el-tooltip
              content="固定在右侧"
              effect="light"
              placement="top"
              :open-delay="300"
            >
              <yk-svg-icon
                :icon-class="clo.fixed === 'right' ? 'pinActive' : 'pin'"
                class="svg-right"
                @click.native="setFixed('right', clo)"
              />
            </el-tooltip>
          </div>
        </div>
      </div>
      <i v-if="setting" slot="reference" class="el-icon-setting" />
    </el-popover>
    <!-- 全屏按钮 -->
    <yk-svg-icon
      v-if="fullscreen"
      :icon-class="fullScreenFlag ? 'fs_off' : 'fs_on'"
      @click.native="$emit('fullscreen')"
    />
    <!-- 按钮容器 -->
    <div class="table-operate-btn-content">
      <!-- 插槽自定义表格上方操作栏 -->
      <slot name="operateBtnContent">
        <!-- 默认左右都有操作按钮，如果单纯想左或者想右，请在插入具名插槽 -->
        <div class="operate-btn-content">
          <!-- 流式左右布局 -->
          <slot name="btnContentLeft">
            <div />
          </slot>
          <slot name="btnContentRight">
            <div />
          </slot>
        </div>
      </slot>
    </div>
    <new-table ref="table" :config="config" />
  </div>
</template>
<script>
  import Sortable from 'sortablejs';
  import newTable from './table.jsx';
  import { mapGetters } from 'vuex';
  import { getColumnSortData, saveColumnSortData } from '@/api/common';
  import { cloneDeep } from 'lodash';

  export default {
    name: 'YkTable',
    props: {
      className: String,
      setting: {
        type: Boolean,
        default: true
      },
      fullscreen: {
        type: Boolean,
        default: false
      },
      fullScreenFlag: {
        type: Boolean,
        default: false
      }
    },
    components: { newTable },
    data() {
      return {
        showTable: false,
        storageList: [],
        name: '',
        // 从后台获取到的排序配置
        columnData: [],
        config: {
          children: [],
          attrs: {},
          listeners: {},
          key: ''
        }
      };
    },
    computed: {
      ...mapGetters(['userInfo']),
      menuId() {
        return location.href.split('#')[1];
      }
    },
    watch: {
      $attrs: {
        handler(newV) {
          this.$set(this.config, 'attrs', newV);
        },
        deep: true,
        immediate: true
      }
    },
    async mounted() {
      await this.getColumnData();
      this.initStorage();
      this.updateTable();
      this.rowDrop();
    },
    methods: {
      checkAll() {
        this.storageList.forEach((i) => {
          i.show = true;
        });
        this.updateTable();
      },
      filterProp(arr) {
        this.storageList.forEach((i) => {
          if (arr.includes(i.prop)) {
            i.show = false;
          } else {
            i.show = true;
          }
        });
        this.updateTable();
      },
      clearSelection() {
        this.$refs.table.clearSelection();
      },
      toggleRowSelection(row, selected) {
        this.$refs.table.toggleRowSelection(row, selected);
      },
      toggleAllSelection() {
        this.$refs.table.toggleAllSelection();
      },
      toggleRowExpansion(row, expanded) {
        this.$refs.table.toggleRowExpansion(row, expanded);
      },
      setCurrentRow(row) {
        this.$refs.table.setCurrentRow(row);
      },
      clearSort() {
        this.$refs.table.clearSort();
      },
      clearFilter(columnKey) {
        this.$refs.table.clearFilter(columnKey);
      },
      doLayout() {
        this.$refs.table.doLayout();
      },
      sort(prop, order) {
        this.$refs.table.sort(prop, order);
      },
      //行拖拽
      rowDrop() {
        const content = document.querySelector('.setting-row-content');
        const _this = this;
        Sortable.create(content, {
          draggable: '.setting-row',
          delay: 0,
          animation: 150,
          // invertSwap: true,
          easing: 'cubic-bezier(1, 0, 0, 1)',
          swapThreshold: 12,
          ghostClass: 'blue-background-class',
          onEnd({ newIndex, oldIndex }) {
            const currRow = _this.storageList.splice(oldIndex, 1)[0];
            _this.storageList.splice(newIndex, 0, currRow);
            let newArray = _this.storageList.slice(0);
            _this.$nextTick(() => {
              _this.storageList = newArray;
              _this.updateTable();
            });
          }
        });
      },
      // 动态列渲染表格时，需要在列渲染完成后重新渲染一下表格
      reload() {
        this.$nextTick(() => {
          this.getColumnData();
          this.initStorage();
          this.updateTable();
        });
      },
      getInstance() {
        const ref = this.$children.find(
          (i) => i.$options._componentTag === 'el-table'
        );
        return ref;
        // return this.$refs.table.$refs?.elTable;
      },
      showOrHidden(val, clo) {
        if (!val && this.storageList.filter((i) => i.show).length === 0) {
          this.$message.warning('列表最少显示一列');
          this.$nextTick(() => {
            clo.show = true;
          });
          return;
        }
        this.updateTable();
      },
      setFixed(value, clo) {
        if (clo.fixed === value) {
          clo.fixed = false;
        } else {
          clo.fixed = value;
        }
        this.updateTable();
      },
      // 对比后端数据和前端数据
      handleData(frontData = [], backData) {
        // 深度复制 backData，以免直接修改原始数据
        const updatedColumnData = cloneDeep(backData);
        // 寻找新增的字段
        const addedFields = frontData
          .filter((m) => !backData.find((n) => n.columnCode === m.prop))
          .reverse();
        // 将新增的字段加入 updatedColumnData
        addedFields.forEach((field) => {
          const index = frontData.indexOf(field);
          const nextField = frontData[index + 1];
          const insertIndex = nextField
            ? updatedColumnData.findIndex(
                (n) => n.columnCode === nextField.prop
              )
            : -1;
          const { prop, fixed } = field;
          const fieldData = {
            columnCode: prop,
            showFlag: true,
            fixed: fixed || false
          };
          if (insertIndex !== -1) {
            updatedColumnData.splice(insertIndex, 0, fieldData);
          } else {
            updatedColumnData.push(fieldData);
          }
        });

        // 寻找删除的字段
        const removedFields = backData.filter(
          (m) => !frontData.find((n) => m.columnCode === n.prop)
        );

        // 从 updatedColumnData 中删除删除的字段
        removedFields.forEach((field) => {
          const index = updatedColumnData.indexOf(field);
          if (index !== -1) {
            updatedColumnData.splice(index, 1);
          }
        });

        // 现在 updatedColumnData 包含了新增的字段并删除了删除的字段
        return updatedColumnData;
      },
      // 获取列数据
      async getColumnData() {
        try {
          const params = {
            menuId: this.menuId,
            userId: this.userInfo.user_id
          };
          const { data } = await getColumnSortData(params);

          const childrenNodesProps = this.$vnode.componentOptions.children
            .filter(
              (node) =>
                node.componentOptions && !node.componentOptions.propsData.type
            )
            .map((node) => node.componentOptions.propsData);
          this.columnData = this.handleData(
            childrenNodesProps,
            data.data || []
          );
        } catch (e) {
          console.error(e);
        }
      },
      // 初始化缓存配置
      async initStorage() {
        let list = cloneDeep(this.columnData);
        this.$vnode.componentOptions.children.forEach((node) => {
          // 以label为准，因为可能会改文本
          if (
            !(!node.componentOptions || node.componentOptions.propsData.type) &&
            list.findIndex(
              (i) => i.label === node.componentOptions.propsData.label
            ) < 0
          ) {
            // 非插槽且 不是特殊类型的 找不到就加上
            const propsData = cloneDeep(node.componentOptions.propsData);
            if (propsData.fixed === undefined || propsData.fixed === false) {
              propsData.fixed = false;
            } else {
              propsData.fixed = propsData.fixed ? propsData.fixed : 'left';
            }
            // 查找与propsData.prop相匹配的columnCode的索引
            const tempIndex = this.columnData.findIndex(
              (i) => i.columnCode === propsData.prop
            );
            // 如果找到了匹配的columnCode
            if (tempIndex > -1) {
              // 从匹配的对象中解构showFlag和fixed属性
              const { showFlag: show, fixed } = this.columnData[tempIndex];
              // 将解构的属性与propsData中的属性合并，创建一个新的对象
              list[tempIndex] = {
                ...propsData,
                show,
                // 将fixed属性设置为真，如果它的值不是'false'
                fixed: fixed !== 'false' && fixed
              };
            } else {
              list.push({
                fixed: false, // 默认新增的都是不固定
                show: true, // 默认新增的都是显示的
                ...propsData
              });
            }
          }
        });
        // 必须在节点数组存在的才有意义
        list = list.filter(
          (item) =>
            item.show !== undefined &&
            this.$vnode.componentOptions.children.find((n) => {
              return (
                n.componentOptions &&
                item.label === n.componentOptions.propsData.label
              );
            })
        );
        this.storageList = list;
      },
      // 更新表格列时, 将新数据存储到后端
      async saveColumnData() {
        try {
          // 获取存储列表中的列信息
          const columnList = this.storageList
            // 过滤出有属性“prop”的列
            .filter(({ prop }) => prop)
            // 映射列信息，只返回需要的属性：columnCode 和 showFlag
            .map(({ prop, show, fixed }) => {
              return {
                columnCode: prop, // 列的编码
                showFlag: show, // 是否显示该列
                fixed
              };
            });
          const sData = {
            columnList,
            menuId: this.menuId,
            userId: this.userInfo.user_id
          };
          await saveColumnSortData(sData);
        } catch (e) {
          console.error(e);
        }
      },
      // 根据缓存的数组进行渲染表格
      updateTable() {
        // 特殊类型
        const childrenNodes = this.$vnode.componentOptions.children.filter(
          (node) =>
            node.componentOptions && node.componentOptions.propsData.type
        );
        this.storageList.forEach((item) => {
          if (item.show) {
            const node = this.$vnode.componentOptions.children.find(
              (n) =>
                n.componentOptions &&
                n.componentOptions.propsData.label === item.label
            );
            if (node) {
              node.componentOptions.propsData.fixed = item.fixed;
              childrenNodes.push(node);
            }
          }
        });
        this.config.children = childrenNodes;
        this.config.attrs = this.$attrs;
        this.config.listeners = this.$listeners;
        this.config.className = this.className;
        // 通过key值触发表格刷新，避免拖拽由于数组长度一致导致diff算法无法识别
        this.config.key = Math.random() + '';
      }
    }
  };
</script>
<style lang="scss" scoped>
  .table-cloumn-setting-popper {
    .setting-row-content {
      // max-height: 320px;
      // overflow-y: auto;
      .setting-row {
        height: 24px;
        line-height: 24px;

        .svg-move {
          margin-right: 8px;
          font-size: 16px;
          cursor: move;
        }

        .label {
          margin-right: 8px;
        }

        .svg-left,
        .svg-right {
          cursor: pointer;
        }

        .svg-right {
          margin-left: 4px;
          transform: rotate(270deg);
        }
      }
    }
  }

  .blue-background-class {
    background-color: #c1dfff !important;
  }

  .yk-table {
    position: relative;
    width: 100%;
    height: 100%;

    .el-icon-setting {
      position: absolute;
      top: 5px;
      right: 7px;
      font-size: 18px;
      cursor: pointer;
    }

    .svg-icon {
      position: absolute;
      top: 5px;
      right: 32px;
      font-size: 18px;
      cursor: pointer;
    }

    .table-operate-btn-content {
      width: calc(100% - 40px);
      min-height: 28px; // 给一个最低的高度，防止没有插槽，右侧icon被吞
      .operate-btn-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        min-height: 28px;
        padding-bottom: 6px;
      }
    }
  }
</style>
