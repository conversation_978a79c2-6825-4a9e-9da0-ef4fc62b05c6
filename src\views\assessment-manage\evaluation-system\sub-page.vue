<template>
  <basic-container>
    <el-card class="box-card">
      <div slot="header" class="library-title">基本信息</div>
      <DetailInfo v-if="!isEdit" :detail="detail"></DetailInfo>
      <!-- v-if="(isEdit && detail.schemeNo) || (isEdit && !id)" -->
      <EditInfo
        ref="info"
        v-if="isEdit || (isEdit && !id)"
        :formInfo="formInfo"
        :isEdit="isEdit"
        v-loading="loading"
        :formMember="formMember"
        :lowerDepartment="lowerDepartment"
        :id="id"
        @setsolution="handleSetsolution"
      ></EditInfo>
    </el-card>
    <el-card class="box-card">
      <div slot="header" class="library-title">指标配置</div>
      <EditMember
        ref="member"
        :isEdit="isEdit"
        :form="formMember"
        :formInfo="formInfo"
        :id="id"
        @setStageList="setStageList"
      ></EditMember>
    </el-card>

    <!-- <el-card class="box-card">
      <div slot="header" class="library-title">审批流程</div>
      sdfsdf
    </el-card>  && permission['check-system-save'] -->
    <div style="width: 100%; height: 80px"></div>
    <div class="btn-group">
      <el-button
        icon="el-icon-circle-close"
        :loading="btnLoading"
        type="text"
        @click="resetQuery"
        >取消</el-button
      >
      <el-button
        v-if="isEdit"
        :loading="btnLoading"
        type="text"
        icon="el-icon-circle-check"
        @click="handleSubmit"
      >
        保存</el-button
      >
    </div>
  </basic-container>
</template>
<script>
  import { DetailInfo, EditInfo, EditMember, DetailMember } from './components';
  import {
    getDetail,
    submitFetch,
    getLowerDepartment
  } from '@/api/assess-manage';
  import { mapGetters } from 'vuex';
  import { calculate } from '@/util/util';
  const form = {
    id: undefined,
    schemeNo: undefined,
    schemeName: '',
    initiateOrgId: '',
    initiateOrgName: '',
    oldinitiateOrgId: '',
    oldinitiateOrgName: '',
    assessedOrgId: [],
    assessedOrgName: '',
    totalScore: '',
    schemeStatus: '0',
    remark: '',
    createUserName: '',
    createTime: ''
  };
  const formMember = {
    stageList: []
  };
  export default {
    name: 'indicator-sub-page',
    components: {
      DetailInfo,
      EditInfo,
      EditMember,
      DetailMember
    },
    data() {
      return {
        detail: {},
        formInfo: { ...form },
        formMember: { ...formMember },
        btnLoading: false,
        loading: false,
        lowerDepartment: []
      };
    },
    computed: {
      isEdit() {
        let route = {
          'evaluation-system-edit': true,
          'evaluation-system-add': true
        };
        return route[this.$route.name] || false;
      },
      id() {
        return this.$route.params.id;
      },
      ...mapGetters(['permission', 'userInfo'])
    },
    created() {
      // 新增
      if (!this.id) {
        // 申报年份
        // let year = new Date().getFullYear();
        // this.formFunds.year = year + '';
      } else {
        this.getDetail();
      }
      // this.initialization();
    },
    watch: {
      'formInfo.initiateOrgId': {
        handler: async function (val) {
          if (val && this.isEdit) {
            // this.loading = true;
            // await this.getLowerDepartment(val);
            // 懒加载
            // this.$refs.info &&
            //   this.$refs.info.changelazyLoad({ data: { id: val } });
            // this.loading = false;
          }
        }
      }
    },
    methods: {
      async getLowerDepartment(deptId) {
        try {
          let {
            data: { data }
          } = await getLowerDepartment({ deptId });
          this.lowerDepartment = data || [];
        } catch (error) {
          console.log(error);
        }
      },
      handleSetsolution() {
        this.setStageList();
        // this.$refs.member.getIndicatorList();
      },
      setStageList(arr) {
        this.formMember.stageList = arr || [];
        this.calculateScore();
      },
      // 计算总分
      calculateScore() {
        let list = this.formMember.stageList;
        let total = 0;
        list.forEach((item) => {
          if (item.addScore == '0') {
            total = calculate('add', total, item.score);
          }
        });
        console.log(total);
        this.$set(this.formInfo, 'totalScore', total);
        this.$set(this.detail, 'totalScore', total);
      },
      validTable() {
        let stageList = this.formMember.stageList;
        for (let index = 0; index < stageList.length; index++) {
          if (!(stageList[index].list && stageList[index].list.length)) {
            return {
              index
            };
          }
        }
        return;
      },
      // 保存
      async handleSubmit() {
        let info = this.$refs['info'].validForm();
        if (!info) return this.$message.warning('请填写必要字段');
        let length = this.formMember.stageList.length;
        if (!length) return this.$message.warning('请添加指标配置');
        // if (!this.id) {
        let obj = this.validTable();
        if (obj)
          return this.$message.warning(`请添加第${obj.index + 1}行评分标准`);
        // }
        try {
          let list = this.formMember.stageList;
          // list.forEach((item) => {
          //   item.id = undefined;
          //   let subList = item.list || [];
          //   subList.forEach((element) => {
          //     element.id = undefined;
          //   });
          // });
          const params = {
            ...this.formInfo,
            assessedOrgId: this.formInfo.assessedOrgId.join(),
            list
          };
          this.btnLoading = true;
          const {
            data: { data }
          } = await submitFetch(params);
          console.log(data);
          this.$message.success('保存成功');
          this.resetQuery();
        } catch (e) {
          console.log(e);
        } finally {
          this.btnLoading = false;
        }
      },
      // 重置
      resetQuery() {
        this.$router.$avueRouter.closeTag();
        this.$router.push(`/assessment-manage/evaluation-system`);
      },
      async getDetail() {
        try {
          const Func = getDetail;
          const {
            data: { data }
          } = await Func({ id: this.id });
          this.detail = { ...data };
          this.setForm();
        } catch (e) {
          console.log(e);
        }
      },
      // 初始化
      setForm() {
        this.detail.isInit = 1;
        for (const key in this.formInfo) {
          this.formInfo[key] = this.detail[key];
        }
        this.formInfo.assessedOrgId = this.detail.assessedOrgId.split(',');
        // 初始化编辑-保持数据显示
        this.formInfo.oldinitiateOrgId = this.formInfo.initiateOrgId;
        this.formInfo.oldinitiateOrgName = this.formInfo.initiateOrgName;
        // 获取指标分类
        // this.$refs.member.getIndicatorList();

        let list = this.detail.list || [];
        list.forEach((item) => {
          item.list.forEach((method) => {
            method.scoreMethod = method.scoreMethod
              .replace(/&lt;/g, '<')
              .replace(/&gt;/g, '>')
              .replace(/&amp;/g, '&')
              .replace(/&quot;/g, '"')
              .replace(/&apos;/g, "'");
          });
          // item.id = undefined;
          item.columnIndicator = item.number;
        });
        this.formMember.stageList = list;
        this.calculateScore();
      }
    }
  };
</script>

<style lang="scss" scoped>
  .library-title {
    margin: 5px 0;
    padding-left: 8px;
    border-left: 4px solid #409eff;
  }

  .box-card:not(:first-child) {
    margin-top: 20px;
  }

  .el-pagination {
    margin-top: 20px;
  }

  .btn-group {
    position: fixed;
    bottom: 8px;
    left: 208px;
    z-index: 5;
    box-sizing: border-box;
    width: calc(100% - 240px);
    padding: 0 20px;
    text-align: right;
    background-color: rgba(66, 78, 89, 79%);
    border-radius: 4px;

    :deep(.el-button--text) {
      color: #cae4fb;
    }
  }

  /deep/ .el-table .el-table__header .cell {
    color: #909399 !important;
  }

  /deep/ .el-button--small span {
    font-size: 13px !important;
  }

  /deep/ .el-button--mini,
  .el-button--small {
    font-size: 13px !important;
  }

  /deep/ .el-table .cell {
    font-size: 14px;
  }
</style>
