<template>
  <div class="avue-logo">
    <transition name="fade">
      <span v-if="keyCollapse" class="avue-logo_subtitle" key="0">
        <img
          src="/img/logo.png"
          v-if="env === 'production'"
          style="width: 40px; margin-top: 18px"
        />
      </span>
    </transition>
    <transition-group name="fade">
      <template v-if="!keyCollapse">
        <span class="avue-logo_title" key="1">
          <img
            src="/img/logo.png"
            v-if="env === 'production'"
            style="width: 30px; margin: 0 5px -3px 0"
          />
          {{ website.indexTitle }}
        </span>
      </template>
    </transition-group>
  </div>
</template>

<script>
  import { mapGetters } from 'vuex';
  import { env } from '@/config/env';
  export default {
    name: 'logo',
    data() {
      return {
        env
      };
    },
    created() {},
    computed: {
      ...mapGetters(['website', 'keyCollapse'])
    },
    methods: {}
  };
</script>

<style lang="scss">
  .fade-leave-active {
    transition: opacity 0.2s;
  }

  .fade-enter-active {
    transition: opacity 2.5s;
  }

  .fade-enter,
  .fade-leave-to {
    opacity: 0;
  }

  .avue-logo {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1024;
    box-sizing: border-box;
    width: 180px;
    height: 64px;
    overflow: hidden;
    color: rgba(255, 255, 255, 80%);
    font-size: 20px;
    line-height: 64px;
    background-color: #20222a;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 15%);

    &_title {
      display: block;
      font-weight: 300;
      font-weight: bold;
      font-size: 16px;
      text-align: center;
    }

    &_subtitle {
      display: block;
      color: #fff;
      font-weight: bold;
      font-size: 18px;
      text-align: center;
    }
  }
</style>
