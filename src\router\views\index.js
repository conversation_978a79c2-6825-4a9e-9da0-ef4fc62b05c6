import Layout from '@/page/index/';

export default [
  {
    path: '/cockpit',
    name: 'cockpit',
    component: () => import('@/views/cockpit'),
    alwaysShow: false,
    hiden: true,
    meta: {
      title: '驾驶舱',
      icon: 'info-pc'
    }
  },
  {
    path: '/wel',
    component: Layout,
    redirect: '/wel/index',
    children: [
      {
        path: 'index',
        name: 'wel',
        meta: {
          title: '首页',
          i18n: 'dashboard',
          keepAlive: true
        },
        component: () =>
          import(/* webpackChunkName: "views" */ '@/views/wel/index')
      },
      {
        path: 'dashboard',
        name: 'dashboard',
        meta: {
          title: '控制台',
          i18n: 'dashboard',
          menu: false
        },
        component: () =>
          import(/* webpackChunkName: "views" */ '@/views/wel/dashboard')
      }
    ]
  },
  {
    path: '/dict-horizontal',
    component: Layout,
    redirect: '/dict-horizontal/index',
    children: [
      {
        path: 'index',
        name: 'dict-horizontal',
        meta: {
          title: '字典管理',
          i18n: 'dict'
        },
        component: () =>
          import(
            /* webpackChunkName: "views" */ '@/views/util/demo/dict-horizontal'
          )
      }
    ]
  },
  {
    path: '/dict-vertical',
    component: Layout,
    redirect: '/dict-vertical/index',
    children: [
      {
        path: 'index',
        name: 'dict-vertical',
        meta: {
          title: '字典管理',
          i18n: 'dict'
        },
        component: () =>
          import(
            /* webpackChunkName: "views" */ '@/views/util/demo/dict-vertical'
          )
      }
    ]
  },
  {
    path: '/info',
    component: Layout,
    redirect: '/info/index',
    children: [
      {
        path: 'index',
        name: 'userinfo',
        meta: {
          title: '个人信息',
          i18n: 'info'
        },
        component: () =>
          import(
            /* webpackChunkName: "views" */ '@/views/system/userinfo/index'
          )
      }
    ]
  }
  // {
  //   path: '/demo',
  //   component: Layout,
  //   redirect: '/demo/order',
  //   children: [
  //     {
  //       path: 'order',
  //       name: 'order',
  //       meta: {
  //         title: '订单信息',
  //         i18n: 'order'
  //       },
  //       component: () =>
  //         import(/* webpackChunkName: "views" */ '@/views/demo/order')
  //     }
  //   ]
  // },
  // {
  //   path: '/demo',
  //   component: Layout,
  //   redirect: '/demo/product',
  //   children: [
  //     {
  //       path: 'product',
  //       name: '产品信息',
  //       meta: {
  //         title: 'product',
  //         i18n: 'order'
  //       },
  //       component: () =>
  //         import(/* webpackChunkName: "views" */ '@/views/demo/product')
  //     }
  //   ]
  // },
];
