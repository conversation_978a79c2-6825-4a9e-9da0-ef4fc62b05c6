import request from '@/router/axios';
// ======= 业务字典 ========

// ------ 专业分类 -------
// 分页列表
export const getMajorList = (data) => {
  return request({
    url: '/api/dict-major/page',
    method: 'post',
    data
  });
};
// 新增、编辑
export const majorSave = (data) => {
  return request({
    url: '/api/dict-major/save',
    method: 'post',
    data
  });
};
// 启用、停用
export const majorStatus = (data) => {
  return request({
    url: '/api/dict-major/status',
    method: 'post',
    data
  });
};
// 删除
export const majorDel = (data) => {
  return request({
    url: '/api/dict-major/delete',
    method: 'post',
    data
  });
};
// 详情
export const majorDetail = (params) => {
  return request({
    url: '/api/dict-major/detail',
    method: 'get',
    params
  });
};

// ------ 列支渠道、项目分类 -------
// 分页列表
export const getDictCommonList = (data) => {
  return request({
    url: '/api/dict-common/page',
    method: 'post',
    data
  });
};
// 新增、编辑
export const dictCommonSave = (data) => {
  return request({
    url: '/api/dict-common/save',
    method: 'post',
    data
  });
};
// 启用、停用
export const dictCommonStatus = (data) => {
  return request({
    url: '/api/dict-common/status',
    method: 'post',
    data
  });
};
// 删除
export const dictCommonDel = (data) => {
  return request({
    url: '/api/dict-common/delete',
    method: 'post',
    data
  });
};
// 详情
export const dictCommonDetail = (params) => {
  return request({
    url: '/api/dict-common/detail',
    method: 'get',
    params
  });
};

// ------ 项目阶段 -------
// 列表（不分页）
export const getPhaseList = (data) => {
  return request({
    url: '/api/dict-phase/list',
    method: 'post',
    data
  });
};
// 新增、编辑
export const phaseSave = (data) => {
  return request({
    url: '/api/dict-phase/save',
    method: 'post',
    data
  });
};
// 启用、停用
export const phaseStatus = (data) => {
  return request({
    url: '/api/dict-phase/status',
    method: 'post',
    data
  });
};
// 修改必要节点
export const phaseType = (data) => {
  return request({
    url: '/api/dict-phase/type',
    method: 'post',
    data
  });
};
// 删除
export const phaseDel = (data) => {
  return request({
    url: '/api/dict-phase/delete',
    method: 'post',
    data
  });
};
// 详情
export const phaseDetail = (params) => {
  return request({
    url: '/api/dict-phase/detail',
    method: 'get',
    params
  });
};
// 拖拽排序
export const phaseSort = (data) => {
  return request({
    url: '/api/dict-phase/sort',
    method: 'post',
    data
  });
};

// ------ 项目标签 -------
// ******* 标签分类 *******
// 树形列表
export const getLabelTreeList = (params) => {
  return request({
    url: '/api/dict-label-classify/tree/list',
    method: 'get',
    params
  });
};
// 新增、编辑
export const labelClassSave = (data) => {
  return request({
    url: '/api/dict-label-classify/save',
    method: 'post',
    data
  });
};
// 详情
export const labelClassDetail = (params) => {
  return request({
    url: '/api/dict-label-classify/detail',
    method: 'get',
    params
  });
};
// 删除
export const labelClassDel = (data) => {
  return request({
    url: '/api/dict-label-classify/delete',
    method: 'post',
    data
  });
};
// ******** 项目标签 *********
// 分页列表
export const getLabelList = (data) => {
  return request({
    url: '/api/dict-label/page',
    method: 'post',
    data
  });
};
// 新增、编辑
export const labelSave = (data) => {
  return request({
    url: '/api/dict-label/save',
    method: 'post',
    data
  });
};
// 启用、停用
export const labelStatus = (data) => {
  return request({
    url: '/api/dict-label/status',
    method: 'post',
    data
  });
};
// 删除
export const labelDel = (data) => {
  return request({
    url: '/api/dict-label/delete',
    method: 'post',
    data
  });
};
// 详情
export const labelDetail = (params) => {
  return request({
    url: '/api/dict-label/detail',
    method: 'get',
    params
  });
};
// ------ 知识分类 -------
// 列表（不分页）
export const getClassifyList = (data) => {
  return request({
    url: '/api/zbusiness-dict/classify/list',
    method: 'post',
    data
  });
};
// 新增、编辑
export const classifySave = (data) => {
  return request({
    url: '/api/zbusiness-dict/classify/save',
    method: 'post',
    data
  });
};
// 删除
export const classifyDel = (params) => {
  return request({
    url: '/api/zbusiness-dict/classify/deleteById',
    method: 'delete',
    params
  });
};
// 详情
export const classifyDetail = (id) => {
  return request({
    url: `/api/zbusiness-dict/classify/fetchById/${id}`,
    method: 'get'
  });
};
// 启用、停用
export const classifyStatus = (data) => {
  return request({
    url: `/api/zbusiness-dict/classify/status`,
    method: 'put',
    data
  });
};
// 拖拽排序
export const classifySort = (data) => {
  return request({
    url: '/api/zbusiness-dict/classify/sort',
    method: 'post',
    data
  });
};
