/**
 * 全局配置文件
 */
export default {
  title:
    process.env.NODE_ENV === 'production'
      ? '山东能源集团信息技术管控系统'
      : '信息技术管控系统',
  logo: '',
  storeName: 'saber',
  indexTitle: '信息技术管控系统',
  correspondentName: 'saber',
  correspondentSec: 'c2FiZXI6c2FiZXJfc2VjcmV0',
  tenantMode: false, // 是否开启租户模式
  tenantName: '000000',
  captchaMode: true, // 是否开启验证码模式
  captchaType: 'slider', // 验证码类型
  switchMode: false, // 是否开启部门切换模式
  lockPage: '/lock',
  tokenTime: 3000,
  tokenHeader: 'Szyk-Auth',
  imgOssPath: 'https://attila-static.oss-cn-beijing.aliyuncs.com/web',
  imgOssStaticPath: 'https://attila-oss.oss-cn-qingdao.aliyuncs.com/static/web',
  //http的status默认放行列表
  statusWhiteList: [],
  //配置首页不可关闭
  isFirstPage: false,
  fistPage: {
    label: 'wel',
    value: '/wel/index',
    title: '首页',
    params: {},
    query: {},
    props: false,
    meta: {
      title: '首页',
      i18n: 'dashboard'
    },
    group: [],
    close: false
  },
  pvCookieKe: 'SZYK-PV',
  // 向后台发送cookie时间间隔, 单位毫秒
  sendCookieInterval: 2 * 60 * 1000,
  //配置菜单的属性
  menu: {
    iconDefault: 'iconfont icon-caidan',
    props: {
      id: 'id',
      remark: 'remark',
      name: 'componentName',
      label: 'name',
      path: 'path',
      icon: 'source',
      children: 'children',
      componentPath: 'componentPath',
      meta: 'meta',
      isHide: 'isHide',
      activeMenu: 'activeMenu',
      isProps: 'isProps',
      isKeepAlive: 'isKeepAlive'
    }
  },
  // websocket配置
  WS: {
    enable: false, // 是否启用
    lockReconnect: false, // 连接失败不进行重连
    maxReconnect: 5 // 最大重连次数，若连接失败
  },
  // 第三方系统授权地址
  authUrl: 'http://localhost/szyk-auth/oauth/render',
  // 流程设计器地址
  flowDesignUrl: 'http://localhost:9999',
  // 报表设计器地址(cloud端口为8108,boot端口为80)
  reportUrl: 'http://localhost:8108/ureport',
  // 部门组件全部数据根数据
  deptRootSync: {
    id: '0',
    deptName: '顶级',
    deptCategory: 1,
    hasChildren: true,
    parentId: 'ROOT'
  },
  // 部门组件懒加载根数据
  deptRoot: { deptName: '顶级', id: '0', deptCategory: 1, hasChildren: true }
};
