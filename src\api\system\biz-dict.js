import request from '@/router/axios';
// ------ 指标分类 -------
// 列表
export const getPhaseList = (data) => {
  return request({
    url: '/api/evaluate-target-classify/page',
    method: 'post',
    data
  });
};
// 新增、编辑
export const phaseSave = (data) => {
  return request({
    url: '/api/evaluate-target-classify/save',
    method: 'post',
    data
  });
};
// 启用、停用
export const phaseStatus = (data) => {
  return request({
    url: '/api/evaluate-target-classify/status',
    method: 'post',
    data
  });
};
// 是否加分项
export const phaseType = (data) => {
  return request({
    url: '/api/evaluate-target-classify/score',
    method: 'post',
    data
  });
};
// 删除
export const phaseDel = (data) => {
  return request({
    url: '/api/evaluate-target-classify/delete',
    method: 'post',
    data
  });
};
// 详情
export const phaseDetail = (params) => {
  return request({
    url: '/api/evaluate-target-classify/detail',
    method: 'get',
    params
  });
};
// 后评价小组-----------------------------
// 列表
export const getMajorList = (data) => {
  return request({
    url: '/api/evaluate-group/page',
    method: 'post',
    data
  });
};
// 人员列表
export const getPeopleList = (data) => {
  return request({
    url: '/api/zbusiness-person/personalbase/page/list',
    method: 'post',
    data
  });
};
// 新增、编辑 评价
export const estimateSave = (data) => {
  return request({
    url: '/api/evaluate-group/save',
    method: 'post',
    data
  });
};
// 启用、停用
export const majorStatus = (data) => {
  return request({
    url: '/api/evaluate-group/status',
    method: 'post',
    data
  });
};
// 删除
export const majorDel = (data) => {
  return request({
    url: '/api/evaluate-group/delete',
    method: 'post',
    data
  });
};
// 详情
export const getDetail = (params) => {
  return request({
    url: '/api/evaluate-group/detail',
    method: 'get',
    params
  });
};
