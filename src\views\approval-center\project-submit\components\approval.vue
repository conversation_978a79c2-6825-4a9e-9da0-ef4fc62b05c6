<template>
  <Dialog
    v-if="visible"
    :visible="visible"
    :title="title"
    width="800px"
    @closed="hide"
  >
    <el-alert
      :title="form.approval == 1 ? reminder : backReminder"
      v-if="form.approval"
      type="warning"
      class="reminder-wrap"
      show-icon
      :closable="false"
    >
    </el-alert>
    <el-alert
      :title="hint"
      type="warning"
      v-if="stepObj.userType == 1 && form.approval == 1"
      class="reminder-wrap"
      show-icon
      :closable="false"
    >
    </el-alert>
    <el-form
      label-suffix=":"
      ref="form"
      :rules="rules"
      v-loading="loading"
      label-width="150px"
      :model="form"
    >
      <el-form-item label="操作" prop="approval">
        <el-radio-group
          v-model="form.approval"
          class="approval-opinion"
          @change="handleApproval"
        >
          <!-- 选中的数据有退回的且不退回逻辑为0，则不能提交 :disabled="params.backLogic0 && params.hasBack" -->
          <el-radio-button label="1"
            ><i class="el-icon-check"></i> 提交</el-radio-button
          >
          <!-- 不同级别部门发起的不能退回，比如三级部分，四级部门发起的两条数据无法一起退回 -->
          <el-radio-button label="2" :disabled="params.hasDifferent"
            ><i class="el-icon-back"></i> 退回</el-radio-button
          >
        </el-radio-group>
      </el-form-item>
      <el-form-item
        v-if="form.approval === '1' && showAssignPoint"
        label="下一节点办理人"
        prop="step"
      >
        <el-select
          v-model="form.step"
          style="width: 100%"
          @change="handleStep"
          placeholder="请选择"
        >
          <el-option
            v-for="dict in stepObj.users"
            :key="dict.stepUserId"
            :label="dict.stepUserName"
            :value="dict.stepUserId"
          ></el-option>
        </el-select>
      </el-form-item>
      <!-- <el-form-item
        v-if="form.approval === '2' && showAssignPoint"
        label="退回下一节点"
        prop="step"
      >
        <el-select
          v-model="form.step"
          @change="handleStep"
          style="width: 100%"
          placeholder="请选择"
        >
          <el-option
            v-for="dict in stepObj.users"
            :key="dict.flowSort"
            :label="dict.stepName"
            :value="dict.flowSort"
          >
            <span
              >{{ dict.stepName
              }}<span style="color: #8492a6; font-size: 13px"
                >（{{ dict.approveUserName }}）</span
              ></span
            >
          </el-option>
        </el-select>
      </el-form-item> -->
      <el-form-item
        :label="params.isLeaders ? '分管领导意见' : '备注'"
        prop="operateRemark"
      >
        <el-input
          v-model="form.operateRemark"
          type="textarea"
          :maxlength="200"
          placeholder="请输入"
        ></el-input>
      </el-form-item>
    </el-form>
    <div slot="footer">
      <el-button type="primary" plain @click="hide">取消</el-button>
      <el-button type="primary" @click="onSubmit">确定 </el-button>
    </div>
    <ValidDialog
      v-if="validateVisible"
      :dialogTitle="dialogTitle"
      :formVisible="validateVisible"
      :list="batchList"
      :showProjectName="true"
      labelName="提交单位"
      @close="handleDialogClose"
    ></ValidDialog>
  </Dialog>
</template>
<script>
  import Dialog from '@/components/basic-dialog';
  import ValidDialog from '@/components/yk-validate-dialog';
  import {
    getNext,
    getReturnProcess,
    checkReviewComment
  } from '@/api/workflow';

  import { mapGetters } from 'vuex';
  const form = {
    approval: undefined,
    step: undefined,
    operateRemark: undefined
  };
  export default {
    components: { Dialog, ValidDialog },
    props: {
      showReviewBtn: {
        type: Number,
        default: 1
      }
    },
    computed: {
      ...mapGetters(['userInfo'])
    },
    data() {
      return {
        visible: false,
        loading: false,
        showAssignPoint: false,
        title: '办理操作',
        reminder: '待提交X个，本次提交X个，文审通过X个，文审不通过X个。',
        backReminder:
          '本次共退回XX个，其中待文审X个，文审通过X个，文审不通过X个。',
        hint: '下个审批节点为分管领导，分管领导需要在OA审批。',
        // ------------
        params: {}, // 传参
        form: { ...form },
        rules: {
          approval: [
            {
              required: true,
              message: '请选择',
              trigger: ['change']
            }
          ],
          step: [
            {
              required: true,
              message: '请选择',
              trigger: ['change']
            }
          ]
        },
        stepObj: {},
        editSelected: {},
        validateVisible: false,
        // dialog标题
        dialogTitle: '',
        // 批量提报结果
        batchList: []
      };
    },
    methods: {
      handleDialogClose() {
        this.validateVisible = false;
        this.batchList = [];
      },
      handleApproval(val) {
        this.form.step = undefined;
        this.stepObj.userType = 0;
        switch (val) {
          case '1':
            this.getNext();
            break;
          // case '2':
          //   this.getReturnProcess();
          //   break;
        }
      },
      handleStep(val) {
        let obj = this.stepObj.users.find(
          (item) => (item.stepUserId || item.flowSort) === val
        );
        this.editSelected = obj;
      },
      // 获取下一审核节点
      async getNext() {
        try {
          this.loading = true;
          let {
            data: { data }
          } = await getNext({ ...this.params });
          this.stepObj = data || {};
          let users = this.stepObj.users || [];
          if (users.length) {
            this.showAssignPoint = true;
            if (users.length === 1) {
              this.form.step = users[0].stepUserId;
              this.editSelected = users[0];
            }
          } else {
            this.showAssignPoint = false;
            this.form.step = undefined;
            this.editSelected = {};
          }
        } catch (error) {
          console.log(error);
        } finally {
          this.loading = false;
        }
      },
      // 获取退回列表
      async getReturnProcess() {
        try {
          this.loading = true;
          let {
            data: { data }
          } = await getReturnProcess({ ...this.params });
          this.stepObj.users = data || [];
          this.showAssignPoint = true;
          let users = this.stepObj.users;
          if (users.length === 1) {
            this.form.step = users[0].flowSort;
            this.editSelected = users[0];
          } else {
            // this.showAssignPoint = false;
            this.form.step = undefined;
            this.editSelected = {};
          }
        } catch (error) {
          console.log(error);
        } finally {
          this.loading = false;
        }
      },
      validForm() {
        let bool = false;
        this.$refs.form.validate((val) => {
          bool = val;
        });
        return bool;
      },
      // 校验是否提交后审查意见
      async checkReviewComment() {
        try {
          this.loading = true;
          let idList = this.params.instanceIds;
          let params = {
            idList
          };
          const {
            data: { data }
          } = await checkReviewComment(params);
          return data || false;
        } catch (e) {
          return false;
        } finally {
          this.loading = false;
        }
      },
      // 提交
      async onSubmit() {
        let bool = this.validForm();
        if (!bool) return;
        // 有审查意见就检查是否添加
        if (this.showReviewBtn && this.form.approval === '1') {
          let checkObj = await this.checkReviewComment();
          let { dataList = [] } = checkObj;
          if (dataList.length) {
            this.batchList = dataList;
            this.dialogTitle = '审查意见验证结果';
            this.validateVisible = true;
            return;
          }
        }

        let params = {};
        //  step,
        let { operateRemark, approval } = this.form;
        let { instanceIds } = this.params;
        if (this.form.approval === '1') {
          await this.confirm(`${this.reminder.slice(0, -1)}，是否确认提交？`);
          let { submitChoose } = this.stepObj;
          let { stepDeptId, stepDeptName, stepUserId, stepUserName } =
            this.editSelected;
          params = {
            instanceIds,
            operateRemark,
            stepDeptId,
            stepDeptName,
            stepUserId,
            stepUserName,
            submitChoose
          };
        } else {
          await this.confirm(
            `${this.backReminder.slice(0, -1)}，是否确认提交？`
          );
          params = {
            // flowSort: step,
            instanceIds,
            operateRemark
          };
        }
        this.$emit('save-success', { ...params, approval });
        this.hide();
      },
      async show(params) {
        this.visible = true;
        this.params = params;
        if (params.isLeaders) {
          // 待提交${params.total || 0}个，
          this.reminder = `本次提交${params.instanceIds.length}个。`;
          this.backReminder = `本次共退回${params.instanceIds.length}个。`;
        } else {
          // 待提交${params.total || 0}个，
          this.reminder = `本次提交${params.instanceIds.length}个，文审通过${params.reviewPass}个，文审不通过${params.reviewNoPass}个。`;
          // 本次共退回XX个，其中待文审X个，文审通过X个，文审不通过X个。
          this.backReminder = `本次共退回${params.instanceIds.length}个，其中待文审${params.reviewPending}个，文审通过${params.reviewPass}个，文审不通过${params.reviewNoPass}个。`;
        }
      },
      hide() {
        this.params = {};
        this.stepObj = {};
        this.editSelected = {};
        this.form = { ...form };
        this.$emit('close');
        this.visible = false;
      }
    }
  };
</script>
<style lang="scss" scoped>
  .reminder-wrap {
    margin-top: -16px;
    margin-bottom: 15px;
  }

  .reminder-wrap:first-child {
    margin-bottom: 25px;
  }

  /deep/ .approval-opinion {
    .el-radio-button:nth-of-type(1) .el-radio-button__inner {
      color: #67c23a;
      background: #f0f9eb;
      border-color: #c2e7b0;
    }

    .el-radio-button:nth-of-type(2) .el-radio-button__inner {
      color: #e6a23c;
      background: #fdf6ec;
      border-color: #f5dab1;
    }

    .el-radio-button:nth-of-type(1).is-active .el-radio-button__inner {
      color: #fff;
      background-color: #67c23a;
      border-color: #67c23a;
      box-shadow: -1px 0 0 0 #67c23a;
    }

    .el-radio-button:nth-of-type(2).is-active .el-radio-button__inner {
      color: #fff;
      background-color: #e6a23c;
      border-color: #e6a23c;
      box-shadow: -1px 0 0 0 #e6a23c;
    }

    .el-radio-button.is-disabled .el-radio-button__inner {
      color: #b9b9b9b8;
      background-color: #d7d7d7;
      border-color: #e5e5e5;
    }
  }
</style>
