<template>
  <el-popover
    ref="popover"
    v-model="visible"
    @show="onShowPopover"
    @hide="onHidePopover"
    trigger="click"
    placement="bottom-start"
    class="bc-region-select"
    :disabled="hDisabled"
  >
    <div
      @mouseenter="poperHovering = true"
      @mouseleave="poperHovering = false"
      class="bc-region-poper"
    >
      <div class="bc-region-container">
        <div class="bc-region-level province">
          <el-form-item>
            <h-radio-group
              v-model="provinceCode"
              @change="onProvinceChange"
              :data-source="provinceList"
              :props="props"
              type="textButton"
            ></h-radio-group>
          </el-form-item>
        </div>
        <div v-if="level > 1" class="bc-region-level city">
          <el-form-item v-if="provinceCode">
            <h-radio-group
              v-model="cityCode"
              @change="onCityChange"
              :data-source="cityList"
              :props="props"
              type="textButton"
            ></h-radio-group>
          </el-form-item>
          <span v-else class="bc-region-level-empty">暂无市级区域信息</span>
        </div>
        <div v-if="level > 2" class="bc-region-level county">
          <el-form-item v-if="cityCode">
            <h-radio-group
              v-model="countyCode"
              @change="onCountyChange"
              :data-source="countyList"
              :props="props"
              type="textButton"
            ></h-radio-group>
          </el-form-item>
          <span v-else class="bc-region-level-empty">暂无区级区域信息</span>
        </div>
      </div>
      <div v-if="checkStrictly" class="bc-region-button">
        <el-button @click="onSubmit" type="primary">确定</el-button>
        <el-button @click="onCancel" type="info">取消</el-button>
      </div>
    </div>
    <el-input
      slot="reference"
      ref="input"
      @focus="onInputFocus"
      @blur="onInputBlur"
      @mouseenter.native="inputHovering = true"
      @mouseleave.native="inputHovering = false"
      :value="regionText"
      :title="regionText"
      readonly
      :placeholder="placeholder"
      :disabled="hDisabled"
      :size="size"
      :class="{ 'is-focus': showStatus }"
      :validate-event="false"
    >
      <slot slot="prefix" name="prefix"></slot>
      <i
        v-if="showClose"
        slot="suffix"
        @click="onClearClick"
        class="icon el-icon-circle-close"
      ></i>
      <i
        v-else
        slot="suffix"
        class="icon el-icon-arrow-down"
        :class="{ 'is-reverse': showStatus }"
      ></i>
    </el-input>
  </el-popover>
</template>

<script>
  import { getDivisionTree } from '@/api/system/division';

  export default {
    name: 'RegionSelect',
    inject: {
      elForm: {
        default: ''
      }
    },
    props: {
      value: {
        type: [String, Array],
        required: true
      },
      placeholder: String,
      disabled: {
        type: Boolean,
        default: false
      },
      showAllLevels: {
        type: Boolean,
        default: true
      },
      emitPath: {
        type: Boolean,
        default: true
      },
      checkStrictly: {
        type: Boolean,
        default: false
      },
      size: String,
      clearable: {
        type: Boolean,
        default: true
      },
      separator: {
        type: String,
        default: '/'
      },
      level: {
        type: Number,
        default: 3
      }
    },
    data() {
      return {
        visible: false,
        showStatus: false,
        provinceCode: '',
        cityCode: '',
        countyCode: '',
        provinceList: [],
        cityList: [],
        countyList: [],
        props: {
          label: 'title',
          value: 'value'
        },
        inputHovering: false,
        poperHovering: false,
        regionText: ''
      };
    },
    computed: {
      hValue: {
        get() {
          return this.value || this.emitPath ? [] : '';
        },
        set(value) {
          this.$emit('input', value);
          this.$emit('change', value);
          if (this.$parent.$options.componentName === 'ElFormItem') {
            this.$parent.$emit('el.form.change', value);
          }
        }
      },
      hDisabled() {
        return this.disabled || (this.elForm || {}).disabled;
      },
      showClose() {
        return (
          this.clearable &&
          !this.hDisabled &&
          this.inputHovering &&
          this.value &&
          this.value.length
        );
      }
    },
    watch: {
      value() {
        this.analyzeValue();
      },
      provinceList() {
        this.analyzeValue();
      },
      provinceCode(val) {
        let list = [];
        if (val) {
          let province = this.provinceList.find((item) => item.value === val);
          if (province) {
            list = province.children || [];
          }
        }
        this.cityList = list;
      },
      cityCode(val) {
        let list = [];
        if (val) {
          let city = this.cityList.find((item) => item.value === val);
          if (city) {
            list = city.children || [];
          }
        }
        this.countyList = list;
      }
    },
    created() {
      this.getProvinceList();
    },
    methods: {
      getProvinceList() {
        getDivisionTree().then((res) => {
          this.provinceList = res.data.data || [];
          // if (this.value && this.value.length) {
          //   this.analyzeValue();
          // }
        });
      },
      analyzeValue() {
        let {
          provinceCode,
          provinceName,
          cityCode,
          cityName,
          countyCode,
          countyName
        } = this.getCurrentRegionInfo();
        this.provinceCode = provinceCode;
        this.cityCode = cityCode;
        this.countyCode = countyCode;

        let text = '';
        if (this.value && this.value.length) {
          text = this.showAllLevels
            ? `${provinceName}${
                cityName ? ` ${this.separator} ${cityName}` : ''
              }${countyName ? ` ${this.separator} ${countyName}` : ''}`
            : countyName;
        }
        this.regionText = text;
      },
      getCurrentRegionInfo() {
        return this.getRegionInfoByValue(this.value);
      },
      getRegionInfoByValue(value) {
        let province = {};
        let city = {};
        let county = {};
        if (this.emitPath && value instanceof Array) {
          let [provinceCode = '', cityCode = '', countyCode = ''] = value;
          province =
            this.provinceList.find((item) => item.value === provinceCode) || {};
          let cityList = province.children || [];
          city = cityList.find((item) => item.value === cityCode) || {};
          let countyList = city.children || [];
          county = countyList.find((item) => item.value === countyCode) || {};
        } else if (!this.emitPath && typeof value === 'string' && value) {
          let regionList = this.findRegionPathByValue(value, this.provinceList);
          [province = {}, city = {}, county = {}] = regionList;
        }
        return {
          provinceCode: province.value || '',
          provinceName: province.title || '',
          cityCode: city.value || '',
          cityName: city.title || '',
          countyCode: county.value || '',
          countyName: county.title || ''
        };
      },
      findRegionPathByValue(value, list = []) {
        let paths = [];
        for (let i = 0; i < list.length; i++) {
          let item = list[i];
          if (item.value === value) {
            paths.push(item);
            break;
          }
          let subPaths = this.findRegionPathByValue(value, item.children);
          if (subPaths.length) {
            paths.push(item, ...subPaths);
          }
        }
        return paths;
      },
      onShowPopover() {
        this.showStatus = true;
        this.$emit('visible-change', true);
      },
      onHidePopover() {
        this.showStatus = false;
        this.analyzeValue();
        this.$emit('visible-change', false);
      },
      onProvinceChange(val) {
        this.cityCode = '';
        this.countyCode = '';
        if (this.level === 1) {
          this.onChange(val);
        }
      },
      onCityChange(val) {
        this.countyCode = '';
        if (this.level === 2) {
          this.onChange(val);
        }
      },
      onCountyChange(val) {
        if (this.checkStrictly) {
          return;
        }
        this.onChange(val);
      },
      onChange(val) {
        if (this.emitPath) {
          this.hValue = [this.provinceCode, this.cityCode, this.countyCode];
        } else {
          this.hValue = val;
        }
        this.poperHovering = false;
        this.visible = false;
      },
      onClearClick() {
        event.stopPropagation();
        this.visible = false;
        this.hValue = this.emitPath ? [] : '';
        this.provinceCode = '';
        this.cityCode = '';
        this.countyCode = '';
        this.$emit('clear');
      },
      onInputFocus(event) {
        if (!this.poperHovering) {
          this.$emit('focus', event);
        } else {
          this.visible = true;
        }
      },
      onInputBlur(event) {
        if (this.poperHovering) {
          this.$refs.input.focus();
        } else {
          this.$emit('blur', event);
          this.visible = false;
        }
      },
      onSubmit() {
        let value = null;
        if (this.emitPath) {
          value = this.trimArray([
            this.provinceCode,
            this.cityCode,
            this.countyCode
          ]);
        } else {
          value = this.countyCode || this.cityCode || this.provinceCode || '';
        }
        this.hValue = value;
        this.poperHovering = false;
        this.visible = false;
      },
      trimArray(array) {
        let index = array.findIndex((item) => !item);
        while (index > -1) {
          array.splice(index, 1);
          index = array.findIndex((item) => !item);
        }
        return array;
      },
      onCancel() {
        this.visible = false;
      }
    }
  };
</script>

<style lang="scss" scoped>
  .bc-region-select {
    position: relative;
    display: inline-block;

    .icon {
      width: 25px;
      font-size: 14px;
      cursor: pointer;

      &.el-icon-arrow-down {
        transform: rotateZ(0);
        transition: transform 0.3s;

        &.is-reverse {
          transform: rotateZ(-180deg);
        }
      }
    }
  }
</style>

<style lang="scss">
  .bc-region-select {
    .el-input {
      width: 100%;

      .el-input__inner {
        text-overflow: ellipsis;
        cursor: pointer;
      }
      outline: none;

      :focus {
        outline: none;
      }
    }
  }

  .bc-region-poper {
    .bc-region-container {
      display: flex;

      .bc-region-level {
        position: relative;
        width: 220px;

        &.province {
          width: 251px;
        }

        & + .bc-region-level {
          margin-left: 10px;
          padding-left: 10px;
          border-left: 1px #e4e7ed solid;
        }

        .el-form-item {
          margin-bottom: 0;

          .el-radio-group {
            vertical-align: middle;

            .el-radio-button__inner {
              padding: 7px;
              border: 0;
              border-radius: 4px;
            }

            .el-radio-button__orig-radio:checked + .el-radio-button__inner {
              box-shadow: unset;
            }
          }
        }

        .bc-region-level-empty {
          position: absolute;
          top: 50%;
          left: 50%;
          color: #90939a;
          font-size: 14px;
          transform: translate(-50%, -50%);
        }
      }
    }

    .bc-region-button {
      margin-top: 10px;
      text-align: center;
    }
  }
</style>
