<template>
  <el-row :gutter="12">
    <el-col :span="leftSpan">
      <basic-container class="tree-padding-wrap" :autoHeight="true">
        <slot name="left"></slot>
      </basic-container>
    </el-col>
    <el-col
      class="right-span"
      :class="{ 'full-width': !showLeft }"
      :span="rightSpan"
    >
      <basic-container :autoHeight="true">
        <slot name="right"></slot>
      </basic-container>
      <el-tooltip :content="showLeft ? '折叠' : '展开'">
        <el-button
          class="toggle-btn"
          :icon="`el-icon-d-arrow-${showLeft ? 'left' : 'right'}`"
          @click="showLeft = !showLeft"
        ></el-button>
      </el-tooltip>
    </el-col>
  </el-row>
</template>

<script>
  export default {
    components: {},
    data() {
      return {
        initLeftSpan: 5,
        showLeft: true
      };
    },
    computed: {
      leftSpan() {
        return this.showLeft ? this.initLeftSpan : 0;
      },
      rightSpan() {
        return 24 - this.leftSpan;
      }
    },
    mounted() {},
    methods: {}
  };
</script>
<style scoped lang="scss"></style>
