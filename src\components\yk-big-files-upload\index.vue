<template>
  <el-upload
    action="/"
    :multiple="multiple"
    :drag="drag"
    :file-list="fileList"
    :headers="headers"
    :limit="limit"
    :disabled="disabled"
    :http-request="handleHttpRequest"
    :on-remove="handleRemoveFile"
    :before-upload="beforeUpload"
  >
    <div v-if="drag">
      <i class="el-icon-upload"></i>
      <div class="el-upload__text">
        将文件拖到此处，或<em>{{ btnTitle }}</em>
      </div>
      <div slot="tip" class="el-upload__tip">
        提示：仅能上传{{ fileFormat }}文件格式，此处支持上传{{
          limit
        }}个文件，若要替换，请先删除已上传的文件。
      </div>
    </div>
    <template v-else style="text-align: left">
      <el-button type="primary" icon="el-icon-upload">
        {{ btnTitle }}</el-button
      >
      <div slot="tip" class="el-upload__tip">
        提示：仅能上传{{ fileFormat }}文件格式，此处支持上传{{
          limit
        }}个文件，若要替换，请先删除已上传的文件。
      </div>
    </template>
  </el-upload>
</template>

<script>
  import { deepClone } from '@/util/util';
  import md5 from '@/util/md5';
  import { taskInfo, initTask, preSignUrl, merge } from '@/api/big-files';
  import Queue from 'promise-queue-plus';
  import axios from 'axios';
  import { Notification } from 'element-ui';

  export default {
    name: 'YkBigFilesUpload',
    data() {
      return {
        // 文件上传分块任务的队列（用于移除文件时，停止该文件的上传队列） key：fileUid value：queue object
        fileUploadChunkQueue: {},
        tempFile: null,
        fileArr: [],
        msgList: []
      };
    },
    model: {
      prop: 'fileList',
      event: 'change'
    },
    props: {
      drag: {
        type: Boolean,
        default: false
      },
      btnTitle: {
        type: String,
        default: '点击上传'
      },
      multiple: {
        type: Boolean,
        default: true
      },
      limit: {
        type: Number,
        default: 10
      },
      disabled: {
        type: Boolean,
        default: false
      },
      headers: {
        type: Object,
        default() {
          return {};
        }
      },
      tipText: {
        type: String,
        default: '提示文案：大文件断点续传'
      },
      // 文件回显列表，编辑回显使用
      fileList: {
        type: Array,
        default() {
          return [];
        }
      },
      // 文件格式
      fileFormat: {
        type: String,
        default: 'rar,zip,jar,7-zip,tar,gzip,7z,avi,mov,wmv,mkv,3gp,rm'
      },
      beforeUploadFn: {
        type: Function,
        default: () => {
          return true;
        }
      }
    },
    watch: {
      msgList: {
        handler(newVal) {
          if (newVal.length) {
            this.msgGo();
          }
        },
        deep: true
      }
    },
    methods: {
      msgGo() {
        const self = this;
        this.$message({
          message: `${self.msgList[0].name}上传文件格式不支持 !`,
          type: 'error',
          onClose() {
            self.msgList.shift();
          }
        });
      },
      /**
       *  上传文件之前的钩子
       */
      beforeUpload(file) {
        let fileExt = file.name.replace(/.+\./, '');
        const formatLimit = this.fileFormat.split(',');
        let isTrueFile = formatLimit.indexOf(fileExt.toLowerCase()) !== -1;
        if (!isTrueFile) {
          this.msgList.push(file);
          return false;
        }
        let propUp = this.beforeUploadFn(file);
        return isTrueFile && propUp;
      },
      /**
       * 获取一个上传任务，没有则初始化一个
       */
      async getTaskInfo(file) {
        let task;
        const identifier = await md5(file);
        const res = await taskInfo(identifier);
        const { code, data, msg } = res.data;
        if (code === 200) {
          task = JSON.stringify(data) === '{}' ? null : data;
          if (JSON.stringify(data) !== '{}') {
            this.tempFile = {
              id: data.taskRecord.id,
              name: data.taskRecord.fileName,
              url: data.taskRecord.objectKey
            };
          }
          if (!task) {
            const initTaskData = {
              identifier,
              fileName: file.name,
              totalSize: file.size,
              chunkSize: 5 * 1024 * 1024
            };
            const res = await initTask(initTaskData);
            const { code, data, msg } = res.data;
            if (code === 200 && data.taskRecord.chunkNum > 0) {
              task = data;
              this.tempFile = {
                id: data.taskRecord.id,
                name: data.taskRecord.fileName,
                url: data.taskRecord.objectKey
              };
            } else {
              Notification.error({
                title: '文件上传错误',
                message: msg
              });
            }
          }
        } else {
          Notification.error({
            title: '文件上传错误！',
            message: msg
          });
        }
        return task;
      },
      /**
       * 上传逻辑处理，如果文件已经上传完成（完成分块合并操作），则不会进入到此方法中
       */
      handleUpload(file, taskRecord, options) {
        let uploadedSize = 0; // 已上传的大小
        const totalSize = file.size || 0; // 文件总大小
        const { existPartList, chunkSize, chunkNum, fileIdentifier } =
          taskRecord;

        const uploadNext = async (partNumber) => {
          const start = new Number(chunkSize) * (partNumber - 1);
          const end = start + new Number(chunkSize);
          const blob = file.slice(start, end);
          const res = await preSignUrl({
            identifier: fileIdentifier,
            partNumber: partNumber
          });
          const { code, data } = res.data;
          if (code === 200 && data) {
            await axios.request({
              url: data,
              method: 'PUT',
              data: blob,
              headers: {
                'Content-Type': 'application/octet-stream'
              }
            });
            return Promise.resolve({
              partNumber: partNumber,
              uploadedSize: blob.size
            });
          }
          return Promise.reject(`分片${partNumber}， 获取上传地址失败`);
        };
        /**
         * 更新上传进度
         * @param increment 为已上传的进度增加的字节量
         */
        const updateProcess = (increment) => {
          increment = new Number(increment);
          const { onProgress } = options;
          let factor = 1000; // 每次增加1000 byte
          let from = 0;
          // 通过循环一点一点的增加进度
          while (from <= increment) {
            from += factor;
            uploadedSize += factor;
            const percent = Math.round(
              (uploadedSize / totalSize) * 100
            ).toFixed(2);
            onProgress({ percent: percent });
          }
        };

        return new Promise((resolve) => {
          const failArr = [];
          const queue = Queue(5, {
            retry: 3, //Number of retries
            retryIsJump: false, //retry now?
            workReject: function (reason) {
              failArr.push(reason);
            },
            queueEnd: function () {
              resolve(failArr);
            }
          });
          this.fileUploadChunkQueue[file.uid] = queue;
          for (let partNumber = 1; partNumber <= chunkNum; partNumber++) {
            const exitPart = (existPartList || []).find(
              (exitPart) => exitPart.partNumber == partNumber
            );
            if (exitPart) {
              // 分片已上传完成，累计到上传完成的总额中
              updateProcess(exitPart.size);
            } else {
              queue.push(() =>
                uploadNext(partNumber).then((res) => {
                  // 单片文件上传完成再更新上传进度
                  updateProcess(res.uploadedSize);
                })
              );
            }
          }
          if (queue.getLength() == 0) {
            // 所有分片都上传完，但未合并，直接return出去，进行合并操作
            resolve(failArr);
            return;
          }
          queue.start();
        });
      },
      /**
       * el-upload 自定义上传方法入口
       */
      async handleHttpRequest(options) {
        const file = options.file;
        if (!file.size) {
          return Notification.error({
            title: '文件上传错误',
            message: `请检查文件${options.file.name}`,
            duration: 0
          });
        }
        const task = await this.getTaskInfo(file);
        if (task) {
          const { finished, taskRecord } = task;
          const { fileIdentifier: identifier } = taskRecord;
          if (finished) {
            this.emitFiles();
            return {
              id: taskRecord.id,
              name: taskRecord.fileName,
              url: taskRecord.objectKey
            };
          } else {
            const errorList = await this.handleUpload(
              file,
              taskRecord,
              options
            );
            if (errorList.length > 0) {
              options.onError();
              Notification.error({
                title: '文件上传错误',
                message: `${options.file.name}部分分片上传失败，请尝试重新上传文件`,
                duration: 0
              });
              return;
            }
            const res = await merge(identifier);
            const { code, msg } = res.data;
            if (code === 200) {
              this.emitFiles();
              return {
                id: taskRecord.id,
                name: taskRecord.fileName,
                url: taskRecord.objectKey
              };
            } else {
              Notification({
                title: '文件上传错误',
                message: msg,
                duration: 0
              });
            }
          }
        } else {
          Notification.error({
            title: '文件上传错误',
            message: `获取${options.file.name}上传任务失败`,
            duration: 0
          });
        }
      },
      /**
       * 移除文件列表中的文件
       * 如果文件存在上传队列任务对象，则停止该队列的任务
       */
      handleRemoveFile(uploadFile) {
        const queueObject = this.fileUploadChunkQueue[uploadFile.uid];
        if (queueObject) {
          queueObject.stop();
          this.fileUploadChunkQueue[undefined];
        }
        const id = uploadFile.id;
        const index = this.fileArr.findIndex((item) => {
          return item.id === id;
        });
        if (index !== -1) {
          this.fileArr.splice(index, 1);
          this.$emit('change', this.fileArr);
        }
      },
      // 触发emit
      emitFiles() {
        this.fileArr.push(this.tempFile);
        this.tempFile = null;
        const fileArr = deepClone(this.fileArr);
        const _arr = this.uniqueFunc(fileArr, 'id');
        this.fileArr = _arr;
        this.$emit('change', this.fileArr);
      },
      // 去重
      uniqueFunc(arr, key) {
        const res = new Map();
        return arr.filter(
          (item) => !res.has(item[key]) && res.set(item[key], 1)
        );
      }
    }
  };
</script>

<style lang="scss" scoped>
  .text {
    color: #929292;
    font-size: 12px;
    letter-spacing: 0.5px;
  }
</style>
