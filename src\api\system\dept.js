import request from '@/router/axios';

export const getOnlyList = (params) => {
  return request({
    url: '/api/szyk-system/dept/list',
    method: 'get',
    params: {
      ...params
    }
  });
};
export const lazyTreeByParent = (params) => {
  return request({
    url: '/api/szyk-system/szykdeptmdm/lazyTreeByParent',
    method: 'get',
    params: {
      ...params
    }
  });
};

export const getList = (current, size, params) => {
  return request({
    url: '/api/szyk-system/dept/list',
    method: 'get',
    params: {
      ...params,
      current,
      size
    }
  });
};

export const getLazyList = (parentId, params) => {
  return request({
    url: '/api/szyk-system/dept/lazy-list',
    method: 'get',
    params: {
      ...params,
      parentId
    }
  });
};

export const remove = (ids) => {
  return request({
    url: '/api/szyk-system/dept/check-remove',
    method: 'post',
    params: {
      ids
    }
  });
};
export const batchImportFromMdm = (data) => {
  return request({
    url: '/api/szyk-system/dept/batchImportFromMdm',
    method: 'post',
    data
  });
};

export const add = (row) => {
  return request({
    url: '/api/szyk-system/dept/submit',
    method: 'post',
    data: row
  });
};

export const update = (row) => {
  return request({
    url: '/api/szyk-system/dept/submit',
    method: 'post',
    data: row
  });
};

export const getDept = (id) => {
  return request({
    url: '/api/szyk-system/dept/detail',
    method: 'get',
    params: {
      id
    }
  });
};
// 用户管理-懒加载-有部门
export const getLazyDeptTree_2 = (params) => {
  return request({
    url: '/api/szyk-system/dept/lazy-tree',
    method: 'get',
    params
  });
};
// 用户管理-懒加载
export const getLazyDeptTree_3 = (params) => {
  return request({
    url: '/api/szyk-system/dept/unit/lazy-tree',
    method: 'get',
    params
  });
};
// 懒加载
export const getLazyDeptTree = (params) => {
  return request({
    url: `/api/szyk-system/dept/unit/lazy-tree?parentId=${params}`,
    method: 'get'
  });
};

// 查询当前组织及下级单位
export const getChildLazyDeptTree = (params) => {
  return request({
    url: `/api/szyk-system/dept/current/child/lazy-tree?parentId=${params}`,
    method: 'get'
  });
};
// 懒加载 搜索结果
export const getLazySearch = (params) => {
  return request({
    url: '/api/szyk-system/dept/list',
    method: 'get',
    params
  });
};
// 完整
export const getDeptTree = (tenantId) => {
  return request({
    url: '/api/szyk-system/dept/tree',
    method: 'get',
    params: {
      tenantId
    }
  });
};

export const getDeptLazyTree = (parentId, onlyUnit = false) => {
  return request({
    url: '/api/szyk-system/dept/lazy-tree',
    method: 'get',
    params: {
      parentId,
      onlyUnit
    }
  });
};
export const deptTitleTree = (params) => {
  return request({
    url: '/api/szyk-system/dept/deptTitleTree',
    method: 'get',
    params
  });
};
/**
 * 切换企业
 * @param {String} deptId 企业ID
 */
export const setUserCompany = (deptId) =>
  request({
    url: '/api/attila-system/dept/company/switch',
    method: 'post',
    params: { deptId }
  });
// 职务
export const positionPerson = () => {
  return request({
    url: '/api/attila-system/company-job/list',
    method: 'get'
  });
};
/**
 * 创建新组织
 */
export const orgCreate = (data) => {
  return request({
    url: '/api/attila-system/dept/org/create',
    method: 'post',
    data
  });
};
/**
 * 企业下组织机构部门树
 */
export const getTreeList = (orgId) => {
  return request({
    url: '/api/system-attila/attila/tree',
    method: 'get',
    params: { orgId }
  });
};
/**
 * 组织机构部门员工树(选择直属上级用)
 */
export const getStaffTreeList = (nodeId, processId, orgId) => {
  return request({
    url: '/api/attila-user/user/all-tree',
    method: 'get',
    params: { nodeId, processId, orgId }
  });
};
/**
 * 获取企业关联企业（仅一层）
 * @param {Object} orgId 参数
 */
export const getLinkDepts = (orgId) =>
  request({
    url: '/api/system-attila/org/linkList',
    method: 'get',
    params: {
      orgId
    }
  });

export const getDeptLazyTreeByParent = (parentId, tenantId) => {
  return request({
    url: '/api/szyk-system/dept/lazy-tree-by-parent',
    method: 'get',
    params: {
      parentId,
      tenantId
    }
  });
};

// 获取所有部门
export const getDeptAll = (tenantId) => {
  return request({
    url: '/api/szyk-system/dept/all',
    method: 'get',
    params: {
      tenantId
    }
  });
};

// 获取部门的所有父级部门id列表(查名字)
export const getDeptNameList = (deptName, tenantId) => {
  return request({
    url: '/api/szyk-system/dept/select-ancestor-id-list',
    method: 'get',
    params: {
      deptName,
      tenantId
    }
  });
};

// 获取部门的所有父级部门id列表(查ids)
export const getDeptIdList = (ids, tenantId) => {
  return request({
    url: '/api/szyk-system/dept/select-ancestor-id-list-by-ids',
    method: 'get',
    params: {
      ids,
      tenantId
    }
  });
};

// // 获取部门下的人员列表
// export const getUserList = (params) => {
//   return request({
//     url: '/api/szyk-user/page',
//     method: 'get',
//     params
//   });
// };

// 获取部门下的人员列表
export const getUserList = (params) => {
  return request({
    url: '/api/szyk-user/select-page',
    method: 'get',
    params
  });
};

// 获取当前单位的下级单位
export const getChildDeptList = (params) => {
  return request({
    url: '/api/szyk-system/dept/current/child',
    method: 'get',
    params
  });
};
// 查询当前单位的所有下级单位
export const allLowUnit = (params) => {
  return request({
    url: '/api/szyk-system/dept/allLowUnit',
    method: 'get',
    params
  });
};

// 搜索当前单位的下级单位，平级显示
export const getChildDeptListLevel = (params) => {
  return request({
    url: '/api/szyk-system/dept/current/child/list',
    method: 'get',
    params
  });
};

// 当前单位和上级单位的部门树形结构
export const getCurrentDeptList = () => {
  return request({
    url: '/api/szyk-system/dept/current/up/dept/tree',
    method: 'get'
  });
};

// 根据部门id获取对应的单位信息
export const getUnitInfoByDeptId = (params) => {
  return request({
    url: '/api/szyk-system/dept/getUnitInfoByDeptId',
    method: 'get',
    params
  });
};

// 启用机构
export const enableDept = (params) => {
  return request({
    url: '/api/szyk-system/dept/check-enable',
    method: 'post',
    params
  });
};
// 停用机构
export const disableDept = (params) => {
  return request({
    url: '/api/szyk-system/dept/check-disable',
    method: 'post',
    params
  });
};

// 获取二三级单位
export const getUnitTwo = (params) => {
  return request({
    url: '/api/szyk-system/dept/levelThreeUnit/lazy-tree',
    method: 'get',
    params
  });
};
// 数据湖-懒加载树形接口
export const lakeLazyTree = (params) => {
  return request({
    url: '/api/szyk-system/szykdeptmdm/lazy-tree',
    method: 'get',
    params
  });
};
// 数据湖-模糊查询接口
export const lakeSearchTree = (params) => {
  return request({
    url: '/api/szyk-system/szykdeptmdm/search-tree',
    method: 'get',
    params
  });
};
// 级联导入MDM部门到dept表
export const cascadeImportToDept = (data) => {
  return request({
    url: '/api/szyk-system/szykdeptmdm/cascadeImportToDept',
    method: 'post',
    data
  });
};
