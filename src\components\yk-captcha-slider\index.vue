<template>
  <div class="slider">
    <div class="content">
      <div class="bottom">
        <div class="refresh-btn" @click="refresh"></div>
      </div>
      <div class="bg-img-div">
        <img :src="data.backgroundImage" alt />
      </div>
      <div :style="sliderImgStyles" class="slider-img-div">
        <img
          :clientWidth="trackData.sliderImageWidth"
          :clientHeight="trackData.sliderImageHeight"
          :src="data.sliderImage"
          alt
        />
      </div>
    </div>
    <div class="slider-move">
      <div class="slider-move-track">拖动滑块完成拼图</div>
      <div
        :style="moveStyles"
        class="slider-move-btn"
        @mousedown="slider_down($event)"
        @touchstart="slider_down($event)"
      ></div>
    </div>
  </div>
</template>

<script>
  import { getCaptchaCode } from '@/api/user';

  export default {
    name: 'slider',
    props: {},
    data() {
      return {
        clientWidth: null,
        clientHeight: null,
        data: {
          backgroundImage: null,
          sliderImage: null,
          id: null
        },
        checkData: {
          id: null,
          track: null
        },
        startX: 0,
        startY: 0,
        end: 206,
        moveX: 0,
        movePercent: 0,
        // 滑动轨迹滑动时间等数据
        trackData: {
          bgImageWidth: 0,
          bgImageHeight: 0,
          sliderImageWidth: 0,
          sliderImageHeight: 0,
          startSlidingTime: null,
          endSlidingTime: null,
          trackList: []
        },
        moveStyles: {
          transform: 'translate(0px, 0px)',
          backgroundPosition: '-5px 11.79625%'
        },
        sliderImgStyles: {
          transform: 'translate(0px, 0px)'
        }
      };
    },
    created() {
      this.gen();
    },
    mounted() {
      const bgElements = document.getElementsByClassName('bg-img-div');
      this.trackData.bgImageWidth = bgElements.item(0).clientWidth;
      this.trackData.bgImageHeight = bgElements.item(0).clientHeight;
    },
    methods: {
      /**
       * 生成滑动验证图片
       */
      gen() {
        this.reset();
        const type = 'SLIDER';
        getCaptchaCode(type).then((res) => {
          this.data = res.data.captcha;
          this.checkData.id = res.data.id;

          this.trackData.startSlidingTime = new Date();
          console.log('init', JSON.stringify(this.trackData));
        });
      },
      /**
       * 验证
       */
      check() {
        this.$emit('goLogin', this.checkData.id, this.trackData);
      },
      /**
       * 刷新
       */
      refresh() {
        this.gen();
      },
      /**
       * 重置
       */
      reset() {
        this.moveStyles.transform = 'translate(0, 0)';
        this.moveStyles.backgroundPosition = '-5px 11.79625%';
        this.sliderImgStyles.transform = 'translate(0, 0)';
        this.startX = 0;
        this.startY = 0;
        this.moveX = 0;
        this.movePercent = 0;
        this.checkData.track = null;
        this.checkData.id = null;
        this.trackData.startSlidingTime = null;
        this.trackData.endSlidingTime = null;
        this.trackData.trackList = [];
      },
      close() {
        this.show = false;
      },

      /*
       * 滑动图片鼠标按下
       */
      slider_down(event) {
        let targetTouches = event.originalEvent
          ? event.originalEvent.targetTouches
          : event.targetTouches;
        let startX = event.pageX;
        let startY = event.pageY;
        if (startX === undefined) {
          startX = Math.round(targetTouches[0].pageX);
          startY = Math.round(targetTouches[0].pageY);
        }
        this.startX = startX;
        this.startY = startY;
        this.moveStyles.backgroundPosition = '-5px 31.0092%';

        const pageX = this.startX;
        const pageY = this.startY;
        const startTime = this.trackData.startSlidingTime;
        const trackArr = this.trackData.trackList;
        trackArr.push({
          x: pageX - startX,
          y: pageY - startY,
          type: 'down',
          t: new Date().getTime() - startTime.getTime()
        });
        console.log('start', startX, startY);
        // pc
        window.addEventListener('mousemove', this.move);
        window.addEventListener('mouseup', this.up);
        // 手机端
        window.addEventListener('touchmove', this.move, false);
        window.addEventListener('touchend', this.up, false);
      },
      /**
       * 移动
       * @param event
       */
      move(event) {
        if (window.TouchEvent && event instanceof TouchEvent) {
          event = event.touches[0];
        }
        let pageX = Math.round(event.pageX);
        let pageY = Math.round(event.pageY);
        const startX = this.startX;
        const startY = this.startY;
        const startTime = this.trackData.startSlidingTime;
        const end = this.end;
        const bgImageWidth = this.trackData.bgImageWidth;
        const trackList = this.trackData.trackList;
        let moveX = pageX - startX;
        const track = {
          x: pageX - startX,
          y: pageY - startY,
          type: 'move',
          t: new Date().getTime() - startTime.getTime()
        };
        trackList.push(track);
        if (moveX < 0) {
          moveX = 0;
        } else if (moveX > end) {
          moveX = end;
        }
        this.moveX = moveX;
        this.movePercent = moveX / bgImageWidth;

        this.moveStyles.transform = 'translate(' + moveX + 'px, 0px)';
        this.sliderImgStyles.transform = 'translate(' + moveX + 'px, 0px)';
        // console.log('move', JSON.stringify(track));
      },
      /**
       * 鼠标松下,进行验证
       * @param event
       */
      up(event) {
        window.removeEventListener('mousemove', this.move);
        window.removeEventListener('mouseup', this.up);
        window.removeEventListener('touchmove', this.move);
        window.removeEventListener('touchend', this.up);
        if (window.TouchEvent && event instanceof TouchEvent) {
          event = event.changedTouches[0];
        }
        this.trackData.endSlidingTime = new Date();
        let pageX = Math.round(event.pageX);
        let pageY = Math.round(event.pageY);
        const startX = this.startX;
        const startY = this.startY;
        const startTime = this.trackData.startSlidingTime;
        const trackList = this.trackData.trackList;

        const track = {
          x: pageX - startX,
          y: pageY - startY,
          type: 'up',
          t: new Date().getTime() - startTime.getTime()
        };
        trackList.push(track);
        // console.log("up", JSON.stringify(trackList));
        this.$emit('move-stop', this.checkData);
        this.check();
      }
    }
  };
</script>

<style scoped>
  .slider {
    z-index: 999;
    box-sizing: border-box;
    width: 278px;
    height: 240px;
    padding: 9px;
    background-color: #fff;
    border-radius: 6px;
    box-shadow: 0 0 11px 0 #999;
  }

  .slider .content {
    position: relative;
    width: 100%;
    height: 159px;
  }

  .bg-img-div,
  .slider-img-div {
    position: absolute;
    width: 100%;
    height: 100%;
    transform: translate(0, 0);
  }

  .bg-img-div img {
    width: 100%;
  }

  .slider-img-div img {
    height: 100%;
  }

  .slider .slider-move {
    position: relative;
    width: 100%;
    height: 60px;
    margin: 11px 0;
  }

  .slider .bottom {
    position: absolute;
    width: 100%;
    height: 19px;
  }

  .refresh-btn,
  .close-btn,
  .slider-move-track,
  .slider-move-btn {
    background: url('/img/bg/captcha.png') no-repeat;
  }

  .refresh-btn,
  .close-btn {
    display: inline-block;
  }

  .slider-move .slider-move-track {
    color: #88949d;
    font-size: 14px;
    line-height: 38px;
    white-space: nowrap;
    text-align: center;
    user-select: none;
  }

  .slider {
    user-select: none;
  }

  .slider-move .slider-move-btn {
    position: absolute;
    top: -12px;
    left: 0;
    width: 66px;
    height: 66px;
    background-position: -5px 11.79625%;
    transform: translate(0, 0);
  }

  .slider-move-btn:hover,
  .close-btn:hover,
  .refresh-btn:hover {
    cursor: pointer;
  }

  .bottom .close-btn {
    width: 20px;
    height: 20px;
    background-position: 0 44.86874%;
  }

  .bottom .refresh-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 2;
    width: 20px;
    height: 20px;
    margin-left: 10px;
    background-position: 0 81.38425%;
  }
</style>
