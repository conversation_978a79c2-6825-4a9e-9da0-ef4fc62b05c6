<template>
  <Dialog
    v-if="visible"
    :visible="visible"
    :title="title"
    width="1100px"
    @closed="hide"
  >
    <el-form
      label-suffix=":"
      ref="queryForm"
      label-width="80px"
      :model="queryForm"
      inline
    >
      <el-form-item label="指标名称">
        <el-input
          class="mb10"
          clearable
          :maxlength="20"
          v-model.trim="queryForm.evaluateTarget"
          @keyup.enter.native.stop.prevent="queryText"
          type="text"
          placeholder="请输入"
        />
      </el-form-item>
      <el-form-item label="评分标准">
        <el-input
          class="mb10"
          clearable
          :maxlength="20"
          v-model.trim="queryForm.scoreMethod"
          @keyup.enter.native.stop.prevent="queryText"
          type="text"
          placeholder="请输入"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="queryText"
          >搜索</el-button
        >
        <el-button icon="el-icon-delete" @click="reset">清空</el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="tableData"
      v-loading="loading"
      border
      stripe
      style="width: 100%"
      class="mt-10"
      row-key="id"
      :header-cell-style="{ backgroundColor: '#fafafa' }"
      @select-all="handleSelectionAll"
      @select="handleSelectionChange"
      ref="multipleTable"
    >
      <el-table-column align="center" type="selection" width="30">
      </el-table-column>
      <!-- <el-table-column type="index" width="60" align="center" label="序列" /> -->
      <el-table-column
        prop="classifyName"
        width="75"
        align="center"
        label="指标分类"
      >
      </el-table-column>
      <el-table-column
        prop="evaluateTarget"
        header-align="center"
        label="评价指标"
      >
      </el-table-column>
      <el-table-column header-align="center" label="评分标准">
        <template slot-scope="{ row }">
          {{ row.scoreMethod || '--' }}
        </template>
      </el-table-column>
      <el-table-column min-width="80" align="center" label="考核扣分">
        <template slot-scope="{ row }">
          {{ row.rwScore || '--' }}
        </template>
      </el-table-column>
    </el-table>
    <yk-pagination
      small
      v-show="total > 0"
      :total="total"
      :page.sync="queryForm.current"
      :limit.sync="queryForm.size"
      @pagination="getList"
    />
    <div slot="footer">
      <el-button type="primary" plain @click="hide">取消</el-button>
      <el-button type="primary" @click="onSubmit">确定 </el-button>
    </div>
  </Dialog>
</template>
<script>
  import Dialog from '@/components/basic-dialog';
  // import { getAllMaterialList } from "@/api/distribution-apply";
  import { getDeduction } from '@/api/assess-manage';
  import { trimAll } from '@/util/util';
  const queryForm = {
    scoreMethod: undefined,
    evaluateTarget: undefined,
    size: 10,
    current: 1
  };
  export default {
    components: { Dialog },
    props: {
      formInfo: {
        type: Object,
        default: () => {}
      }
    },
    data() {
      return {
        visible: false,
        loading: false,
        tableData: [],
        total: 0,
        title: '选择督办事项',
        // ------------
        editSelected: {}, // 已选中编辑过的列表
        queryForm: { ...queryForm },
        selectedList: []
      };
    },
    methods: {
      // 检索
      async queryText() {
        Object.assign(this.queryForm, {
          current: 1,
          size: 10
        });
        await this.request();
      },
      // 请求列表
      async request() {
        this.loading = true;
        let { scoreMethod, evaluateTarget } = this.queryForm;
        const params = {
          scoreMethod: trimAll(scoreMethod),
          evaluateTarget: trimAll(evaluateTarget),
          rwId: this.editSelected.id
        };
        try {
          const {
            data: { data }
          } = await getDeduction(params);
          // const { records = [], total = 0 } = data || {};
          // this.total = total;
          this.tableData = data || [];
          this.resetSelected();
        } catch (e) {
          console.error(e);
        } finally {
          this.loading = false;
        }
      },
      // 分页查询
      getList({ page, limit }) {
        Object.assign(this.queryForm, {
          current: page,
          size: limit
        });
        this.request();
      },
      // 提交
      onSubmit() {
        // this.$message.success('保存成功');
        let length = this.selectedList.length;
        if (!length) return this.$message.warning('请选择督办事项');
        this.$emit('save-success', this.selectedList);
        // this.hide();
      },
      async show(row = {}) {
        this.visible = true;
        this.editSelected = row;
        await this.queryText();
      },
      // 重置table选中显示
      resetSelected() {
        // 赋值
        let arr = [...this.selectedList];
        // 判断
        let ids = arr.map((item) => item.id);
        let selectedRows = this.tableData.filter((item) =>
          ids.includes(item.id)
        );
        if (selectedRows) {
          this.$nextTick(() => {
            selectedRows.forEach((row) => {
              this.$refs['multipleTable'].toggleRowSelection(row, true);
            });
          });
        } else {
          this.$nextTick(() => {
            this.$refs['multipleTable'].clearSelection();
          });
        }
      },
      resetPage() {
        this.queryForm = { ...queryForm };
      },
      reset() {
        this.resetPage();
        this.queryText();
      },
      hide() {
        this.selectedList = [];
        this.editSelected = {};
        this.resetPage();
        this.visible = false;
        this.$emit('hide');
      },
      // 全选
      handleSelectionAll(selection) {
        if (selection && selection.length >= this.tableData.length) {
          let complexArr = [...this.selectedList];
          let selectedIp = complexArr.map((item) => item.id);
          this.tableData.forEach((item) => {
            if (!selectedIp.includes(item.id)) {
              // this.selectedList.push(item);
              // let obj = this.selectedList.find((edit) => edit.id === item.id);
              // if (obj) {
              //   this.selectedList.push(obj);
              // } else {
              this.selectedList.push(item);
              // }
            }
          });
        } else {
          let cancelIpArr = this.tableData.map((item) => item.id);
          this.selectedList = this.selectedList.filter(
            (item) => !cancelIpArr.includes(item.id)
          );
        }
      },
      // select 单选
      handleSelectionChange(selection, row) {
        let selected = this.selectedList.some((item) => item.id === row.id);
        if (!selected) {
          // let obj = this.selectedList.find((item) => item.id === row.id);
          // if (obj) {
          //   this.selectedList.push(obj);
          // } else {
          this.selectedList.push(row);
          // }
        } else {
          let index = this.selectedList.findIndex((item) => item.id === row.id);
          this.selectedList.splice(index, 1);
        }
      }
    }
  };
</script>
