<template>
  <Dialog
    v-if="visible"
    :visible="visible"
    :title="title"
    width="800px"
    @closed="hide"
  >
    <el-form
      label-suffix=":"
      ref="form"
      :rules="rules"
      v-loading="loading"
      label-width="150px"
      :model="form"
    >
      <el-form-item label="项目名称" style="margin-bottom: 0">
        <div class="project-name-wrap">
          <el-tag
            v-for="item in params.rows"
            type="info"
            :key="item.id"
            effect="plain"
          >
            {{ item.description }}
          </el-tag>
        </div>
      </el-form-item>
      <el-form-item label="提交单位" style="margin-bottom: 0">
        <div class="project-name-wrap">
          {{ (params.rows && params.rows[0].submitDeptName) || '--' }}
        </div>
      </el-form-item>
      <el-form-item label="建设单位" style="margin-bottom: 0">
        <div class="project-name-wrap">
          {{ form.constructionUnitName || '--' }}
        </div>
      </el-form-item>
      <el-form-item label="建设内容">
        <div class="project-name-wrap construction-line">
          {{ form.constructionContent || '--' }}
        </div>
      </el-form-item>
      <el-form-item
        v-if="showReviewBtn === 1"
        label="审查结论"
        prop="approveResult"
      >
        <el-select
          v-model="form.approveResult"
          filterable
          placeholder="请选择"
          style="width: 100%"
          clearable
        >
          <el-option
            v-for="item in serviceDicts.type['project_approval_opinion']"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="审查意见" prop="approveRemark">
        <el-input
          v-model.trim="form.approveRemark"
          type="textarea"
          :maxlength="1000"
          placeholder="请输入"
        ></el-input>
      </el-form-item>
    </el-form>
    <div slot="footer">
      <el-button type="primary" plain @click="hide">取消</el-button>
      <el-button type="primary" @click="onSubmit">确定 </el-button>
    </div>
  </Dialog>
</template>
<script>
  import Dialog from '@/components/basic-dialog';
  import { getReviewDetail } from '@/api/workflow';
  import { trimAll } from '@/util/util';

  const form = {
    approveResult: undefined,
    approveRemark: undefined
  };
  export default {
    components: { Dialog },
    serviceDicts: ['project_approval_opinion'],
    // props: {
    //   showReviewBtn: {
    //     type: Number,
    //     default: 0
    //   }
    // },
    data() {
      return {
        visible: false,
        loading: false,
        showAssignPoint: false,
        title: '审查意见操作',
        // ------------
        params: {}, // 传参
        form: { ...form },
        rules: {
          approveResult: [
            {
              required: true,
              message: '请选择',
              trigger: ['change']
            }
          ],
          approveRemark: [
            { required: true, message: '请输入', trigger: ['blur'] }
          ]
        },
        stepObj: {},
        editSelected: {},
        showReviewBtn: 0
      };
    },
    methods: {
      validForm() {
        let bool = false;
        this.$refs.form.validate((val) => {
          bool = val;
        });
        return bool;
      },
      // 提交
      onSubmit() {
        let bool = this.validForm();
        if (!bool) return;
        let { approveRemark, approveResult } = this.form;
        let { rows } = this.params;
        approveRemark = trimAll(approveRemark);
        let idList = rows.map((item) => item.businessId);
        this.$emit('save-success', {
          corpApproveResult:
            this.showReviewBtn === 1 ? approveResult || '' : undefined,
          corpApproveRemark:
            this.showReviewBtn === 1 ? approveRemark || '' : undefined,
          secApproveRemark:
            this.showReviewBtn === 2 ? approveRemark || '' : undefined,
          idList
        });
        this.hide();
      },
      async show(params) {
        let { show, rows } = params;
        // 0没有1集团2二级
        this.showReviewBtn = rows[0].checkFlag;
        this.rules.approveRemark[0].required =
          this.showReviewBtn == 2 ? true : false;
        this.params = params;
        if (show) {
          this.getDetail();
        }
        this.visible = true;
      },
      async getDetail() {
        try {
          this.loading = true;
          let { businessId } = this.params.rows[0];
          let {
            data: { data }
          } = await getReviewDetail({ businessId });
          this.setForm(data);
        } catch ({ msg }) {
          msg && this.error(msg);
        } finally {
          this.loading = false;
        }
      },
      setForm(obj) {
        // for (const key in this.form) {
        //   this.form[key] = obj[key] || undefined;
        // }
        const resultKey =
          this.showReviewBtn === 1 ? 'corpApproveResult' : 'secApproveResult';
        const remarkKey =
          this.showReviewBtn === 1 ? 'corpApproveRemark' : 'secApproveRemark';
        this.form.approveResult = obj[resultKey] || undefined;
        this.form.approveRemark = obj[remarkKey] || undefined;
        this.form.constructionContent = obj.constructionContent;
        this.form.constructionUnitName = obj.constructionUnitName;
      },
      hide() {
        this.params = {};
        this.stepObj = {};
        this.editSelected = {};
        this.form = { ...form };
        this.$emit('close');
        this.visible = false;
      }
    }
  };
</script>
<style lang="scss" scoped>
  .project-name-wrap {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    max-height: 200px;
    overflow: auto;
  }

  .construction-line {
    margin-top: 6px;
    line-height: 19px;
  }
</style>
