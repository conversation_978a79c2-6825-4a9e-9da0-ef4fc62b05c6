import request from '@/router/axios';

// ======== 驾驶舱 ========
// 任务/督办下发统计
export const getDistributeData = (params) => {
  return request({
    url: `/api/zbusiness-stat/project-task/headTaskIssueStat`,
    method: 'get',
    params
  });
};

// 超时提报任务/督办统计
export const getOverdueData = (params) => {
  return request({
    url: `/api/zbusiness-stat/project-task/overtimeSubmitTaskStat`,
    method: 'get',
    params
  });
};

// 任务退回次数统计
export const getReturnData = (params) => {
  return request({
    url: `/api/zbusiness-stat/project-task/backTaskStat`,
    method: 'get',
    params
  });
};

// 驾驶舱考核排名默认参数
export const getRankOptions = () => {
  return request({
    url: `/api/zbusiness-stat/project-task/rwRankDefaultParam`,
    method: 'post'
  });
};

// 考核排名信息
export const getAssessData = (params) => {
  return request({
    url: `/api/zbusiness-stat/project-task/rwRank`,
    method: 'get',
    params
  });
};

// 项目总数
export const getProjectSum = (params) => {
  return request({
    url: `/api/zbusiness-stat/project-stat/boardProjectPhaseStat`,
    method: 'get',
    params
  });
};

// 投资估算
export const getInvestment = (params) => {
  return request({
    url: `/api/zbusiness-stat/project-stat/projectInvestment`,
    method: 'get',
    params
  });
};

// 项目审查数量
export const getProjectExamine = (params) => {
  return request({
    url: `/api/zbusiness-stat/project-stat/projectExamine`,
    method: 'get',
    params
  });
};

// 项目验收数量
export const getProjectAccept = (params) => {
  return request({
    url: `/api/zbusiness-stat/project-stat/projectAccept`,
    method: 'get',
    params
  });
};

// 项目后评价统计
export const getProjectEvaluate = (params) => {
  return request({
    url: `/api/zbusiness-stat/project-stat/projectEvaluate`,
    method: 'get',
    params
  });
};

// 项目审查数量分布
export const getProjectDistribution = (params) => {
  return request({
    url: `/api/zbusiness-stat/project-stat/companyExamine`,
    method: 'get',
    params
  });
};

// 设备台账&信息系统台账&数据中心机房
export const getOwnerResource = (params) => {
  return request({
    url: `/api/zbusiness-stat/resource-stat/headOwnerResourceStat`,
    method: 'get',
    params
  });
};
