import request from '@/router/axios';
// 根据当前人所在组织查询建设单位的项目
export const getProjectList = (data) => {
  return request({
    url: '/api/szyk-zbusiness/plbase/list/construction/unit',
    method: 'post',
    data
  });
};
// 列表
export const getPage = (data) => {
  return request({
    url: '/api/project_evaluate/create/page',
    method: 'post',
    data
  });
};
// 保存-编辑
export const submitFetch = (data) => {
  return request({
    url: '/api/project_evaluate/create/save',
    method: 'post',
    data
  });
};
// 批量删除
export const batchDelete = (data) => {
  return request({
    url: '/api/project_evaluate/create/delete',
    method: 'post',
    data
  });
};
// 我的创建-完成
export const setComplete = (params) => {
  return request({
    url: `/api/project_evaluate/create/complete`,
    method: 'get',
    params
  });
};
// 根据ID获取数据
export const getDetail = (params) => {
  return request({
    url: `/api/project_evaluate/detail`,
    method: 'get',
    params
  });
};
// 获取指标体系列表
export const getIndicatorList = (params) => {
  return request({
    url: `/api/scheme-target/list`,
    method: 'get',
    params
  });
};
// 获取后评价小组列表
export const getGroupList = (params) => {
  return request({
    url: `/api/evaluate-group/list`,
    method: 'get',
    params
  });
};
// 根据ID获取数据 小组成员
export const getMemberList = (params) => {
  return request({
    url: `/api/evaluate-group/getMember`,
    method: 'get',
    params
  });
};
// 后评价-指派
export const sendMember = (data) => {
  return request({
    url: '/api/project_evaluate/evaluate/sendMember',
    method: 'post',
    data
  });
};
