<template>
  <div>
    <el-form
      ref="form"
      :model="queryParams"
      label-width="85px"
      :inline="true"
      label-suffix="："
    >
      <!-- <el-form-item label="流程名称" prop="flowName">
        <el-input
          v-model="queryParams.flowName"
          placeholder="请输入"
          clearable
          style="width: 100px"
          :maxlength="20"

        />
      </el-form-item> -->
      <el-form-item label="项目名称" prop="projectName">
        <el-input
          v-model.trim="queryParams.projectName"
          placeholder="请输入"
          clearable
          style="width: 120px"
          :maxlength="20"
        />
      </el-form-item>
      <el-form-item label="申报年份" prop="year">
        <el-date-picker
          v-model="queryParams.year"
          style="width: 111px"
          type="year"
          value-format="yyyy"
          placeholder="请选择"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="批次" prop="batchName">
        <el-input
          v-model.trim="queryParams.batchName"
          placeholder="请输入"
          style="width: 110px"
          clearable
          :maxlength="20"
        />
      </el-form-item>
      <el-form-item
        v-show="activeName === '0'"
        label="下一节点"
        prop="nextStepName"
      >
        <el-select
          v-model="queryParams.nextStepName"
          filterable
          placeholder="请选择"
          style="width: 130px"
          clearable
        >
          <el-option
            v-for="dict in nextStepList"
            :key="dict"
            :label="dict"
            :value="dict"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        :label="activeName === '2' ? '发起时间' : '提交时间'"
        prop="applyTime"
      >
        <el-date-picker
          v-model="queryParams.applyTime"
          type="daterange"
          style="width: 220px"
          align="right"
          range-separator="至"
          value-format="yyyy-MM-dd HH:mm:ss"
          :default-time="['00:00:00', '23:59:59']"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item :label="'提交人'" prop="applyUserName">
        <el-input
          v-model.trim="queryParams.applyUserName"
          placeholder="请输入"
          clearable
          style="width: 100px"
          :maxlength="20"
        />
      </el-form-item>
      <el-form-item label="提交单位" prop="deptId">
        <InputTree
          v-model="queryParams.deptId"
          lazy
          clearable
          :form="queryParams"
          :dic="deptData"
          style="width: 120px"
          :showSearch="false"
          :props="{
            label: 'title',
            value: 'id',
            isLeaf: 'isLeaf',
            formLabel: 'deptName',
            formValue: 'deptId'
          }"
          :load="lazyLoad"
          :lazyLoading="lazyLoading"
          @search="lazySearch"
        ></InputTree>
      </el-form-item>
      <el-form-item label="当前办理状态" label-width="106px" prop="curStatus">
        <el-select
          v-model="queryParams.curStatus"
          filterable
          placeholder="请选择"
          style="width: 90px"
          clearable
        >
          <el-option
            v-for="dict in labelList"
            :key="dict.dictKey"
            :label="dict.dictValue"
            :value="dict.dictKey"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-if="activeName == '0'" label="文审状态" prop="docReview">
        <el-select
          v-model="queryParams.docReview"
          filterable
          placeholder="请选择"
          style="width: 110px"
          clearable
        >
          <el-option
            v-for="dict in serviceDicts.type['doc_review']"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-delete" @click="resetQuery">清空</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
  import { getFlowLazyTree, getnextStepList } from '@/api/workflow';
  import { getStore } from '@/util/store';
  import { InputTree } from '@/components/yk-select-tree';
  import { getDictList } from '@/api/common.js';

  const query = {
    flowName: '',
    batchName: '',
    year: '',
    projectName: '',
    nextStepName: '',
    deptId: '',
    deptName: '',
    docReview: '',
    curStatus: '',
    applyUserName: '',
    applyTime: []
  };
  export default {
    name: 'ProjectSubmitSearch',
    serviceDicts: ['doc_review'],
    components: {
      InputTree
    },
    props: {
      activeName: {
        type: String,
        default: ''
      }
    },
    data() {
      return {
        queryParams: { ...query },
        unfold: false,
        loading: false,
        labelList: [],
        deptData: [],
        nextStepList: [],
        node: null,
        resolveFunc: null,
        lazyLoading: false
      };
    },
    mounted() {
      this.getLabel();
      this.getnextStepList();
    },
    computed: {
      currentBusinessScope() {
        let org = getStore({ name: 'current-organization' });
        return org || {};
      }
    },
    methods: {
      async getnextStepList() {
        try {
          const {
            data: { data }
          } = await getnextStepList({ businessType: '1' }); // dictStatus 0 启用 1 停用
          this.nextStepList = data || [];
        } catch (e) {
          console.error(e);
        }
      },
      async getLabel() {
        try {
          const {
            data: { data }
          } = await getDictList({ code: 'review_status' }); // dictStatus 0 启用 1 停用
          this.labelList = data || [];
        } catch (e) {
          console.error(e);
        }
      },
      async lazySearch(title) {
        if (!title) {
          this.node.childNodes = [];
          this.lazyLoad(this.node, this.resolveFunc);
          return;
        }
        this.lazyLoad(this.node, this.resolveFunc, title);
      },
      async lazyLoad(node, resolve, title) {
        const { data } = node;
        const { id } = data || {};
        if (node.level === 0) {
          this.node = node;
          this.resolveFunc = resolve;
        }
        // let parentId = this.currentBusinessScope['id'];
        let parentId = null;
        let params = {
          tenantId: '000000',
          parentId: title ? parentId : id || parentId,
          title: title || undefined
        };
        this.lazyLoading = true;
        try {
          const {
            data: { data: list }
          } = await getFlowLazyTree(params);
          // 通过调用resolve将子节点数据返回，通知组件数据加载完成
          let arr = list || [];
          arr.forEach((item) => (item.isLeaf = !item.hasChildren));
          if (title) {
            this.deptData = arr;
          } else {
            resolve(arr);
          }
        } catch (error) {
          console.log(error);
        } finally {
          this.lazyLoading = false;
        }
      },
      // 重置
      resetQuery() {
        this.$refs['form'].resetFields();
        this.queryParams.deptId = '';
        this.queryParams.deptName = '';
        this.handleQuery(1);
      },
      // 查询
      handleQuery(isResetQuery) {
        let { applyTime } = this.queryParams;
        let applyStartTime = undefined;
        let applyEndTime = undefined;
        if (Array.isArray(applyTime) && applyTime.length) {
          applyStartTime = applyTime[0];
          applyEndTime = applyTime[1];
        }
        let query = {
          ...this.queryParams,
          applyStartTime,
          applyEndTime,
          applyTime: undefined,
          isResetQuery
        };
        this.$emit('search', query);
      }
    }
  };
</script>
<style lang="scss" scoped>
  /deep/ .el-form-item__label {
    padding-right: 8px;
  }
</style>
