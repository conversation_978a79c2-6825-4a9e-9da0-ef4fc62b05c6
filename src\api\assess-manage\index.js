import request from '@/router/axios';
// 列表
export const getPage = (data) => {
  return request({
    url: '/api/rw-scheme-target/page',
    method: 'post',
    data
  });
};
// 保存-编辑
export const submitFetch = (data) => {
  return request({
    url: '/api/rw-scheme-target/save',
    method: 'post',
    data
  });
};
// 批量删除
export const batchDelete = (data) => {
  return request({
    url: '/api/rw-scheme-target/delete',
    method: 'post',
    data
  });
};
// 根据ID获取数据
export const getDetail = (params) => {
  return request({
    url: `/api/rw-scheme-target/detail`,
    method: 'get',
    params
  });
};
// 查询部门所在下级部门(不包括本级)
export const getLowerDepartment = (params) => {
  return request({
    url: `/api/szyk-system/dept/lower/dept`,
    method: 'get',
    params
  });
};
// 查询体系名称
export const getSystemName = (params) => {
  return request({
    url: `/api/rw-scheme-target/list`,
    method: 'get',
    params
  });
};
// 查询来源单号
export const getSourceNumber = (params) => {
  return request({
    url: `/api/project_base/list/current-lower`,
    method: 'get',
    params
  });
};
// 启用、停用
export const majorStatus = (data) => {
  return request({
    url: '/api/rw-scheme-target/status',
    method: 'post',
    data
  });
};
// 获取调度周期
export const getCyclePage = (data) => {
  return request({
    url: `/api/rw-period/detail/page`,
    method: 'post',
    data
  });
};
// 发起专项考核
export const initiate = (data) => {
  return request({
    url: `/api/rw-scheme-target/initiate`,
    method: 'post',
    data
  });
};
// -----
// 指标列表
export const getIndicator = (params) => {
  return request({
    url: `/api/rw-target-classify/list`,
    method: 'get',
    params
  });
};
// 指标方法列表
export const getMethodList = (data) => {
  return request({
    url: `/api/rw-scheme-target/method/page`,
    method: 'post',
    data
  });
};
// 查询过程考核组织
export const getAssessDetail = (params) => {
  return request({
    url: `/api/project_base/process/getOrg`,
    method: 'get',
    params
  });
};
// 懒加载当前组织下的树形结构
export const getCurrentChild = (params) => {
  return request({
    url: `/api/szyk-system/dept/assessOrg/lazy-tree`,
    method: 'get',
    params
  });
};
// 生成批次
export const createBatchNo = (data) => {
  return request({
    url: `/api/project_base/createBatchNo`,
    method: 'post',
    data
  });
};
// 获取最新批次
export const getNewBatch = (params) => {
  return request({
    url: `/api/project_base/getNewBatch`,
    method: 'get',
    params
  });
};
// 获取 下 一级部门
export const getBelowDepartment = (params) => {
  return request({
    url: `/api/szyk-system/dept/lower/one/dept`,
    method: 'get',
    params
  });
};
// 查询扣分项 ,用于发起督办
export const getDeduction = (params) => {
  return request({
    url: `/api/rw-manage/score/listDeductMethod`,
    method: 'get',
    params
  });
};
// 指标配置 判断指标是否可以删除
export const validEvaluateRemove = (params) => {
  return request({
    url: `/api/rw-scheme-target/validEvaluateRemove`,
    method: 'get',
    params
  });
};
// 指标配置 判断评分方法是否可以删除
export const hasReferenced = (params) => {
  return request({
    url: `/api/rw-scheme-target/validMethodRemove`,
    method: 'get',
    params
  });
};
// 修改业务关联信息
export const updateBusinessInfo = (id, params) => {
  return request({
    url: `/api/zbusiness-task/task/updateBusinessInfo/${id}`,
    method: 'PUT',
    params
  });
};
// 根据考核id查询考核标准
export const listScoreMethodByRwId = (params) => {
  return request({
    url: `/api/rw-manage/score/listScoreMethodByRwId`,
    method: 'get',
    params
  });
};
// 发起考核关联
export const linkData4task = (params) => {
  return request({
    url: `/api/zbusiness-task/task/linkData4task`,
    method: 'get',
    params
  });
};
