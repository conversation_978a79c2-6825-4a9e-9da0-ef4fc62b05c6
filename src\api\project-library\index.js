import request from '@/router/axios';
// 专业分类
export const getClassification = (params) => {
  return request({
    url: '/api/dict-major/list',
    method: 'get',
    params
  });
};
// 项目标签-列表不分页
export const getLabel = (params) => {
  return request({
    url: '/api/dict-label/list',
    method: 'get',
    params
  });
};
// 项目阶段-列表不分页
export const getPhase = (data) => {
  return request({
    url: '/api/dict-phase/list',
    method: 'post',
    data
  });
};
// 列表
export const getPage = (data) => {
  return request({
    url: '/api/szyk-zbusiness/plbase/page',
    method: 'post',
    data
  });
};
// 项目终止
export const termination = (id) => {
  return request({
    url: `/api/szyk-zbusiness/plbase/finish/${id}`,
    method: 'put'
  });
};
// 获取详情
export const getDetail = (id) => {
  return request({
    url: `/api/szyk-zbusiness/plbase/fetchById/${id}`,
    method: 'get'
  });
};
// 项目库导出
export const exportExcel = (data) => {
  return request({
    url: '/api/szyk-zbusiness/plbase/plExport',
    method: 'post',
    responseType: 'blob',
    data
  });
};
// 项目标签分页
export const labelList = (data) => {
  return request({
    url: '/api/dict-label/page',
    method: 'post',
    data
  });
};
// 项目标签分类
export const getLabelClassification = () => {
  return request({
    url: `/api/dict-label-classify/tree/list`,
    method: 'get'
  });
};
// 新增项目标签
export const addLabel = (data) => {
  return request({
    url: '/api/dict-label/save',
    method: 'post',
    data
  });
};
// 为项目新增项目标签
export const addProjectLabel = (data) => {
  return request({
    url: '/api/szyk-zbusiness/plbase/addLabel',
    method: 'post',
    data
  });
};
// 为项目新增项目标签-删除标签
export const delProjectLabel = (data) => {
  return request({
    url: `/api/szyk-zbusiness/plbase/deleteLabel/${data.id}`,
    method: 'delete',
    params: data
  });
};

// 项目库编辑
export const plbaseEdit = (id, data) => {
  return request({
    url: `/api/szyk-zbusiness/plbase/edit/${id}`,
    method: 'put',
    data
  });
};
// 项目调度
export const getDispatchList = (params) => {
  return request({
    url: '/api/zbusiness-project/dispatch/list',
    method: 'get',
    params
  });
};
// 项目调度
export const getFunds = (params) => {
  return request({
    url: '/api/zbusiness-project/fund_plan/detail',
    method: 'get',
    params
  });
};
// 增加项目阶段-显示列表
export const acceptAddlist = (data) => {
  return request({
    url: '/api/project_progress/acceptAddlist',
    method: 'post',
    data
  });
};

// 为项目库新增项目标签
export const saveLabel = (data) => {
  return request({
    url: '/api/project_base/save/label',
    method: 'post',
    data
  });
};
// 为项目库新增项目标签-删除标签
export const delLabel = (data) => {
  return request({
    url: `/api/project_base/delete/label`,
    method: 'get',
    params: data
  });
};
