<template>
  <div>
    <el-form ref="form" :model="queryParams" :inline="true" label-suffix="：">
      <!-- <el-form-item label="体系编号" prop="schemeNo">
        <el-input
          v-model="queryParams.schemeNo"
          placeholder="请输入"
          clearable
          :maxlength="20"
          
        />
      </el-form-item> -->
      <el-form-item label="体系名称" prop="schemeName">
        <el-input
          v-model="queryParams.schemeName"
          placeholder="请输入"
          clearable
          :maxlength="20"
        />
      </el-form-item>
      <el-form-item label="发起组织" prop="initiateOrgName">
        <el-input
          v-model="queryParams.initiateOrgName"
          placeholder="请输入"
          clearable
          :maxlength="20"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-delete" @click="resetQuery">清空</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
  const query = {
    schemeNo: '',
    schemeName: '',
    initiateOrgName: ''
  };
  export default {
    data() {
      return {
        queryParams: { ...query },
        unfold: false,
        loading: false
      };
    },
    mounted() {
      this.handleQuery();
    },
    methods: {
      // 重置
      resetQuery() {
        this.$refs['form'].resetFields();
        this.handleQuery(1);
      },
      // 查询
      handleQuery(isResetQuery) {
        let query = {
          ...this.queryParams,
          isResetQuery
        };
        this.$emit('search', query);
      }
    }
  };
</script>
<style lang="scss"></style>
